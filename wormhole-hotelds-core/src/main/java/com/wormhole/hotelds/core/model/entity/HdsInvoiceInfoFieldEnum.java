package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票信息表 字段枚举
 */
public enum HdsInvoiceInfoFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 门店编码
     */
    hotel_code,

    /**
     * 发票抬头
     */
    invoice_header,

    /**
     * 是否默认：1-是，0-否
     */
    is_default,

    /**
     * 纳税人识别号
     */
    tax_number,

    /**
     * 接收邮箱
     */
    receive_email,

    /**
     * 发票内容
     */
    invoice_content,

    /**
     * 更多信息/备注
     */
    more_info,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 