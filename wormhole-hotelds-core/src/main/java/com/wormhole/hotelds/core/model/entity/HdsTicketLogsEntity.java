package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

@Data
@Table("hds_ticket_logs")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsTicketLogsEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 工单唯一编号
     */
    @Column("ticket_no")
    private String ticketNo;

    /**
     * 操作类型，create（创建）、complete（完成）、expire（过期
     */
    @Column("action_type")
    private String actionType;


    /**
     * 状态: 0-待处理 1-已完成
     */
    @Column("status")
    private Integer status;

    /**
     * 是否过期: 0-未过期 1-已过期
     */
    @Column("expired_flag")
    private Integer expiredFlag;

    @Column("remarks")
    private String remarks;
}
