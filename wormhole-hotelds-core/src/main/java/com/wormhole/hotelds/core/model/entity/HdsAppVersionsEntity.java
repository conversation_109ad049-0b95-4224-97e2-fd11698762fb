package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * App 版本信息表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_app_versions")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsAppVersionsEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Integer id;


    /**
     * 平台：android / ios
     */
    @Column("platform")
    private String platform;


    /**
     * 应用编码（如 hotel_admin / hotel_guest）
     */
    @Column("app_code")
    private String appCode;

    /**
     * 上线时间
     */
    @Column("online_time")
    private LocalDateTime onLineTime;


    /**
     * 版本名（展示用，如 1.0.3）
     */
    @Column("app_version")
    private String appVersion;


    /**
     * 是否强制更新（1是 0否）
     */
    @Column("is_force")
    private Integer isForce;


    /**
     * 更新内容描述
     */
    @Column("update_content")
    private String updateContent;


    /**
     * 下载地址（Android 为 APK 地址，iOS 可填 App Store 链接）
     */
    @Column("download_url")
    private String downloadUrl;


    /**
     * APK 文件 MD5 校验值
     */
    @Column("md5_hash")
    private String md5Hash;


    /**
     * 状态：1 上架 0 下架
     */
    @Column("status")
    private Integer status;


}