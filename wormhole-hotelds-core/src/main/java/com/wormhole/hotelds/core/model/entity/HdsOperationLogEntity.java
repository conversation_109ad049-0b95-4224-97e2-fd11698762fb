package com.wormhole.hotelds.core.model.entity;

import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 系统操作日志表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_operation_log")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsOperationLogEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 日志id
     */
    @Id
    private Long id;

    @Column("hotel_code")
    private String hotelCode;


    /**
     * 员工id
     */
    @Column("employee_id")
    private Long employeeId;


    /**
     * 员工名称
     */
    @Column("employee_name")
    private String employeeName;


    /**
     * 模块编码 设备管理，商家管理，酒店管理，房间管理等等
     */
    @Column("module_code")
    private String moduleCode;


    /**
     * 模块名称
     */
    @Column("module_name")
    private String moduleName;


    /**
     * 操作对象id
     */
    @Column("object_id")
    private Long objectId;


    /**
     * 操作对象类型(hotel:门店,device:设备,contract:合同,merchant:商家等)
     */
    @Column("object_type")
    private String objectType;


    /**
     * 操作对象名称
     */
    @Column("object_name")
    private String objectName;


    /**
     * 操作类型(add:新增,update:修改,delete:删除,query:查询,export:导出,import:导入,login:登录,logout:登出等)
     */
    @Column("opr_type")
    private String oprType;


    /**
     * 调用方法
     */
    @Column("opr_method")
    private String oprMethod;


    /**
     * 操作ip地址
     */
    @Column("opr_ip")
    private String oprIp;


    /**
     * 请求参数
     */
    @Column("opr_params")
    private String oprParams;


    /**
     * 操作内容描述
     */
    @Column("opr_content")
    private String oprContent;


    /**
     * 操作前数据(json格式)
     */
    @Column("opr_before")
    private String oprBefore;


    /**
     * 操作后数据(json格式)
     */
    @Column("opr_after")
    private String oprAfter;


    /**
     * 操作时间
     */
    @Column("opr_time")
    private LocalDateTime oprTime;


    /**
     * 操作结果(success:成功,fail:失败)
     */
    @Column("opr_result")
    private String oprResult;


    /**
     * 错误消息
     */
    @Column("error_msg")
    private String errorMsg;


    /**
     * 执行时长(毫秒)
     */
    @Column("execution_time")
    private Long executionTime;


    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;


}