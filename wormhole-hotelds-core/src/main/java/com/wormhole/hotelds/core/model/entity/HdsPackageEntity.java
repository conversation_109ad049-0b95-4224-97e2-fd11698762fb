package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：付费套餐主表
 */
@Data
@Table("hds_package")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsPackageEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 套餐编码，唯一标识
     */
    @Column("package_code")
    private String packageCode;

    /**
     * 套餐名称，唯一
     */
    @Column("package_name")
    private String packageName;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    @Column("pay_mode")
    private Integer payMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    @Column("discount_mode")
    private Integer discountMode;

    /**
     * 状态：1-启用，0-停用
     */
    @Column("status")
    private Integer status;

    /**
     * 是否推荐：1-是，0-否
     */
    @Column("is_recommend")
    private Integer isRecommend;
} 