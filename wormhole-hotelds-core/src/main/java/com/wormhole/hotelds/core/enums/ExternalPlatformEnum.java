package com.wormhole.hotelds.core.enums;

import lombok.Getter;

@Getter
public enum ExternalPlatformEnum {
    CTRIP("Ctrip", "携程"),
    MEITUAN("<PERSON><PERSON><PERSON>", "美团"),
    DOUYIN("DouYin", "抖音"),
    FLIGGY("Fliggy", "飞猪"),
    BDW("Bdw","百达屋"),

    ;

    private final String code;
    private final String name;

    ExternalPlatformEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
