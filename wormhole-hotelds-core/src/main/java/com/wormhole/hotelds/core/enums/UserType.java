package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;
/**
 * 用户类型枚举
 */
@Getter
@AllArgsConstructor
public enum UserType {
    NORMAL(0, "普通用户"),
    STAFF(1, "员工");

    private final Integer code;
    private final String description;

    private static final Map<Integer, UserType> USER_TYPE_MAP = new HashMap<>();

    static {
        for (UserType type : UserType.values()) {
            USER_TYPE_MAP.put(type.getCode(), type);
        }
    }

    public static UserType getByCode(Integer code) {
        return USER_TYPE_MAP.get(code);
    }
}