package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/5/16 14:43
 * @Description：邀请码表
 */
@Data
@Table("hds_invitation_code")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsInvitationCodeEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */

    @Id
    private Integer id;

    /**
     * 邀请码(5位数字母+数字)
     */
    @Column("invitation_code")
    private String invitationCode;

    /**
     * 邀请码名称
     */
    @Column("name")
    private String name;

    /**
     * 邀请码来源: 1-运营端, 2-门店
     */
    @Column("source")
    private Integer source;

    /**
     * 门店编码(运营端为NULL)
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 状态: 1-启用, 0-关闭
     */
    @Column("status")
    private Integer status;
}
