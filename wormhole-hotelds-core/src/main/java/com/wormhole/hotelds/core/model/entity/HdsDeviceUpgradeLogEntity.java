package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 设备升级记录日志表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_upgrade_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceUpgradeLogEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    private Long id;


    /**
     * 客户端生成的唯一设备号
     */
    @Column("device_id")
    private String deviceId;


    /**
     * 设备序列号
     */
    @Column("device_sn")
    private String deviceSn;


    /**
     * 升级前的版本号
     */
    @Column("pre_version")
    private String preVersion;


    /**
     * 升级后的版本号
     */
    @Column("new_version")
    private String newVersion;


    /**
     * 升级时间
     */
    @Column("upgrade_time")
    private LocalDateTime upgradeTime;


    /**
     * 升级来源（例如：后台推送、手动升级等）
     */
    @Column("upgrade_source")
    private String upgradeSource;


    /**
     * 升级是否成功（1-成功，0-失败）
     */
    @Column("success")
    private Integer success;


    /**
     * 升级失败时的错误信息
     */
    @Column("error_message")
    private String errorMessage;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


}