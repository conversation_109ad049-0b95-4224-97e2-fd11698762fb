package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 设备位置表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_position")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDevicePositionEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;


    /**
     * 位置编号（自定义编码）
     */
    @Column("position_code")
    private String positionCode;


    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 楼栋
     */
    @Column("block")
    private String block;

    /**
     * 区域
     */
    @Column("area")
    private String area;

    /**
     * 区域
     */
    @Column("area_code")
    private String areaCode;

    /**
     * 位置牌号
     */
    @Column("number")
    private String number;


    /**
     * 位置名称
     */
    @Column("position_name")
    private String positionName;


    /**
     * 设备app类型 (front: ai智能座机; room: ai云电话pad版)
     */
    @Column("device_app_type")
    private String deviceAppType;


    /**
     * wifi ssid
     */
    @Column("wifi_ssid")
    private String wifiSsid;


    /**
     * wifi 密码
     */
    @Column("wifi_password")
    private String wifiPassword;

    /**
     * 设备类型排序
     */
    @Column("app_type_sort_order")
    private Integer appTypeSortOrder;

    /**
     * 楼栋区域排序
     */
    @Column("block_area_sort_order")
    private Integer blockAreaSortOrder;

    /**
     * 位置排序
     */
    @Column("position_sort_order")
    private Integer positionSortOrder;


}