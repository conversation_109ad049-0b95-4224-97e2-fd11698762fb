package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 应用系统信息表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_app")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsAppEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 应用系统识别码
     */
    @Column("code")
    private String code;


    /**
     * 应用系统名称
     */
    @Column("name")
    private String name;


}