package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 设备运行态表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 设备型号id
     */
    @Column("model_id")
    private Long modelId;


    /**
     * 客户端生成的唯一设备号
     */
    @Column("device_id")
    private String deviceId;


    /**
     * 设备序列号
     */
    @Column("device_sn")
    private String deviceSn;


    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 业务设备位置编号
     */
    @Column("position_code")
    private String positionCode;


    /**
     * 设备状态 0-待激活 1-在线 2-离线 3-停用
     */
    @Column("device_status")
    private Integer deviceStatus;


    /**
     * 设备app类型 (front: ai智能座机; room: ai云电话pad版)
     */
    @Column("device_app_type")
    private String deviceAppType;


    /**
     * 激活时间
     */
    @Column("active_time")
    private LocalDateTime activeTime;


    /**
     * 上次心跳检测时间
     */
    @Column("last_heartbeat_time")
    private LocalDateTime lastHeartbeatTime;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


    /**
     *
     */
    @Column("rtc_user_id")
    private String rtcUserId;


    /**
     *
     */
    @Column("user_id")
    private String userId;


    /**
     *
     */
    @Column("user_name")
    private String userName;


    /**
     * 设备IMEI号
     */
    @Column("imei")
    private String imei;


}