package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票申请表
 */
@Data
@Table("hds_invoice_apply")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsInvoiceApplyEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 申请编号
     */
    @Column("application_no")
    private String applicationNo;

    /**
     * 发票金额
     */
    @Column("invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 关联账单数量
     */
    @Column("bill_count")
    private Integer billCount;

    /**
     * 申请状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    @Column("application_status")
    private Integer applicationStatus;

    /**
     * 审核备注
     */
    @Column("review_remark")
    private String reviewRemark;

    /**
     * 审核人
     */
    @Column("reviewed_by")
    private String reviewedBy;

    /**
     * 审核人名称
     */
    @Column("reviewed_by_name")
    private String reviewedByName;

    /**
     * 审核时间
     */
    @Column("reviewed_at")
    private LocalDateTime reviewedAt;

    /**
     * 发票号码
     */
    @Column("invoice_no")
    private String invoiceNo;

    /**
     * 发票代码
     */
    @Column("invoice_code")
    private String invoiceCode;

    /**
     * 发票文件URL
     */
    @Column("invoice_url")
    private String invoiceUrl;

    /**
     * 开票时间
     */
    @Column("invoiced_at")
    private LocalDateTime invoicedAt;

    /**
     * 发票抬头
     */
    @Column("invoice_header")
    private String invoiceHeader;

    /**
     * 纳税人识别号
     */
    @Column("tax_number")
    private String taxNumber;

    /**
     * 接收邮箱
     */
    @Column("receive_email")
    private String receiveEmail;

    /**
     * 发票内容
     */
    @Column("invoice_content")
    private String invoiceContent;

    /**
     * 更多信息/备注
     */
    @Column("more_info")
    private String moreInfo;
} 