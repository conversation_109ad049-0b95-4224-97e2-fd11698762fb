package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票明细表 字段枚举
 */
public enum HdsInvoiceDetailFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 发票申请ID
     */
    application_id,

    /**
     * 账单ID
     */
    bill_id,

    /**
     * 订单编号
     */
    order_no,

    /**
     * 套餐名称
     */
    package_name,

    /**
     * 账单金额
     */
    bill_amount,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 