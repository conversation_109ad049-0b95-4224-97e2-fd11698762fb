package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/16 14:46
 * @Description：邀请码表 字段枚举
 */
public enum HdsInvitationCodeFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 邀请码(5位数字母+数字)
     */
    invitation_code,

    /**
     * 邀请码名称
     */
    name,

    /**
     * 邀请码来源: 1-运营端, 2-门店
     */
    source,

    /**
     * 门店编码(运营端为NULL)
     */
    hotel_code,

    /**
     * 状态: 1-启用, 0-关闭
     */
    status,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 更新人ID
     */
    updated_by,

    /**
     * 更新人姓名
     */
    updated_by_name,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status;
}
