package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;

@AllArgsConstructor
@Getter
public enum EmployeeStatusEnum {

    ACTIVE(1, "正常"),
    DISABLED(2, "停用"),
    CANCELED(3, "注销");

    private final int code;
    private final String description;

    public static EmployeeStatusEnum fromCode(int code) {
        return Arrays.stream(values())
                .filter(status -> status.code == code)
                .findFirst().orElse(DISABLED);
    }
}
