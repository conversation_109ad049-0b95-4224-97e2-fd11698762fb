package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.constant.*;
import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.time.*;

@Data
@Table("hds_service_tickets")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsServiceTicketEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 工单唯一编号
     */
    @Column("ticket_no")
    private String ticketNo;

    /**
     * 状态: 0-待处理 1-已完成
     */
    @Column("status")
    private Integer status;

    /**
     * 优先级: 0-普通 1-紧急/投诉
     */
    @Column("priority")
    private Integer priority;
    /**
     * 是否过期: 0-未过期 1-已过期
     */
    @Column("expired_flag")
    private Integer expiredFlag;
    /**
     * 0: 普通用户下单，1:员工下单
     */
    @Column("user_type")
    private Integer userType;

    /**
     * @see ClientType
     */
    @Column("client_type")
    private Integer clientType;
    /**
     * 通话结束时间
     */
    @Column("end_of_call_time")
    private LocalDateTime endOfCallTime;

    @Column("service_type")
    private String serviceType;
    /**
     * 服务类别代码
     */
    @Column("service_category")
    private String serviceCategory;

    /**
     * 服务子类别代码
     */
    @Column("service_subcategory")
    private String serviceSubcategory;

    /**
     * 服务类别名称
     */
    @Column("service_category_name")
    private String serviceCategoryName;

    /**
     * 服务子类别名称
     */
    @Column("service_subcategory_name")
    private String serviceSubcategoryName;

    /**
     * 客户诉求内容
     */
    @Column("guest_request")
    private String guestRequest;

    /**
     * 结构化工具输出
     */
    private String tool; // 需配置JSON转换器

    /**
     * 酒店代码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 位置编码
     */
    @Column("position_code")
    private String positionCode;

    /**
     * RTC语音房间ID
     */
    @Column("rtc_room_id")
    private String rtcRoomId;

    @Column("return_room_id")
    private String returnRoomId;


    /**
     * 实际完成时间
     */
    @Column("completion_time")
    private LocalDateTime completionTime;

    /**
     * 完成人员工号
     */
    @Column("completed_by")
    private String completedBy;

    /**
     * 完成人员姓名
     */
    @Column("completed_by_name")
    private String completedByName;

    /**
     * 设备标识
     */
    @Column("device_id")
    private String deviceId;

    /**
     * 对话标识
     */
    @Column("conversation_id")
    private String conversationId;

    /**
     * 客户端请求标识
     */
    @Column("client_req_id")
    private String clientReqId;
    /**
     * 0 自动创建 1 手动创建
     */
    @Column("create_type")
    private Integer createType;

    @Column("conversation_type")
    private Integer conversationType;

    /**
     * 工单闭环机制
     */
    @Column("closed_loop_level")
    private Integer closedLoopLevel;

    /**
     * 回复异常类型
     */
    @Column("reply_exception_type")
    private Integer replyExceptionType;

    @Column("overdue_flag")
    private Integer overdueFlag;

    @Column("service_keywords")
    private String serviceKeywords;



}