package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 酒店住中AI-RTC通话信息实体类
 */
@Data
@Table("hds_lived_rtc_call_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsLivedRtcCallInfo  extends BaseEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 通话开始时间
     */
    @Column("call_start_time")
    private LocalDateTime callStartTime;

    /**
     * 通话结束时间
     */
    @Column("call_end_time")
    private LocalDateTime callEndTime;

    /**
     * RTC房间ID
     */
    @Column("rtc_room_id")
    private String rtcRoomId;

    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 发起方类型: 1-前台, 2-客房, 3-智能体
     */
    @Column("initiator_type")
    private Integer initiatorType;

    /**
     * 发起方用户ID
     */
    @Column("initiator_user_id")
    private String initiatorUserId;

    /**
     * 接收方类型: 1-前台, 2-客房, 3-智能体
     */
    @Column("receiver_type")
    private Integer receiverType;

    /**
     * 接收方用户ID
     */
    @Column("receiver_user_id")
    private String receiverUserId;

    /**
     * 发起方位置编码
     */
    @Column("position_code")
    private String positionCode;

    /**
     * 通话状态: 1-通话中, 2-已接通, 3-已取消, 4-已拒绝, 5-已结束
     */
    @Column("call_status")
    private Integer callStatus;

    /**
     * 是否有AI参与通话
     * 0-否, 1-是
     */
    @Column("has_ai_participant")
    private Integer hasAiParticipant;


}