package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：门店付费套餐表
 */
@Data
@Table("hds_hotel_package")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelPackageEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 门店套餐编码
     */
    @Column("hotel_package_code")
    private String hotelPackageCode;

    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 主套餐code
     */
    @Column("package_code")
    private String packageCode;

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    @Column("pay_mode")
    private Integer payMode;

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    @Column("discount_mode")
    private Integer discountMode;

    /**
     * 状态：1-启用，0-停用
     */
    @Column("status")
    private Integer status;

    @Column("adjust_price_reason")
    private String adjustPriceReason;
} 