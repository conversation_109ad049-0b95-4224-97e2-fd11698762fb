package com.wormhole.hotelds.core.model.entity;

/**
 * 设备信息表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-11 10:41:45
 */
public enum HdsDeviceInfoFieldEnum {
    /**
     * id
     */
    id,

    /**
     * 设备序列号
     */
    device_sn,

    /**
     * 设备IMEI号
     */
    imei,

    /**
     * 设备型号id
     */
    model_id,

    /**
     * mac地址
     */
    mac_address,

    /**
     * 保修开始时间
     */
    warranty_start,

    /**
     * 保修结束时间
     */
    warranty_end,

    /**
     * 入库时间
     */
    storage_time,

    /**
     * 当前app版本
     */
    app_version,

    /**
     * 设备归属类型 (front: ai智能座机; room: ai云电话pad版)
     */
    device_app_type,

    /**
     * 采购单价（元）
     */
    price,

    /**
     * 规格描述
     */
    spec_desc,

    /**
     * 备注
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态：0-删除，1-有效
     */
    row_status;

}