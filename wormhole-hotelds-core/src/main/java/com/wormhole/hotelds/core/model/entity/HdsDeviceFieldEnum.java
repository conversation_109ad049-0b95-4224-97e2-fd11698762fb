package com.wormhole.hotelds.core.model.entity;

/**
 * 设备运行态表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-11 10:42:11
 */
public enum HdsDeviceFieldEnum {
    /**
     * id
     */
    id,

    /**
     * 设备类型id
     */
    model_id,

    /**
     * 客户端生成的唯一设备号
     */
    device_id,

    /**
     * 设备序列号
     */
    device_sn,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 业务设备位置编号
     */
    position_code,

    /**
     * 设备状态 0-待激活 1-在线 2-离线 3-停用
     */
    device_status,

    /**
     * 设备归属类型 (front: ai智能座机; room: ai云电话pad版)
     */
    device_app_type,

    /**
     * 激活时间
     */
    active_time,

    /**
     * 上次心跳检测时间
     */
    last_heartbeat_time,

    /**
     * 备注
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态：0-删除，1-有效
     */
    row_status,

    /**
     * 
     */
    rtc_user_id,

    /**
     * 
     */
    user_id,

    /**
     * 
     */
    user_name,

    /**
     * 设备IMEI号
     */
    imei;

}