package com.wormhole.hotelds.core.model.entity;

/**
 * 酒店品牌表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-03 16:36:14
 */
public enum HdsBrandFieldEnum {
    /**
     * 
     */
    id,

    /**
     * 品牌编号
     */
    brand_code,

    /**
     * 品牌名称
     */
    brand_name,

    /**
     * 品牌logo图片url
     */
    brand_logo,

    /**
     * 品牌描述简介
     */
    brand_desc,

    /**
     * 公司编码
     */
    merchant_id,

    /**
     * 公司名称
     */
    merchant_name,

    /**
     * 星级类型：0经济型 1舒适型 2品质型 3高档型 4豪华型
     */
    star_type,

    /**
     * 状态 1有效 0无效
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}