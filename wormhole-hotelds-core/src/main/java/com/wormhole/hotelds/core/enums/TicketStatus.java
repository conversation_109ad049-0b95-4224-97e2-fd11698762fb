package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;
@Getter
@AllArgsConstructor
public enum TicketStatus {
    PENDING(0, "待处理"),
    COMPLETED(1, "已完成");

    private final Integer code;
    private final String description;

    private static final Map<Integer, TicketStatus> STATUS_MAP = new HashMap<>();

    static {
        for (TicketStatus status : TicketStatus.values()) {
            STATUS_MAP.put(status.getCode(), status);
        }
    }

    public static TicketStatus getByCode(Integer code) {
        return STATUS_MAP.get(code);
    }
}
