package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/22 13:48
 * @Description：HdsUserLeadFieldEnum
 */
public enum HdsUserLeadFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 线索编码(系统随机生成)
     */
    lead_code,

    /**
     * 邀请人ID(员工表ID)
     */
    inviter_id,

    /**
     * 被邀请人ID(员工表ID)
     */
    invitee_id,

    /**
     * 被邀请人使用的邀请码
     */
    invite_code,

    /**
     * 线索状态：0-初始，1-发展中，2-已完成
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status;
}
