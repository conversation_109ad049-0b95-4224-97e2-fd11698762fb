package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 酒店品牌表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_brand")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsBrandEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 品牌编号
     */
    @Column("brand_code")
    private String brandCode;


    /**
     * 品牌名称
     */
    @Column("brand_name")
    private String brandName;


    /**
     * 品牌logo图片url
     */
    @Column("brand_logo")
    private String brandLogo;


    /**
     * 品牌描述简介
     */
    @Column("brand_desc")
    private String brandDesc;


    /**
     * 公司编码
     */
    @Column("merchant_id")
    private String merchantId;


    /**
     * 公司名称
     */
    @Column("merchant_name")
    private String merchantName;


    /**
     * 星级类型：0经济型 1舒适型 2品质型 3高档型 4豪华型
     */
    @Column("star_type")
    private Integer starType;


    /**
     * 状态 1有效 0无效
     */
    @Column("status")
    private Integer status;


}