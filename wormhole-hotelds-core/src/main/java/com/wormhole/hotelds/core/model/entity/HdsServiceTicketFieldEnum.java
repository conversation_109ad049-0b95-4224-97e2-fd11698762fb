package com.wormhole.hotelds.core.model.entity;

public enum HdsServiceTicketFieldEnum {
    id,
    ticket_no,
    status,
    is_overdue,
    priority,
    expired_flag,
    user_type,
    client_type,
    end_of_call_time,
    service_type,
    service_category,
    service_subcategory,
    service_category_name,
    service_subcategory_name,
    guest_request,
    tool,
    hotel_code,
    position_code,
    rtc_room_id,
    return_room_id,
    completion_time,
    completed_by,
    completed_by_name,
    device_id,
    conversation_id,
    create_type,
    client_req_id,
    created_by,
    created_by_name,
    updated_by,
    updated_by_name,
    created_at,
    updated_at,
    row_status,
    conversation_type,

    closed_loop_level,
    reply_exception_type,
    overdue_flag,
    ;
}