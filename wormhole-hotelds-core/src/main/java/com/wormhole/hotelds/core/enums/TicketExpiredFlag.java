package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;
/**
 * 工单过期标识枚举
 */
@Getter
@AllArgsConstructor
public enum TicketExpiredFlag {
    NOT_EXPIRED(0, "未过期"),
    EXPIRED(1, "已过期");

    private final Integer code;
    private final String description;

    private static final Map<Integer, TicketExpiredFlag> EXPIRED_FLAG_MAP = new HashMap<>();

    static {
        for (TicketExpiredFlag flag : TicketExpiredFlag.values()) {
            EXPIRED_FLAG_MAP.put(flag.getCode(), flag);
        }
    }

    public static TicketExpiredFlag getByCode(Integer code) {
        return EXPIRED_FLAG_MAP.get(code);
    }
}