package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/22 13:50
 * @Description：HdsUserHotelLeadMappingFieldEnum
 */
public enum HdsUserHotelLeadMappingFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 关联用户线索表 hds_user_lead.id
     */
    user_lead_id,

    /**
     * 关联门店线索表 hds_hotel_lead.id
     */
    hotel_lead_id,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status;
}
