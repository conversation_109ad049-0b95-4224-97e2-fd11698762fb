package com.wormhole.hotelds.core.model.entity;

/**
 * 账号工单映射表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-08 
 */
public enum HdsEmployeeTicketMappingFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 账号id
     */
    employee_id,
    /**
     * 账号名称
     */
    employee_name,
    /**
     * 酒店code
     */
    hotel_code,

    /**
     * 账号类型，1总机 2分机
     */
    employee_type,

    /**
     * 接单类型：1全部 2部分
     */
    receive_ticket_type,

    /**
     * 工单类型用于前端展示用,多个用逗号分割：DELIVERY、.....
     */
    ticket_categories,

    /**
     *  暂停接单 0允许接单 1暂停接单
     */
    accept_ticket_status,
    /**
     * 工单视图权限标识 0不分配 1分配
     */
    ticket_assignment_flag,

    /**
     * 位置code
     */
    position_codes,
    /**
     * 区域code
     */
    area_codes,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人ID
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;
}