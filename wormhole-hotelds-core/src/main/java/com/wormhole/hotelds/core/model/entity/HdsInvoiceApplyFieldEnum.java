package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票申请表 字段枚举
 */
public enum HdsInvoiceApplyFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 申请编号
     */
    application_no,

    /**
     * 发票金额
     */
    invoice_amount,

    /**
     * 关联账单数量
     */
    bill_count,

    /**
     * 申请状态：0-待审核，1-审核中，2-已开票，3-已驳回
     */
    application_status,

    /**
     * 审核备注
     */
    review_remark,

    /**
     * 审核人
     */
    reviewed_by,

    /**
     * 审核人名称
     */
    reviewed_by_name,

    /**
     * 审核时间
     */
    reviewed_at,

    /**
     * 发票号码
     */
    invoice_no,

    /**
     * 发票代码
     */
    invoice_code,

    /**
     * 发票文件URL
     */
    invoice_url,

    /**
     * 开票时间
     */
    invoiced_at,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,

    /**
     * 发票抬头
     */
    invoice_header,

    /**
     * 纳税人识别号
     */
    tax_number,

    /**
     * 接收邮箱
     */
    receive_email,

    /**
     * 发票内容
     */
    invoice_content,

    /**
     * 更多信息/备注
     */
    more_info,
} 