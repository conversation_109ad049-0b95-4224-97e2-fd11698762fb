package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：门店个性化定价表 字段枚举
 */
public enum HdsHotelProductPricingFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 门店套餐ID，关联hds_hotel_package.id
     */
    hotel_package_code,

    /**
     * 产品ID，关联hds_product.id
     */
    product_id,

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    period_type,

    /**
     * 自定义门市价，为空则使用标准价格
     */
    custom_market_price,

    /**
     * 自定义优惠价，为空则使用标准价格
     */
    custom_discount_price,

    /**
     * 自定义折扣比例，为空则使用标准比例
     */
    custom_discount_rate,

    /**
     * 最终价格（计算后的实际价格）
     */
    final_price,

    /**
     * 是否使用自定义定价：0-使用标准价格，1-使用自定义价格
     */
    is_custom_pricing,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 