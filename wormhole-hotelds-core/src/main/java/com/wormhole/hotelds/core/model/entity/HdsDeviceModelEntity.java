package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 设备型号表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_model")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceModelEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;


    /**
     * 型号编码
     */
    @Column("model_code")
    private String modelCode;


    /**
     * 型号名称
     */
    @Column("model_name")
    private String modelName;


    /**
     * 设备类别(0:pad,1:音箱,2:电话,3:空调,4:电视,5:门锁,6:照明,7:窗帘,8:传感器,9:其他)
     */
    @Column("category")
    private String category;


    /**
     * 厂商企业名称
     */
    @Column("company_name")
    private String companyName;


    /**
     * 厂商名称
     */
    @Column("manufacturer")
    private String manufacturer;


    /**
     * 厂商联系人
     */
    @Column("manufacturer_contact_person")
    private String manufacturerContactPerson;


    /**
     * 厂商联系方式
     */
    @Column("manufacturer_contact")
    private String manufacturerContact;


    /**
     * 设备图片url
     */
    @Column("image_url")
    private String imageUrl;


    /**
     * 保修期（月）
     */
    @Column("warranty_months")
    private Integer warrantyMonths;


    /**
     * 规格描述
     */
    @Column("spec_desc")
    private String specDesc;


    /**
     * 状态(1:启用,0:禁用)
     */
    @Column("status")
    private Integer status;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


    /**
     * 总库存数量
     */
    @Column("total_inventory")
    private Integer totalInventory;


    /**
     * 可用库存数量
     */
    @Column("inventory")
    private Integer inventory;

    /**
     * 设备app类型 (front: ai智能座机; room: ai云电话pad版)
     */
    @Column("device_app_type")
    private String deviceAppType;

    /**
     * 品牌名称
     */
    @Column("brand_name")
    private String brandName;

    /**
     * 厂商客服/维修电话
     */
    @Column("manufacturer_service_provider")
    private String manufacturerServiceProvider;

    /**
     * 保修情况
     */
    @Column("warranty_info")
    private String warrantyInfo;

}