package com.wormhole.hotelds.core.enums;

import lombok.*;

/**
 * 接单状态枚举
 */
@AllArgsConstructor
@Getter
public enum DeviceLogEventTypeEnum {
    inbound("inbound", "入库"),
    activation("activation", "激活"),
    rebinding("rebinding","换绑"),
    deactivation("deactivation","停用"),
    enabling("enabling","启用"),
    ;
    
    private final String code;
    private final String description;
    
    public static DeviceLogEventTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DeviceLogEventTypeEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}