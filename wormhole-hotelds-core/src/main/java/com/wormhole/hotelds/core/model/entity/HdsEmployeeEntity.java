package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 员工信息表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_employee")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsEmployeeEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 账号
     */
    @Column("username")
    private String username;


    /**
     * 姓名
     */
    @Column("name")
    private String name;


    /**
     * 密码，加密存储
     */
    @Column("password")
    private String password;


    /**
     * 手机
     */
    @Column("mobile")
    private String mobile;


    /**
     * 邮箱
     */
    @Column("email")
    private String email;


    /**
     * 性别 1男 2女
     */
    @Column("gender")
    private Integer gender;


    /**
     * 状态 1正常 2停用 3注销
     */
    @Column("status")
    private Integer status;


    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    @Column("type")
    private Integer type;


}