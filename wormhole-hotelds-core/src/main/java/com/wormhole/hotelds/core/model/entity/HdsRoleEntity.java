package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 角色表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_role")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsRoleEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 角色编码
     */
    @Column("role_code")
    private String roleCode;


    /**
     * 角色名称
     */
    @Column("role_name")
    private String roleName;


    /**
     * 角色描述
     */
    @Column("description")
    private String description;


    /**
     * 状态 1正常 2禁用
     */
    @Column("status")
    private Integer status;


}