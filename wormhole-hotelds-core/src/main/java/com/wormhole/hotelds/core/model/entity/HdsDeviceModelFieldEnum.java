package com.wormhole.hotelds.core.model.entity;

/**
 * 设备型号表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-08 09:38:09
 */
public enum HdsDeviceModelFieldEnum {
    /**
     * id
     */
    id,

    /**
     * 型号编码
     */
    model_code,

    /**
     * 型号名称
     */
    model_name,

    /**
     * 设备类别(0:pad,1:音箱,2:电话,3:空调,4:电视,5:门锁,6:照明,7:窗帘,8:传感器,9:其他)
     */
    category,

    /**
     * 厂商企业名称
     */
    company_name,

    /**
     * 厂商名称
     */
    manufacturer,

    /**
     * 厂商联系人
     */
    manufacturer_contact_person,

    /**
     * 厂商联系方式
     */
    manufacturer_contact,

    /**
     * 设备图片url
     */
    image_url,

    /**
     * 保修期（月）
     */
    warranty_months,

    /**
     * 规格描述
     */
    spec_desc,

    /**
     * 状态(1:启用,0:禁用)
     */
    status,

    /**
     * 备注
     */
    remark,

    /**
     * 总库存数量
     */
    total_inventory,

    /**
     * 可用库存数量
     */
    inventory,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}