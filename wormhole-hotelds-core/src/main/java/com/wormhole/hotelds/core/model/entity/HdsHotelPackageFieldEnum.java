package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：门店付费套餐表 字段枚举
 */
public enum HdsHotelPackageFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 门店套餐编码
     */
    hotel_package_code,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 主套餐code
     */
    package_code,

    /**
     * 状态：1-启用，0-停用
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 