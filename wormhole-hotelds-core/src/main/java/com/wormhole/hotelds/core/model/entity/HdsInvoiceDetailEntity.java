package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票明细表
 */
@Data
@Table("hds_invoice_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsInvoiceDetailEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 发票申请ID
     */
    @Column("application_id")
    private Integer applicationId;

    /**
     * 账单ID
     */
    @Column("bill_id")
    private Integer billId;

    /**
     * 订单编号
     */
    @Column("order_no")
    private String orderNo;

    /**
     * 套餐名称
     */
    @Column("package_name")
    private String packageName;

    /**
     * 账单金额
     */
    @Column("bill_amount")
    private BigDecimal billAmount;
} 