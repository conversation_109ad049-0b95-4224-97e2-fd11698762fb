package com.wormhole.hotelds.core.model.entity;

/**
 * App 版本信息表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-07 08:25:45
 */
public enum HdsAppVersionsFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 平台：android / ios
     */
    platform,

    /**
     * 应用编码（如 hotel_admin / hotel_guest）
     */
    app_code,

    /**
     * 版本名（展示用，如 1.0.3）
     */
    app_version,

    /**
     * 是否强制更新（1是 0否）
     */
    is_force,

    /**
     * 更新内容描述
     */
    update_content,

    /**
     * 下载地址（Android 为 APK 地址，iOS 可填 App Store 链接）
     */
    download_url,

    /**
     * APK 文件 MD5 校验值
     */
    md5_hash,

    /**
     * 状态：1 上架 0 下架
     */
    status,

    /**
     * 创建人 ID
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人 ID
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}