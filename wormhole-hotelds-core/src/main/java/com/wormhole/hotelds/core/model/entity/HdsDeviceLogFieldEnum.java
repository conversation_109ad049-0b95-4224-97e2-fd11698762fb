package com.wormhole.hotelds.core.model.entity;

/**
 * 设备生命周期记录表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-11 10:26:42
 */
public enum HdsDeviceLogFieldEnum {
    /**
     * id
     */
    id,

    /**
     * 客户端生成的唯一设备号
     */
    device_id,

    /**
     * 设备序列号
     */
    device_sn,

    /**
     * 事件类型 (inbound:入库, activation:激活, rebinding:换绑, deactivation:停用, enabling:启用, etc.)
     */
    event_type,

    /**
     * 事件发生时间
     */
    event_time,

    /**
     * 酒店编码 (可选，用于绑定等场景)
     */
    hotel_code,

    /**
     * 业务位置编号 (可选，用于绑定等场景)
     */
    position_code,

    /**
     * 备注
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态：0-删除，1-有效
     */
    row_status;

}