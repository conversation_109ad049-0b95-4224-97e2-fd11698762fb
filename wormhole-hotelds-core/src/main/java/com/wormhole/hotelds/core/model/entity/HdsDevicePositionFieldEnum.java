package com.wormhole.hotelds.core.model.entity;

/**
 * 设备位置表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-09 17:14:09
 */
public enum HdsDevicePositionFieldEnum {
    /**
     * id
     */
    id,

    /**
     * 位置编号（自定义编码）
     */
    position_code,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 楼栋
     */
    block,

    /**
     * 区域
     */
    area,
    /**
     * 区域编号（自定义编码）
     */
    area_code,

    /**
     * 位置牌号
     */
    number,

    /**
     * 位置名称
     */
    position_name,

    /**
     * 设备归属类型 (front: ai智能座机; room: ai云电话pad版)
     */
    device_app_type,

    /**
     * wifi ssid
     */
    wifi_ssid,

    /**
     * wifi 密码
     */
    wifi_password,

    /**
     * 设备app类型 (front: ai智能座机; room: ai云电话pad版)排序
     */
    app_type_sort_order,

    /**
     * 楼栋楼层排序
     */
    block_area_sort_order,

    /**
     * 位置排序
     */
    position_sort_order,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status;

}