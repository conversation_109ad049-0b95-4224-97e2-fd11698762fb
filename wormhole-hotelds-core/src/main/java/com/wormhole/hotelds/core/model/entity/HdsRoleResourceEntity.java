package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 应用系统角色-资源关系
 *
 * <AUTHOR>
 */
@Data
@Table("hds_role_resource")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsRoleResourceEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 角色编码
     */
    @Column("role_code")
    private String roleCode;


    /**
     * 资源ID
     */
    @Column("resource_id")
    private Integer resourceId;


}