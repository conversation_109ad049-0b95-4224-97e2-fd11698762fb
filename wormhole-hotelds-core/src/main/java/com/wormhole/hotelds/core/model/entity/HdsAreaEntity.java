package com.wormhole.hotelds.core.model.entity;

import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 国家区域
 *
 * <AUTHOR>
 */
@Data
@Table("hds_area")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsAreaEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 区域类型(1 省份 2 城市 3 县区)
     */
    @Column("area_type")
    private Integer areaType;


    /**
     * 地区唯一编号
     */
    @Column("area_code")
    private String areaCode;


    /**
     * 父节点
     */
    @Column("parent_code")
    private String parentCode;


    /**
     * 地区名称中文全拼
     */
    @Column("area_pinyin")
    private String areaPinyin;


    /**
     * 拼音首字母
     */
    @Column("first_letter")
    private String firstLetter;


    /**
     * 邮政编码
     */
    @Column("post_code")
    private String postCode;


    /**
     * 是否有效(0 无效 1 有效)
     */
    @Column("valid")
    private Integer valid;


    /**
     * 是否热门城市
     */
    @Column("hot_mark")
    private Integer hotMark;


    /**
     * 区域中文名称
     */
    @Column("area_name")
    private String areaName;


    /**
     * 区域英文名称
     */
    @Column("area_name_en")
    private String areaNameEn;


    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;


}