package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/20 10:59
 * @Description：HdsMerchantLeadFieldEnum
 */
public enum HdsMerchantLeadFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 邀请人ID(员工表ID)
     */
    inviter_id,

    /**
     * 被邀请人ID(员工表ID)
     */
    invitee_id,

    /**
     * 被邀请人使用的邀请码
     */
    invite_code,

    /**
     * 被邀请人所在门店的邀请码
     */
    hotel_invite_code,

    /**
     * 被邀请人所在门店编码
     */
    hotel_code,

    /**
     * 是否已创建门店: 1-是, 0-否
     */
    is_hotel_created,

    /**
     * 是否已登录: 1-是, 0-否
     */
    is_login,

    /**
     * 首次登录时间
     */
    login_at,

    /**
     * 是否使用功能: 1-是, 0-否
     */
    is_feature_used,

    /**
     * 首次使用功能时间
     */
    feature_used_at,

    /**
     * 奖励状态: 0-未奖励, 1-已奖励, 2-不符合奖励条件
     */
    reward_status,

    /**
     * 奖励月数
     */
    reward_months,

    /**
     * 奖励发放时间
     */
    rewarded_at,

    /**
     * 创建人
     */
    created_by,

    /**
     * 更新人
     */
    updated_by,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;
}
