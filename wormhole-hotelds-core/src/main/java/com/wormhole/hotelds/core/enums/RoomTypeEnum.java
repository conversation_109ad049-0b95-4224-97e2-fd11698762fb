package com.wormhole.hotelds.core.enums;

import lombok.*;

/**
 * 房间类型枚举
 */
@AllArgsConstructor
@Getter
public enum RoomTypeEnum {
    /**
     * 客房
     */
    GUESTROOM("guestroom", "客房"),
    /**
     * 大堂
     */
    LOBBY("lobby", "大堂"),
    /**
     * 餐厅
     */
    RESTAURANT("restaurant", "餐厅"),
    /**
     * 会议室
     */
    MEETING_ROOM("meeting_room", "会议室"),
    /**
     * 健身房
     */
    GYM("gym", "健身房"),
    /**
     * 水疗
     */
    SPA("spa", "水疗"),
    /**
     * 办公区
     */
    OFFICE("office", "办公区"),
    /**
     * 仓库
     */
    STORAGE("storage", "仓库");

    private final String code;
    private final String description;




}