package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 设备生命周期记录表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceLogEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;


    /**
     * 客户端生成的唯一设备号
     */
    @Column("device_id")
    private String deviceId;


    /**
     * 设备序列号
     */
    @Column("device_sn")
    private String deviceSn;


    /**
     * 事件类型 (inbound:入库, activation:激活, rebinding:换绑, deactivation:停用, enabling:启用, etc.)
     */
    @Column("event_type")
    private String eventType;


    /**
     * 事件发生时间
     */
    @Column("event_time")
    private LocalDateTime eventTime;


    /**
     * 酒店编码 (可选，用于绑定等场景)
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 业务位置编号 (可选，用于绑定等场景)
     */
    @Column("position_code")
    private String positionCode;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


}