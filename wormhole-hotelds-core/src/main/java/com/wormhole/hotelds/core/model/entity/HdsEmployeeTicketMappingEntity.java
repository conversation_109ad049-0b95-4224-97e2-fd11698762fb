package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * <AUTHOR>
 */
@Data
@Table("hds_employee_ticket_mapping")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsEmployeeTicketMappingEntity extends BaseEntity implements Serializable {
    /**
     * 主键ID
     */
    @Id
    private Long id;
    /**
     * 账号id
     */
    private String employeeId;
    /**
     * 账号名称
     */
    private String employeeName;

    /**
     * 酒店code
     */
    private String hotelCode;
    /**
     * 账号类型，1总机 2分机
     */
    private Integer employeeType;
    /**
     * 工单类型用于前端展示用,多个用逗号分割：DELIVERY、.....
     */
    private String ticketCategories;
    /**
     *  暂停接单 0允许接单 1暂停接单
     */
    private Integer acceptTicketStatus;

    /**
     * 位置code，多个用逗号分割
     */
    private String positionCodes;

    /**
     * 位置code，多个用逗号分割
     */
    private String areaCodes;

    /**
     * 是否有接单分布视图权限
     */
    private Integer ticketAssignmentFlag;

}
