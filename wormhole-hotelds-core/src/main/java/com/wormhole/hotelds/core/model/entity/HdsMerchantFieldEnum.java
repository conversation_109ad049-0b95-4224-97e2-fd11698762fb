package com.wormhole.hotelds.core.model.entity;

/**
 *  字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-03 16:43:27
 */
public enum HdsMerchantFieldEnum {
    /**
     * 主键id
     */
    id,

    /**
     * 公司编码
     */
    merchant_id,

    /**
     * 公司名称
     */
    merchant_name,

    /**
     * 主体名称
     */
    subject_name,

    /**
     * 商户类型(1:酒店商户 2:服务商商户)
     */
    type,

    /**
     * 国家编码
     */
    country_code,

    /**
     * 国家
     */
    country_name,

    /**
     * 省编码
     */
    province_code,

    /**
     * 省
     */
    province_name,

    /**
     * 城市编码
     */
    city_code,

    /**
     * 城市
     */
    city_name,

    /**
     * 区县编码
     */
    district_code,

    /**
     * 区县
     */
    district_name,

    /**
     * 详细地址
     */
    address,

    /**
     * 公司电话
     */
    merchant_phone,

    /**
     * 公司邮箱
     */
    merchant_email,

    /**
     * 联系人
     */
    merchant_contacts,

    /**
     * 公司营业执照
     */
    merchant_license_url,

    /**
     * 开户名称
     */
    account_name,

    /**
     * 开户银行
     */
    account_bank,

    /**
     * 开户支行
     */
    account_sub_bank,

    /**
     * 银行卡号
     */
    account_card_number,

    /**
     * 银行预留手机号
     */
    account_reserve_mobile,

    /**
     * 提现手机号
     */
    account_cash_mobile,

    /**
     * 状态 1有效 0无效
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}