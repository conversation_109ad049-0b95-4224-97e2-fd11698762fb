package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/5/20 19:01
 * @Description：HdsAgentTimeFlowEntity
 */
@Data
@Table("hds_agent_time_flow")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsAgentTimeFlowEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    @Column("type")
    private Integer type;

    /**
     * 门店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 流水类型: 1-初始赠送, 2-邀请奖励, 3-充值, 4-管理员调整, 5-系统调整
     */
    @Column("flow_type")
    private Integer flowType;

    /**
     * 操作类型: 1-增加, 2-减少
     */
    @Column("operation_type")
    private Integer operationType;

    /**
     * 变更月数
     */
    @Column("months_changed")
    private Integer monthsChanged;

    /**
     * OTA延期月数修改前的值
     */
    @Column("ota_extend_months_before")
    private Integer otaExtendMonthsBefore;

    /**
     * OTA延期月数修改后的值
     */
    @Column("ota_extend_months_after")
    private Integer otaExtendMonthsAfter;

    /**
     * 奖励累计月数修改前的值
     */
    @Column("ota_reward_months_before")
    private Integer otaRewardMonthsBefore;

    /**
     * 奖励累计月数修改后的值
     */
    @Column("ota_reward_months_after")
    private Integer otaRewardMonthsAfter;

    /**
     * 使用的邀请码
     */
    @Column("invitation_code")
    private String invitationCode;

    /**
     * 当前到期时间（修改前）
     */
    @Column("expiration_before")
    private LocalDateTime expirationBefore;

    /**
     * 最新到期时间（修改后）
     */
    @Column("expiration_after")
    private LocalDateTime expirationAfter;
}
