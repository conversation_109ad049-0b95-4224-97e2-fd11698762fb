package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 
 *
 * <AUTHOR>
 */
@Data
@Table("hds_merchant")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsMerchantEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    private Integer id;


    /**
     * 公司编码
     */
    @Column("merchant_id")
    private String merchantId;


    /**
     * 公司名称
     */
    @Column("merchant_name")
    private String merchantName;


    /**
     * 主体名称
     */
    @Column("subject_name")
    private String subjectName;


    /**
     * 商户类型(1:酒店商户 2:服务商商户)
     */
    @Column("type")
    private Integer type;


    /**
     * 国家编码
     */
    @Column("country_code")
    private String countryCode;


    /**
     * 国家
     */
    @Column("country_name")
    private String countryName;


    /**
     * 省编码
     */
    @Column("province_code")
    private String provinceCode;


    /**
     * 省
     */
    @Column("province_name")
    private String provinceName;


    /**
     * 城市编码
     */
    @Column("city_code")
    private String cityCode;


    /**
     * 城市
     */
    @Column("city_name")
    private String cityName;


    /**
     * 区县编码
     */
    @Column("district_code")
    private String districtCode;


    /**
     * 区县
     */
    @Column("district_name")
    private String districtName;


    /**
     * 详细地址
     */
    @Column("address")
    private String address;


    /**
     * 公司电话
     */
    @Column("merchant_phone")
    private String merchantPhone;


    /**
     * 公司邮箱
     */
    @Column("merchant_email")
    private String merchantEmail;


    /**
     * 联系人
     */
    @Column("merchant_contacts")
    private String merchantContacts;


    /**
     * 公司营业执照
     */
    @Column("merchant_license_url")
    private String merchantLicenseUrl;


    /**
     * 开户名称
     */
    @Column("account_name")
    private String accountName;


    /**
     * 开户银行
     */
    @Column("account_bank")
    private String accountBank;


    /**
     * 开户支行
     */
    @Column("account_sub_bank")
    private String accountSubBank;


    /**
     * 银行卡号
     */
    @Column("account_card_number")
    private String accountCardNumber;


    /**
     * 银行预留手机号
     */
    @Column("account_reserve_mobile")
    private String accountReserveMobile;


    /**
     * 提现手机号
     */
    @Column("account_cash_mobile")
    private String accountCashMobile;


    /**
     * 状态 1有效 0无效
     */
    @Column("status")
    private Integer status;


}