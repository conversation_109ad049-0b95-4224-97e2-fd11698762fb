package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 会话住客用户表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_chat_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsChatUserEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;


    /**
     * 账号（openid）
     */
    @Column("account")
    private String account;


    /**
     * 手机号
     */
    @Column("mobile")
    private String mobile;


    /**
     * 昵称
     */
    @Column("nick_name")
    private String nickName;


    /**
     * 用户状态 1:正常 2:禁用 3:注销
     */
    @Column("status")
    private Integer status;

    /**
     * 酒店
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 房间号
     */
    @Column("position_code")
    private String positionCode;

}