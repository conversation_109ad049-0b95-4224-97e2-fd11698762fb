package com.wormhole.hotelds.core.model.entity;

/**
 * 员工信息表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-03 16:37:45
 */
public enum HdsEmployeeFieldEnum {
    /**
     * 
     */
    id,

    /**
     * 账号
     */
    username,

    /**
     * 姓名
     */
    name,

    /**
     * 密码，加密存储
     */
    password,

    /**
     * 手机
     */
    mobile,

    /**
     * 邮箱
     */
    email,

    /**
     * 性别 1男 2女
     */
    gender,

    /**
     * 状态 1正常 2停用 3注销
     */
    status,

    /**
     * 员工类型 1-集团 2-服务商 3-酒店
     */
    type,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}