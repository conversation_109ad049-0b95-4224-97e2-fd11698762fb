package com.wormhole.hotelds.core.enums;

import lombok.*;
import org.apache.commons.lang3.*;

import java.util.*;

// 顶层服务类型枚举
@Getter
@AllArgsConstructor
public enum TicketClosedLoopLevel {
    L1(1,"AI闭环","AI识别出工单类型并且在对话中用户确认通过，用户回应正向（如“对/好/是的”）后挂断，用户无进一步请求或情绪波动。适用工单类型为：咨询服务；送、衣、行、餐四类服务中，查询知识库确认酒店无对应服务，用户认可回答时即可AI闭环。",null),
    L2(2,"前台回电（接续）","AI识别需求成功，但知识库无法完全匹配用户需求，该项服务需要人工干预，AI无法闭环。或者客人主动明确找人工（非AI识别异常导致)",1),
    L3(3,"前台回电（客诉预警）","对话过程中出现AI两轮追问后失败或者用户情绪升级的情况",1),
    L4(4,"紧急呼叫人工","用户通过实体设备（如房控、电视、语音面板）点击“紧急求助/SOS”按钮，系统自动发起通话并成功接通",null),
    L5(5,"前台处理","AI识别为常规服务请求类意图（如送物、维修、清洁等），识别内容明确 ，并且查询知识库完全匹配用户需求，用户无异议，还需要最后一步人工操作。适用工单类型为：咨、送、衣、行、餐、醒、洁、修。还需要人工最后处理操作。",0),
    ;

    private final Integer code;
    private final String concept;
    private final String description;
    /**
     * 未完成状态的处理逻辑 (0-直接点击处理 1-回电)
     * 示例值: 0
     */
    private final Integer incompleteProcessType;
    private final static Map<Integer, TicketClosedLoopLevel>  MAP = new HashMap<>();
    static {
        for (TicketClosedLoopLevel value : TicketClosedLoopLevel.values()) {
            MAP.put(value.getCode(), value);
        }
    }

    public static TicketClosedLoopLevel getByCode(Integer code){
        return MAP.get(code);
    }

    public static boolean isTicketNeedCall(Integer code){
        return code != null && StringUtils.equalsAny(code.toString(),L2.code.toString(),L3.code.toString());
    }

    public static boolean isTicketNeedClick(Integer code){
        return code != null && StringUtils.equalsAny(code.toString(),L5.code.toString());
    }

}



