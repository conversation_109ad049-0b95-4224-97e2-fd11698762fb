package com.wormhole.hotelds.core.utils;

import com.google.common.hash.Hashing;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

public class HoteldsApiUtils {
    public static void main(String[] args) {
        System.out.println(generateAreaCode("HC001", "A栋", "1楼"));
        System.out.println(generateAreaCode("HC001", "A", "1"));
        System.out.println(generateAreaCode("HC001", null, "1"));
        System.out.println(generateAreaCode("HC001", "1", null));
    }

    /**
     * 生成区域编码。规则=hotelCode(区分不同酒店)+block ,area (区分同个酒店不同区域)
     */
    public static String generateAreaCode(String hotelCode,String block,String area){
        String originCode = String.format("%s%s%s", hotelCode, StringUtils.isBlank(block)?"-" : block , StringUtils.isBlank(area) ? "-":area);
        return generateShortCodeByHash(originCode);
    }

    private static String generateShortCodeByHash(String originCode){
        int hash = Hashing.murmur3_32_fixed().hashString(originCode, StandardCharsets.UTF_8).asInt();
        return toBase62(Math.abs(hash));
    }

    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    private static String toBase62(int num) {
        StringBuilder sb = new StringBuilder();
        do {
            int i = num % 62;
            sb.append(BASE62.charAt(i));
            num /= 62;
        } while (num > 0);
        return sb.toString();
    }
}
