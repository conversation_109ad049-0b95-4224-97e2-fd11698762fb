package com.wormhole.hotelds.core.utils;

import cn.hutool.core.util.StrUtil;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

public class DeviceInitRtcUtil {


    public static String getRtcUserId(String deviceType, String deviceId) {
        return  deviceType + StrUtil.UNDERLINE + deviceId;
    }

    public static String getDeviceIdFromRtcUserId(String rtcUserId, String deviceType) {
        if (rtcUserId == null || deviceType == null) {
            return null;
        }

        String prefix = deviceType + StrUtil.UNDERLINE;

        if (rtcUserId.startsWith(prefix)) {
            return rtcUserId.substring(prefix.length());
        }

        return null;
    }

    public static String getHotelCodeFromRtcRoomId(String rtcRoomId){
        if (rtcRoomId == null) return StringUtils.EMPTY;
        String[] split = rtcRoomId.split(StrUtil.UNDERLINE);
        return split.length > 0 ? split[0] : StringUtils.EMPTY;
    }

    public static String getDeviceIdByRtcUserId(String rtcUserId){
       return Arrays.stream(DeviceTypeEnum.values())
                .filter(deviceTypeEnum -> rtcUserId.startsWith(deviceTypeEnum.getCode() + StrUtil.UNDERLINE))
                .findFirst()
                .map(deviceTypeEnum -> rtcUserId.substring(deviceTypeEnum.getCode().length() + 1)).orElse(rtcUserId);
    }
}
