package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：门店个性化定价表
 */
@Data
@Table("hds_hotel_product_pricing")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelProductPricingEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 门店套餐code
     */
    @Column("hotel_package_code")
    private String hotelPackageCode;

    /**
     * 产品ID，关联hds_product.id
     */
    @Column("product_id")
    private Integer productId;

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    @Column("period_type")
    private Integer periodType;

    /**
     * 自定义门市价，为空则使用标准价格
     */
    @Column("custom_market_price")
    private BigDecimal customMarketPrice;

    /**
     * 自定义优惠价，为空则使用标准价格
     */
    @Column("custom_discount_price")
    private BigDecimal customDiscountPrice;

    /**
     * 自定义折扣比例，为空则使用标准比例
     */
    @Column("custom_discount_rate")
    private BigDecimal customDiscountRate;

    /**
     * 最终价格（计算后的实际价格）
     */
    @Column("final_price")
    private BigDecimal finalPrice;

    /**
     * 是否使用自定义定价：0-使用标准价格，1-使用自定义价格
     */
    @Column("is_custom_pricing")
    private Integer isCustomPricing;
} 