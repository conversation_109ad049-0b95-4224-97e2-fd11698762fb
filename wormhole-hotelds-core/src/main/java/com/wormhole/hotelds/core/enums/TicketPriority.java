package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;

/**
 * 工单优先级枚举
 */
@Getter
@AllArgsConstructor
public enum TicketPriority {
    NORMAL(0, "普通"),
    URGENT(1, "紧急/投诉");

    private final Integer code;
    private final String description;

    private static final Map<Integer, TicketPriority> PRIORITY_MAP = new HashMap<>();

    static {
        for (TicketPriority priority : TicketPriority.values()) {
            PRIORITY_MAP.put(priority.getCode(), priority);
        }
    }

    public static TicketPriority getByCode(Integer code) {
        return PRIORITY_MAP.get(code);
    }
}