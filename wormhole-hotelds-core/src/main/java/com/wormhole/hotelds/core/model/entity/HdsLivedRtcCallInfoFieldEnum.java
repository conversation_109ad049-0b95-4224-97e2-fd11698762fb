package com.wormhole.hotelds.core.model.entity;

/**
 * 酒店住中AI-RTC通话信息表字段枚举
 */
public enum HdsLivedRtcCallInfoFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 通话开始时间
     */
    call_start_time,

    /**
     * 通话结束时间
     */
    call_end_time,

    /**
     * RTC房间ID
     */
    rtc_room_id,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 发起方类型
     */
    initiator_type,

    /**
     * 发起方用户ID
     */
    initiator_user_id,

    /**
     * 接收方类型
     */
    receiver_type,

    /**
     * 接收方用户ID
     */
    receiver_user_id,

    /**
     * 发起方位置编码
     */
    position_code,

    /**
     * 通话状态
     */
    call_status,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 修改时间
     */
    updated_at,

    /**
     * 最后更新人ID
     */
    updated_by,

    /**
     * 最后更新人姓名
     */
    updated_by_name,

    /**
     * 行状态
     */
    row_status
}    