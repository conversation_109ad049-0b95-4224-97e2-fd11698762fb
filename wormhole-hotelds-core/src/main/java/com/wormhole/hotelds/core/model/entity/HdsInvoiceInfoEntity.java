package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：发票信息表
 */
@Data
@Table("hds_invoice_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsInvoiceInfoEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 门店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 发票抬头
     */
    @Column("invoice_header")
    private String invoiceHeader;

    /**
     * 是否默认：1-是，0-否
     */
    @Column("is_default")
    private Integer isDefault;

    /**
     * 纳税人识别号
     */
    @Column("tax_number")
    private String taxNumber;

    /**
     * 接收邮箱
     */
    @Column("receive_email")
    private String receiveEmail;

    /**
     * 发票内容
     */
    @Column("invoice_content")
    private String invoiceContent;

    /**
     * 更多信息/备注
     */
    @Column("more_info")
    private String moreInfo;
} 