package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;
import java.time.*;

/**
 * 设备信息表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceInfoEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;


    /**
     * 设备序列号
     */
    @Column("device_sn")
    private String deviceSn;


    /**
     * 设备IMEI号
     */
    @Column("imei")
    private String imei;


    /**
     * 设备型号id
     */
    @Column("model_id")
    private Long modelId;


    /**
     * mac地址
     */
    @Column("mac_address")
    private String macAddress;


    /**
     * 保修开始时间
     */
    @Column("warranty_start")
    private LocalDateTime warrantyStart;


    /**
     * 保修结束时间
     */
    @Column("warranty_end")
    private LocalDateTime warrantyEnd;


    /**
     * 入库时间
     */
    @Column("storage_time")
    private LocalDateTime storageTime;


    /**
     * 当前app版本
     */
    @Column("app_version")
    private String appVersion;


    /**
     * 设备app类型 (front: ai智能座机; room: ai云电话pad版)
     */
    @Column("device_app_type")
    private String deviceAppType;


    /**
     * 采购单价（元）
     */
    @Column("price")
    private BigDecimal price;

    /**
     * 采购时间
     */
    @Column("purchase_time")
    private LocalDateTime purchaseTime;


    /**
     * 规格描述
     */
    @Column("spec_desc")
    private String specDesc;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


}