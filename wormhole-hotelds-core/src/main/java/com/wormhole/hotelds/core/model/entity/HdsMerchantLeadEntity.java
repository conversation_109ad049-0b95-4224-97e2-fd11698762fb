package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/5/20 10:56
 * @Description：HdsMerchantLeadEntity
 */
@Data
@Table("hds_merchant_lead")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsMerchantLeadEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 邀请人ID(员工表ID)
     */
    @Column("inviter_id")
    private Integer inviterId;

    /**
     * 被邀请人ID(员工表ID)
     */
    @Column("invitee_id")
    private Integer inviteeId;

    /**
     * 被邀请人使用的邀请码
     */
    @Column("invite_code")
    private String inviteCode;

    /**
     * 被邀请人所在门店的邀请码
     */
    @Column("hotel_invite_code")
    private String hotelInviteCode;

    /**
     * 被邀请人所在门店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 是否已创建门店: 1-是, 0-否
     */
    @Column("is_hotel_created")
    private Integer isHotelCreated;

    /**
     * 是否已登录: 1-是, 0-否
     */
    @Column("is_login")
    private Integer isLogin;

    /**
     * 首次登录时间
     */
    @Column("login_at")
    private LocalDateTime loginAt;

    /**
     * 是否使用功能: 1-是, 0-否
     */
    @Column("is_feature_used")
    private Integer isFeatureUsed;

    /**
     * 首次使用功能时间
     */
    @Column("feature_used_at")
    private LocalDateTime featureUsedAt;

    /**
     * 奖励状态: 0-未奖励, 1-已奖励, 2-不符合奖励条件
     */
    @Column("reward_status")
    private Integer rewardStatus;

    /**
     * 奖励月数
     */
    @Column("reward_months")
    private Integer rewardMonths;

    /**
     * 奖励发放时间
     */
    @Column("rewarded_at")
    private LocalDateTime rewardedAt;
}
