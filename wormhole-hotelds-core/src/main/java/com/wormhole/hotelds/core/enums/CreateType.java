package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;

/**
 * 创建类型枚举
 */
@Getter
@AllArgsConstructor
public enum CreateType {
    AUTO(0, "自动创建"),
    MANUAL(1, "手动创建");

    private final Integer code;
    private final String description;

    private static final Map<Integer, CreateType> CREATE_TYPE_MAP = new HashMap<>();

    static {
        for (CreateType type : CreateType.values()) {
            CREATE_TYPE_MAP.put(type.getCode(), type);
        }
    }

    public static CreateType getByCode(Integer code) {
        return CREATE_TYPE_MAP.get(code);
    }
}