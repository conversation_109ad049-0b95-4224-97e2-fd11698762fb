package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/4/21 09:15
 * @Description：HdsTaskFieldEnum
 */
public enum HdsTaskFieldEnum {

    /**
     * 主键
     */
    id,

    /**
     * 任务名称
     */
    task_name,

    /**
     * 任务类型
     */
    task_type,

    /**
     * 任务状态
     */
    status,

    /**
     * 状态消息
     */
    message,

    /**
     * 总记录数
     */
    total_rows,

    /**
     * 成功记录数
     */
    success_rows,

    /**
     * 失败记录数
     */
    failed_rows,

    /**
     * 业务类型
     */
    business_type,

    /**
     * 业务ID
     */
    business_id,

    /**
     * 任务参数
     */
    params,

    /**
     * 错误摘要
     */
    error_summary,

    /**
     * 错误信息
     */
    error_details,

    /**
     * 门店code
     */
    hotel_code,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,
}
