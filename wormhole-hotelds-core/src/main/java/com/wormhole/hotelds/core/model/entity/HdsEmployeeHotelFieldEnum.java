package com.wormhole.hotelds.core.model.entity;

/**
 * 员工-酒店关联 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-15 18:38:19
 */
public enum HdsEmployeeHotelFieldEnum {
    /**
     * 
     */
    id,

    /**
     * 员工ID
     */
    employee_id,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 角色编码
     */
    role_code,

    /**
     * 员工在该酒店的状态 1正常 2停用 3注销
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}