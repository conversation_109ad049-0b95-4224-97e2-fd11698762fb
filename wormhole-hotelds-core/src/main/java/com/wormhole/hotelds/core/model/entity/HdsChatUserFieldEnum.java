package com.wormhole.hotelds.core.model.entity;

/**
 * 聊天用户表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-08 
 */
public enum HdsChatUserFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 账号（openid）
     */
    account,

    /**
     * 手机号
     */
    mobile,

    /**
     * 昵称
     */
    nick_name,

    /**
     * 用户状态 1:正常 2:禁用 3:注销
     */
    status,

    /**
     * 酒店
     */
    hotel_code,

    /**
     * 房间号
     */
    position_code,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人ID
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;
}