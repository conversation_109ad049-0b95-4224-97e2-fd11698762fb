package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：交易订单表 字段枚举
 */
public enum HdsOrderFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 订单编号，系统自动生成
     */
    order_no,

    /**
     * 酒店编码，关联hds_hotel_info.hotel_code
     */
    hotel_code,

    /**
     * 套餐编码
     */
    package_code,

    /**
     * 套餐名称
     */
    package_name,

    /**
     * 门店套餐code
     */
    hotel_package_code,

    /**
     * 产品ID，关联hds_product.id
     */
    product_id,

    /**
     * 产品名称
     */
    product_name,

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    period_type,

    /**
     * 房间数量（按房间收费时记录）
     */
    room_count,

    /**
     * 原价金额
     */
    original_amount,

    /**
     * 优惠金额
     */
    discount_amount,

    /**
     * 订单金额（原价-优惠）
     */
    order_amount,

    /**
     * 产品到期时间
     */
    expire_time,

    /**
     * 订单状态：0-待支付，1-已支付，2-已取消
     */
    order_status,

    /**
     * 支付方式：1-微信，2-支付宝
     */
    pay_method,

    /**
     * 支付时间
     */
    paid_at,

    /**
     * 订单过期时间
     */
    expire_at,

    /**
     * 备注信息
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 