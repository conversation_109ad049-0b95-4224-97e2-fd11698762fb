package com.wormhole.hotelds.core.enums;

import lombok.*;

import java.util.*;
/**
 * 反馈状态枚举
 */
@Getter
@AllArgsConstructor
public enum FeedbackStatus {
    NONE(0, "无反馈"),
    SATISFIED(1, "满意"),
    UNSATISFIED(2, "不满意");

    private final Integer code;
    private final String description;

    private static final Map<Integer, FeedbackStatus> FEEDBACK_STATUS_MAP = new HashMap<>();

    static {
        for (FeedbackStatus status : FeedbackStatus.values()) {
            FEEDBACK_STATUS_MAP.put(status.getCode(), status);
        }
    }

    public static FeedbackStatus getByCode(Integer code) {
        return FEEDBACK_STATUS_MAP.get(code);
    }
}