package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：付费产品表 字段枚举
 */
public enum HdsProductFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 套餐code
     */
    package_code,

    /**
     * 产品名称
     */
    product_name,

    /**
     * 产品描述JSON数组，最多10条，每条最大50字符
     */
    product_description,

    /**
     * 产品在套餐中的排序
     */
    sort_order,

    /**
     * 是否推荐：1-是，0-否
     */
    is_recommend,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 