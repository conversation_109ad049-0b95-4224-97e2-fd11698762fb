package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 通话反馈表
 *
 */
@Data
@Table("hds_call_feedback")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsCallFeedbackEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    private String hotelCode;

    private String positionCode;
    /**
    * 反馈状态(0未反馈 1:点赞 2:点踩)
    */
    private Integer feedbackStatus;
    /**
     * 反馈类型(空:点赞 ui_bug:界面问题 harmful:有害内容 over_refuse:过度拒绝 not_follow:未完全按要求完成 incorrect:内容不准确 incomplete:回答不完整 other:其他)
     */
    private String feedbackTypes;
    /**
     * 反馈内容
     */
    private String feedbackContent;
    /**
     * 反馈时间
      */
    private LocalDateTime feedbackTime;
    /**
     * RTC语音房间ID
      */
    private String rtcRoomId;


}