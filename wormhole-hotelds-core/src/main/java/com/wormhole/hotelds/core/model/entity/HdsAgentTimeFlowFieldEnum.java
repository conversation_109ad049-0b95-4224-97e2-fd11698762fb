package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/21 10:29
 * @Description：HdsAgentTimeFlowFieldEnum
 */
public enum HdsAgentTimeFlowFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 类型
     */
    type,

    /**
     * 门店编码
     */
    hotel_code,

    /**
     * 流水类型: 1-初始赠送, 2-邀请奖励, 3-充值, 4-管理员调整, 5-系统调整
     */
    flow_type,

    /**
     * 操作类型: 1-增加, 2-减少
     */
    operation_type,

    /**
     * 变更月数
     */
    months_changed,

    /**
     * OTA延期月数修改前的值
     */
    ota_extend_months_before,

    /**
     * OTA延期月数修改后的值
     */
    ota_extend_months_after,

    /**
     * 奖励累计月数修改前的值
     */
    ota_reward_months_before,

    /**
     * 奖励累计月数修改后的值
     */
    ota_reward_months_after,

    /**
     * 使用的邀请码
     */
    invitation_code,

    /**
     * 当前到期时间（修改前）
     */
    expiration_before,

    /**
     * 最新到期时间（修改后）
     */
    expiration_after,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新人ID
     */
    updated_by,

    /**
     * 更新人名称
     */
    updated_by_name,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态：0-删除，1-有效
     */
    row_status;
}
