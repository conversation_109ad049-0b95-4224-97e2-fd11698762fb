package com.wormhole.hotelds.core.enums;

import com.wormhole.common.util.JacksonUtils;
import lombok.*;

import java.util.*;

/**
 * 设备类型枚举
 */
@AllArgsConstructor
@Getter
public enum DeviceTypeEnum {

    FRONT("front", "AI智能座机",1),
    ROOM("room", "AI云电话客房端",2),
    WECHAT_MINI_APP("wechat_mini_app", "客房AI小程序",2),
    BDW_APP("bdw_app", "百达屋APP",2),
    FRONT_APP("front_app", "AI智能座机APP",1),
    ;
    private final String code;
    private final String description;
    /**
     * 1:酒店；2: 住店用户
     */
    private final Integer source;

    private static final Map<String, DeviceTypeEnum> DEVICE_TYPE_MAP = new HashMap<>();
    static {
        for (DeviceTypeEnum value : DeviceTypeEnum.values()) {
            DEVICE_TYPE_MAP.put(value.getCode(), value);
        }
    }

    public static String getDescriptionByCode(String code) {
        if (DEVICE_TYPE_MAP.containsKey(code)){
            return DEVICE_TYPE_MAP.get(code).getDescription();
        }
        return null;
    }

    public static DeviceTypeEnum getByPrefix(String prefix) {
        if (prefix.startsWith("front")){
            return prefix.startsWith("front_app") ? FRONT_APP : FRONT;
        }
        return Arrays.stream(values())
                .filter(type -> prefix.startsWith(type.getCode()))
                .findFirst()
                .orElse(null);
    }


    public static boolean judgeDeviceFromHotel(String deviceType){
        if (!DEVICE_TYPE_MAP.containsKey(deviceType)){
            return false;
        }
        return DEVICE_TYPE_MAP.get(deviceType).getSource().equals(1);
    }

    public static boolean judgeDeviceFromUser(String deviceType){
        if (!DEVICE_TYPE_MAP.containsKey(deviceType)){
            return false;
        }
        return DEVICE_TYPE_MAP.get(deviceType).getSource().equals(2);
    }
}
