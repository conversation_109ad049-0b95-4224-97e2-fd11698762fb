package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

/**
 * @Author：flx
 * @Date：2025/3/29 21:28
 * @Description：任务实体，用于跟踪异步任务执行状态
 */
@Data
@Table("hds_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsTaskEntity extends BaseEntity {

    /**
     * 主键
     */
    @Id
    @Column("id")
    private Integer id;

    /**
     * 任务名称
     */
    @Column("task_name")
    private String taskName;

    /**
     * 任务类型
     * 例如: 1-Excel导入, 2-导出
     */
    @Column("task_type")
    private Integer taskType;

    /**
     * 任务状态
     * 对应ImportStatusEnum: 0-等待处理, 1-处理中, 2-完成, 3-失败
     */
    @Column("status")
    private Integer status;

    /**
     * 状态消息
     * 记录当前任务进度或错误信息
     */
    @Column("message")
    private String message;

    /**
     * 总记录数
     */
    @Column("total_rows")
    private Integer totalRows;

    /**
     * 成功记录数
     */
    @Column("success_rows")
    private Integer successRows;

    /**
     * 失败记录数
     */
    @Column("failed_rows")
    private Integer failedRows;

    /**
     * 业务类型
     * 例如: hotel, room, store等
     */
    @Column("business_type")
    private String businessType;

    /**
     * 业务ID
     * 关联的业务对象ID
     */
    @Column("business_id")
    private String businessId;

    /**
     * 任务参数
     * 存储任务相关的JSON参数，包括：
     * - fileName: 文件名
     * - objectKey: OSS对象键
     * - fileSize: 文件大小
     * - fileType: 文件类型
     * - fileUrl: 文件URL
     */
    @Column("params")
    private String params;

    /**
     * 错误摘要
     */
    @Column("error_summary")
    private String errorSummary;

    /**
     * 错误信息
     */
    @Column("error_details")
    private String errorDetails;

    /**
     * 门店code
     */
    @Column("hotel_code")
    private String hotelCode;

    @Transient
    @Override
    public void setRowStatus(Integer rowStatus) {  // 修改返回类型为 HdsMerchantEntity
        super.setRowStatus(rowStatus);
    }
}