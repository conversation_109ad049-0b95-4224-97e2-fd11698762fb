package com.wormhole.hotelds.core.model.entity;

/**
 * 系统操作日志表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-03 16:43:51
 */
public enum HdsOperationLogFieldEnum {
    /**
     * 日志id
     */
    id,

    /**
     * 门店编码
     */
    hotel_code,

    /**
     * 员工id
     */
    employee_id,

    /**
     * 员工名称
     */
    employee_name,

    /**
     * 模块编码 设备管理，商家管理，酒店管理，房间管理等等
     */
    module_code,

    /**
     * 模块名称
     */
    module_name,

    /**
     * 操作对象id
     */
    object_id,

    /**
     * 操作对象类型(hotel:门店,device:设备,contract:合同,merchant:商家等)
     */
    object_type,

    /**
     * 操作对象名称
     */
    object_name,

    /**
     * 操作类型(add:新增,update:修改,delete:删除,query:查询,export:导出,import:导入,login:登录,logout:登出等)
     */
    opr_type,

    /**
     * 调用方法
     */
    opr_method,

    /**
     * 操作ip地址
     */
    opr_ip,

    /**
     * 请求参数
     */
    opr_params,

    /**
     * 操作内容描述
     */
    opr_content,

    /**
     * 操作前数据(json格式)
     */
    opr_before,

    /**
     * 操作后数据(json格式)
     */
    opr_after,

    /**
     * 操作时间
     */
    opr_time,

    /**
     * 操作结果(success:成功,fail:失败)
     */
    opr_result,

    /**
     * 错误消息
     */
    error_msg,

    /**
     * 执行时长(毫秒)
     */
    execution_time,

    /**
     * 创建时间
     */
    create_time;

}