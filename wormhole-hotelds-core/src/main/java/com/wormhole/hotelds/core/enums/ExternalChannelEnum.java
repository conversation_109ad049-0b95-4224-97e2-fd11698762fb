package com.wormhole.hotelds.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExternalChannelEnum {
    /**
     * 携程;code跟携程评论excel里面一致
     */
    CTRIP("Ctrip", ExternalPlatformEnum.CTRIP, "携程"),

    /**
     * 美团
     */
    MEITUAN("MeiTuan", ExternalPlatformEnum.MEITUAN, "美团"),

    /**
     * 抖音
     */
    DOUYIN("DouYin", ExternalPlatformEnum.DOUYIN, "抖音"),

    /**
     * 飞猪
     */
    FLIGGY("Fliggy", ExternalPlatformEnum.FLIGGY, "飞猪"),

    /**
     * 去哪儿;code跟携程评论excel里面一致
     */
    QUNAR("Qunar", ExternalPlatformEnum.CTRIP, "去哪儿"),
    /**
     * 智行;code跟携程评论excel里面一致
     * */
    ZHIXING("ZhiXing", ExternalPlatformEnum.CTRIP, "智行"),

    /**
     * 艺龙;code跟携程评论excel里面一致
     */
    ELONOG("Elong", ExternalPlatformEnum.CTRIP, "艺龙"),

    /**
     * 点评
     * */
    DIANPING("DianPing", ExternalPlatformEnum.MEITUAN, "点评"),

    BDW("Bdw", ExternalPlatformEnum.BDW,"百达屋"),

    ;


    private final String code;
    private final ExternalPlatformEnum platform;
    private final String name;

}