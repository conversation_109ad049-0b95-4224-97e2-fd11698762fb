package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：产品定价表
 */
@Data
@Table("hds_product_pricing")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsProductPricingEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 产品ID，关联hds_product.id
     */
    @Column("product_id")
    private Integer productId;

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    @Column("period_type")
    private Integer periodType;

    /**
     * 门市价
     */
    @Column("market_price")
    private BigDecimal marketPrice;

    /**
     * 优惠价（一口价模式使用）
     */
    @Column("discount_price")
    private BigDecimal discountPrice;

    /**
     * 折扣比例，百分比（折扣模式使用）
     */
    @Column("discount_rate")
    private BigDecimal discountRate;

    /**
     * 最终价格（计算后的实际价格）
     */
    @Column("final_price")
    private BigDecimal finalPrice;
} 