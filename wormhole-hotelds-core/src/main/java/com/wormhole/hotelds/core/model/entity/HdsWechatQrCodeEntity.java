package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 微信二维码数据表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_wechat_qr_code")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsWechatQrCodeEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;


    /**
     * 酒店代码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 房间号
     */
    @Column("position_code")
    private String positionCode;


    /**
     * 微信二维码URL
     */
    @Column("qr_code_url")
    private String qrCodeUrl;

    /**
     * 微信海报URL
     */
    @Column("poster_url")
    private String posterUrl;

    /**
     * 普通二维码URL
     */
    @Column("normal_qr_code_url")
    private String normalQrCodeUrl;

    /**
     * 普通海报URL
     */
    @Column("normal_poster_url")
    private String normalPosterUrl;


}