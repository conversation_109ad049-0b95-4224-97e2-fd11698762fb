package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 设备最新版本信息表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_device_latest_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsDeviceLatestInfoEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    private Long id;


    /**
     * 客户端生成的唯一设备号
     */
    @Column("device_id")
    private String deviceId;


    /**
     * 设备序列号
     */
    @Column("device_sn")
    private String deviceSn;


    /**
     * 版本名（展示用，如 1.0.3）
     */
    @Column("app_version")
    private String appVersion;


    /**
     * 备注
     */
    @Column("remark")
    private String remark;


}