package com.wormhole.hotelds.core.model.entity;

/**
 * 通话反馈表 字段枚举
 *
 */
public enum HdsCallFeedbackFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 酒店代码
     */
    hotel_code,

    /**
     * 房间编码
     */
    position_code,
    /**
     * 反馈状态(0未反馈 1:点赞 2:点踩)
     */
    feedback_status,
    /**
     * 反馈类型(空:点赞 ui_bug:界面问题 harmful:有害内容 over_refuse:过度拒绝 not_follow:未完全按要求完成 incorrect:内容不准确 incomplete:回答不完整 other:其他)
     */
    feedback_types,
    /**
     * 反馈内容
     */
    feedback_content,
    /**
     * 反馈时间
     */
    feedback_time,
    /**
     * RTC语音房间ID
     */
    rtc_room_id,
    /**
     * 创建时间
     */
    created_at,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 修改时间
     */
    updated_at,

    /**
     * 最后更新人ID
     */
    updated_by,

    /**
     * 最后更新人姓名
     */
    updated_by_name,

    /**
     * 行状态: 0-已删除 1-正常
     */
    row_status;

}