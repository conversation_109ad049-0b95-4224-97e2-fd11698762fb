package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：付费套餐主表 字段枚举
 */
public enum HdsPackageFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 套餐编码，唯一标识
     */
    package_code,

    /**
     * 套餐名称，唯一
     */
    package_name,

    /**
     * 付费方式：0-按房间数量收费，1-按门店收费
     */
    pay_mode,

    /**
     * 优惠方式：0-折扣，1-一口价
     */
    discount_mode,

    /**
     * 状态：1-启用，0-停用
     */
    status,

    /**
     * 是否推荐：1-是，0-否
     */
    is_recommend,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 