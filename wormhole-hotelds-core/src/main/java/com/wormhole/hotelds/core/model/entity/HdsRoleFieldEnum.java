package com.wormhole.hotelds.core.model.entity;

/**
 * 角色表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-14 16:49:42
 */
public enum HdsRoleFieldEnum {
    /**
     * 
     */
    id,

    /**
     * 角色编码
     */
    role_code,

    /**
     * 角色名称
     */
    role_name,

    /**
     * 角色描述
     */
    description,

    /**
     * 状态 1正常 2禁用
     */
    status,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}