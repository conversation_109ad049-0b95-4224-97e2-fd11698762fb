package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：交易订单表
 */
@Data
@Table("hds_order")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsOrderEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Long id;

    /**
     * 订单编号，系统自动生成
     */
    @Column("order_no")
    private String orderNo;

    /**
     * 酒店编码，关联hds_hotel_info.hotel_code
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 套餐编码
     */
    @Column("package_code")
    private String packageCode;

    /**
     * 套餐名称
     */
    @Column("package_name")
    private String packageName;

    /**
     * 门店套餐code
     */
    @Column("hotel_package_code")
    private String hotelPackageCode;

    @Column("product_id")
    private Integer productId;

    @Column("product_name")
    private String productName;

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    @Column("period_type")
    private Integer periodType;

    /**
     * 房间数量（按房间收费时记录）
     */
    @Column("room_count")
    private Integer roomCount;

    /**
     * 原价金额
     */
    @Column("original_amount")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @Column("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 订单金额（原价-优惠）
     */
    @Column("order_amount")
    private BigDecimal orderAmount;

    /**
     * 产品到期时间
     */
    @Column("expire_time")
    private LocalDateTime expireTime;

    /**
     * 订单状态：0-待支付，1-已支付，2-已取消
     */
    @Column("order_status")
    private Integer orderStatus;

    /**
     * 支付方式：1-微信，2-支付宝
     */
    @Column("pay_method")
    private Integer payMethod;

    /**
     * 支付时间
     */
    @Column("paid_at")
    private LocalDateTime paidAt;

    /**
     * 订单过期时间
     */
    @Column("expire_at")
    private LocalDateTime expireAt;

    /**
     * 备注信息
     */
    @Column("remark")
    private String remark;
} 