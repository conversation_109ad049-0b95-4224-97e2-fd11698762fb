package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：账单表 字段枚举
 */
public enum HdsBillFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 账单编号，系统自动生成
     */
    bill_no,

    /**
     * 易宝订单号（uniqueOrderNo）
     */
    third_order_no,

    /**
     * 流水号（从支付平台获取）
     */
    transaction_no,

    /**
     * 订单编号
     */
    order_no,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 支付金额
     */
    pay_amount,

    /**
     * 支付方式：1-微信，2-支付宝
     */
    pay_method,

    /**
     * 交易状态：1-交易成功，2-已退款
     */
    transaction_status,

    /**
     * 交易完成时间
     */
    transaction_at,

    /**
     * 退款金额
     */
    refund_amount,

    /**
     * 退款时间
     */
    refund_at,

    /**
     * 退款原因
     */
    refund_reason,

    /**
     * 是否已申请发票：0-未申请，1-已申请
     */
    is_invoice_applied,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 