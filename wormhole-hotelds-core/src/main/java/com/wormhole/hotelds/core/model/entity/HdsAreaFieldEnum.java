package com.wormhole.hotelds.core.model.entity;

/**
 * 国家区域 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-03 16:35:12
 */
public enum HdsAreaFieldEnum {
    /**
     * 
     */
    id,

    /**
     * 区域类型(1 省份 2 城市 3 县区)
     */
    area_type,

    /**
     * 地区唯一编号
     */
    area_code,

    /**
     * 父节点
     */
    parent_code,

    /**
     * 地区名称中文全拼
     */
    area_pinyin,

    /**
     * 拼音首字母
     */
    first_letter,

    /**
     * 邮政编码
     */
    post_code,

    /**
     * 是否有效(0 无效 1 有效)
     */
    valid,

    /**
     * 是否热门城市
     */
    hot_mark,

    /**
     * 区域中文名称
     */
    area_name,

    /**
     * 区域英文名称
     */
    area_name_en,

    /**
     * 创建时间
     */
    create_time,

    /**
     * 更新时间
     */
    update_time;

}