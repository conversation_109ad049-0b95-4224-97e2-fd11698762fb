package com.wormhole.hotelds.core.model.entity;

import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * 门店-第三方系统映射表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_hotel_mapping")
@Accessors(chain = true)
@EqualsAndHashCode
public class HdsHotelMappingEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    private Long id;


    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 酒店名称
     */
    @Column("hotel_name")
    private String hotelName;


    /**
     * 外部系统酒店id
     */
    @Column("external_id")
    private String externalId;


    /**
     * 外部系统酒店名称
     */
    @Column("external_name")
    private String externalName;



    /**
     * 外部渠道
     */
    @Column("channel")
    private String channel;
    /**
     * 外部系统平台
     */
    @Column("platform")
    private String platform;



    /**
     * 状态 0无效 1有效
     */
    @Column("status")
    private Integer status;


    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;


    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;


}