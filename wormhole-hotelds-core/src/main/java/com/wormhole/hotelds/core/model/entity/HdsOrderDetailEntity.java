package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：订单明细表
 */
@Data
@Table("hds_order_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsOrderDetailEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 订单编号，系统自动生成
     */
    @Column("order_no")
    private String orderNo;

    /**
     * 套餐信息
     */
    @Column("package_info")
    private String packageInfo;

    /**
     * 产品信息
     */
    @Column("product_info")
    private String productInfo;

    /**
     * 价格信息
     */
    @Column("price_info")
    private String priceInfo;

    /**
     * 门店套餐信息
     */
    @Column("hotel_package_info")
    private String hotelPackageInfo;

    /**
     * 门店价格信息
     */
    @Column("hotel_price_info")
    private String hotelPriceInfo;

    @Column("order_confirm_info")
    private String orderConfirmInfo;
} 