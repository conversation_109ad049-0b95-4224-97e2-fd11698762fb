package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：产品定价表 字段枚举
 */
public enum HdsProductPricingFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 产品ID，关联hds_product.id
     */
    product_id,

    /**
     * 周期类型：1-月度，2-季度，3-年度
     */
    period_type,

    /**
     * 门市价
     */
    market_price,

    /**
     * 优惠价（一口价模式使用）
     */
    discount_price,

    /**
     * 折扣比例，百分比（折扣模式使用）
     */
    discount_rate,

    /**
     * 最终价格（计算后的实际价格）
     */
    final_price,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 