package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 员工-酒店关联
 *
 * <AUTHOR>
 */
@Data
@Table("hds_employee_hotel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsEmployeeHotelEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @Id
    private Integer id;


    /**
     * 员工ID
     */
    @Column("employee_id")
    private Integer employeeId;


    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 角色编码
     */
    @Column("role_code")
    private String roleCode;

    /**
     * 员工在该酒店的状态 1正常 2停用 3注销
     */
    @Column("status")
    private Integer status;


}