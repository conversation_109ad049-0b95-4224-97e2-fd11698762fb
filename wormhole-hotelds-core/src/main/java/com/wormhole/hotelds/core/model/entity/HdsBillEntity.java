package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.math.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：账单表
 */
@Data
@Table("hds_bill")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsBillEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

//    /**
//     * 账单编号，系统自动生成
//     */
//    @Column("bill_no")
//    private String billNo;

    /**
     * 三方订单号（uniqueOrderNo）
     */
    @Column("third_order_no")
    private String thirdOrderNo;

    /**
     * 流水号（从支付平台获取）
     */
    @Column("transaction_no")
    private String transactionNo;

    /**
     * 订单ID，关联hds_order.id
     */
    @Column("order_no")
    private String orderNo;

    /**
     * 酒店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 支付金额
     */
    @Column("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付方式：1-微信，2-支付宝
     */
    @Column("pay_method")
    private Integer payMethod;

    /**
     * 交易状态：1-交易成功，2-已退款
     */
    @Column("transaction_status")
    private Integer transactionStatus;

    /**
     * 交易完成时间
     */
    @Column("transaction_at")
    private LocalDateTime transactionAt;

    /**
     * 退款金额
     */
    @Column("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @Column("refund_at")
    private LocalDateTime refundAt;

    /**
     * 退款原因
     */
    @Column("refund_reason")
    private String refundReason;

    /**
     * 是否已申请发票：0-未申请，1-已申请
     */
    @Column("is_invoice_applied")
    private Integer isInvoiceApplied;
} 