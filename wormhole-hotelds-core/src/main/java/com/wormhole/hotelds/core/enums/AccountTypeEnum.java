package com.wormhole.hotelds.core.enums;

import lombok.*;

/**
 * 账号类型枚举
 */
@AllArgsConstructor
@Getter
public enum AccountTypeEnum {
    
    MAIN_SWITCHBOARD(1, "总机"),
    EXTENSION(2, "分机");
    
    private final Integer code;
    private final String description;

    public static AccountTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AccountTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}