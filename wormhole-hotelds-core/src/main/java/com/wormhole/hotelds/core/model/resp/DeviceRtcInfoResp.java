package com.wormhole.hotelds.core.model.resp;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceRtcInfoResp {


    /**
     * rtc 厂商
     */
    private String rtcManufacturer;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 用户id deviceType + deviceId
     */
    private String rtcUserId;

    /**
     * 设备code
     */
    private String deviceId;

    /**
     * 房间唯一编码
     */
    private String positionCode;

    /**
     * 设备类型 （前台设备 & 客房设备）
     */
    private String deviceType;

    /**
     * 语音房id
     */
    private String rtcRoomId;


    private String hotelName;

    private Integer deviceStatus;

    private String account;

    private String username;

    private Integer employeeType;

    private String positionFullName;

}
