package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/5/22 13:46
 * @Description：HdsUserHotelLeadMappingEntity
 */
@Data
@Table("hds_user_hotel_lead_mapping")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsUserHotelLeadMappingEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 关联用户线索表 hds_user_lead.id
     */
    @Column("user_lead_id")
    private Long userLeadId;

    /**
     * 关联门店线索表 hds_hotel_lead.id
     */
    @Column("hotel_lead_id")
    private Long hotelLeadId;
}
