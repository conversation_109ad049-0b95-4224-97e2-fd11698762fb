package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/5/22 13:42
 * @Description：HdsUserLeadEntity
 */
@Data
@Table("hds_user_lead")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsUserLeadEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 线索编码(系统随机生成)
     */
    @Column("lead_code")
    private String leadCode;

    /**
     * 邀请人ID(员工表ID)
     */
    @Column("inviter_id")
    private Integer inviterId;

    /**
     * 被邀请人ID(员工表ID)
     */
    @Column("invitee_id")
    private Integer inviteeId;

    /**
     * 被邀请人使用的邀请码
     */
    @Column("invite_code")
    private String inviteCode;

    /**
     * 线索状态：0-初始，1-发展中，2-已完成
     */
    @Column("status")
    private Integer status;
}
