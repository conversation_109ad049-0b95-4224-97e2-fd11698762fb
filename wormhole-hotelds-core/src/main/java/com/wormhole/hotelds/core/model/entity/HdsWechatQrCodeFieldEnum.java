package com.wormhole.hotelds.core.model.entity;

/**
 * 微信二维码数据表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-09 10:51:40
 */
public enum HdsWechatQrCodeFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 酒店代码
     */
    hotel_code,

    /**
     * 房间号
     */
    position_code,

    /**
     * 微信二维码
     */
    qr_code_url,

    /**
     * 海报
     */
    poster_url,

    /**
     * 普通二维码URL
     */
    normal_qr_code_url,

    /**
     * 普通海报URL
     */
    normal_poster_url,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 修改时间
     */
    updated_at,

    /**
     * 最后更新人ID
     */
    updated_by,

    /**
     * 最后更新人姓名
     */
    updated_by_name,

    /**
     * 行状态: 0-已删除 1-正常
     */
    row_status;

}