package com.wormhole.hotelds.core.enums;

import lombok.*;

/**
 * 接单状态枚举
 */
@AllArgsConstructor
@Getter
public enum AcceptTicketStatusEnum {
    
    ALLOW_ACCEPT(0, "允许接单"),
    PAUSE_ACCEPT(1, "暂停接单");
    
    private final Integer code;
    private final String description;
    
    public static AcceptTicketStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AcceptTicketStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}