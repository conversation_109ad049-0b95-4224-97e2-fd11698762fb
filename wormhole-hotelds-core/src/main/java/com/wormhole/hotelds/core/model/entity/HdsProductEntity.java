package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：付费产品表
 */
@Data
@Table("hds_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsProductEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    private Integer id;

    /**
     * 套餐ID，关联hds_package.id
     */
    @Column("package_code")
    private String packageCode;

    /**
     * 产品名称
     */
    @Column("product_name")
    private String productName;

    /**
     * 产品描述JSON数组，最多10条，每条最大50字符
     */
    @Column("product_description")
    private String productDescription;

    /**
     * 产品在套餐中的排序
     */
    @Column("sort_order")
    private Integer sortOrder;

    /**
     * 是否推荐：1-是，0-否
     */
    @Column("is_recommend")
    private Integer isRecommend;
} 