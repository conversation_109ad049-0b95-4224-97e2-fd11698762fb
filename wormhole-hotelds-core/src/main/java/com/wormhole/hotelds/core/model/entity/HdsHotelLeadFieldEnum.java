package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/5/22 13:49
 * @Description：HdsHotelLeadFieldEnum
 */
public enum HdsHotelLeadFieldEnum {

    /**
     * 主键ID
     */
    id,

    /**
     * 线索编码
     */
    lead_code,

    /**
     * 门店编码
     */
    hotel_code,

    /**
     * 门店名称
     */
    hotel_name,

    /**
     * Ctrip EBK链接
     */
    ctrip_ebk_url,

    /**
     * 抢占成功的管理员ID
     */
    admin_user_id,

    /**
     * 门店归属状态：0-进行中，1-已归属
     */
    status,

    /**
     * 6个任务节点的进度信息，JSON格式存储完成时间
     */
    progress_json,

    /**
     * 任务全部完成时间
     */
    complete_time,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status;
}
