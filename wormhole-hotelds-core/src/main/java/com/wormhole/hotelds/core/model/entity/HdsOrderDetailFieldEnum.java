package com.wormhole.hotelds.core.model.entity;

/**
 * @Author：flx
 * @Date：2025/1/16 14:43
 * @Description：订单明细表 字段枚举
 */
public enum HdsOrderDetailFieldEnum {
    /**
     * 主键
     */
    id,

    /**
     * 订单编号，系统自动生成
     */
    order_no,

    /**
     * 套餐信息
     */
    package_info,

    /**
     * 产品信息
     */
    product_info,

    /**
     * 价格信息
     */
    price_info,

    /**
     * 门店套餐信息
     */
    hotel_package_info,

    /**
     * 门店价格信息
     */
    hotel_price_info,

    /**
     * 订单确认信息
     */
    order_confirm_info,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at,

    /**
     * 记录状态(1:有效, 0:无效)
     */
    row_status,
} 