package com.wormhole.hotelds.core.model.entity;

/**
 * 设备升级记录日志表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-07 08:27:34
 */
public enum HdsDeviceUpgradeLogFieldEnum {
    /**
     * 主键id
     */
    id,

    /**
     * 客户端生成的唯一设备号
     */
    device_id,

    /**
     * 设备序列号
     */
    device_sn,

    /**
     * 升级前的版本号
     */
    pre_version,

    /**
     * 升级后的版本号
     */
    new_version,

    /**
     * 升级时间
     */
    upgrade_time,

    /**
     * 升级来源（例如：后台推送、手动升级等）
     */
    upgrade_source,

    /**
     * 升级是否成功（1-成功，0-失败）
     */
    success,

    /**
     * 升级失败时的错误信息
     */
    error_message,

    /**
     * 备注
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}