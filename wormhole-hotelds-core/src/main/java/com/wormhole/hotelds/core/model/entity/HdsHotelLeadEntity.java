package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;
import java.time.*;

/**
 * @Author：flx
 * @Date：2025/5/22 13:44
 * @Description：HdsHotelLeadEntity
 */
@Data
@Table("hds_hotel_lead")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelLeadEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 门店线索编码
     */
    private String leadCode;

    /**
     * 门店编码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 门店名称
     */
    @Column("hotel_name")
    private String hotelName;

    /**
     * 携程EBK链接
     */
    @Column("ctrip_ebk_url")
    private String ctripEbkUrl;

    /**
     * 抢占成功的管理员ID
     */
    @Column("admin_user_id")
    private Integer adminUserId;

    /**
     * 门店归属状态：0-进行中，1-已归属
     */
    @Column("status")
    private Integer status;

    /**
     * 6个任务节点的进度信息，JSON格式存储完成时间
     */
    @Column("progress_json")
    private String progressJson;

    /**
     * 任务全部完成时间
     */
    @Column("complete_time")
    private LocalDateTime completeTime;
}
