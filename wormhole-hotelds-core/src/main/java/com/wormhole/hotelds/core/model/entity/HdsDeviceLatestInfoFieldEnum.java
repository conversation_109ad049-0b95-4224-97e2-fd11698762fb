package com.wormhole.hotelds.core.model.entity;

/**
 * 设备最新版本信息表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-11 10:43:48
 */
public enum HdsDeviceLatestInfoFieldEnum {
    /**
     * 主键id
     */
    id,

    /**
     * 客户端生成的唯一设备号
     */
    device_id,

    /**
     * 设备序列号
     */
    device_sn,

    /**
     * 版本名（展示用，如 1.0.3）
     */
    app_version,

    /**
     * 备注
     */
    remark,

    /**
     * 创建人id
     */
    created_by,

    /**
     * 创建人名称
     */
    created_by_name,

    /**
     * 修改人id
     */
    updated_by,

    /**
     * 修改人名称
     */
    updated_by_name,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 更新时间
     */
    updated_at;

}