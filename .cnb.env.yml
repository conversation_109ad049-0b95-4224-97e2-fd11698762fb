#k8s服务名=application名(如果不相等，先以k8s服务名为准，后续找运维变更)
APP_NAME: wormhole-hotelds-api
TASK_APP_NAME: wormhole-hotelds-api

#备注：1. port端口 jvm参数默认为8080, 严禁开发在nacos中配置端口号,如有找运维协调修正; 如有没用jvm参数的服务另找时间跟运维协调修正

#健康检查地址
LIVE_WEB_PATH: /actuator/health/liveness
READ_WEB_PATH: /actuator/health/readiness

#服务下线地址(这里只需要给出url地址即可)
D_URL: http://localhost:8080/nacos/deregister

#构建pom文件所在目录#
CONTEXT_PATH: "./"

#nvm pakage构建时-P指定的环境变量,如不指定空着
DEV_PACKAGE_ENV: "dev"
TEST_PACKAGE_ENV: "test"
PROD_PACKAGE_ENV: "prod"
INTL_DEV_PACKAGE_ENV: ""
INTL_TEST_PACKAGE_ENV: ""
INTL_PROD_PACKAGE_ENV: "intl-prod"
TASK_PACKAGE_ENV: "task"

#arg变量定义(有用到argName变量的这里定义)
DEV_BUILD_ARG: "dev"
TEST_BUILD_ARG: "test"
PROD_BUILD_ARG: "prod"
INTL_DEV_BUILD_ARG: ""
INTL_TEST_BUILD_ARG: ""
INTL_PROD_BUILD_ARG: "intl-prod"
TASK_BUILD_ARG: "task"

###### java构建java版本基础镜像 ######
#jdk1.8：maven:3.8.6-openjdk-8
#jdk11：maven:3.8.6-openjdk-11
#java17：maven:3.9.9-eclipse-temurin-17
#java21：maven:3.9.9-eclipse-temurin-21
#如还有其他java版本先选jdk11的，如还不行找运维协调#
package_imagename: "maven:3.9.9-eclipse-temurin-17"