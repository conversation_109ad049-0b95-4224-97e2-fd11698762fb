package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MatchWormholeRoomsReq implements Serializable {
    /**
     * 百达屋酒店编码
     */
    private String bdwHotelCode;

    /**
     * 子订单号
     */
    private String childOrderNo;

    /**
     * 房间号
     */
    private String roomNo;
}
