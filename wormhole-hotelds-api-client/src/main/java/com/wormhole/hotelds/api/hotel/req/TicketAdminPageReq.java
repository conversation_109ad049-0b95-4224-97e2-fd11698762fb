package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketAdminPageReq {

    private Integer current;

    private Integer pageSize;

    private String startTime;

    private String endTime;

    private String hotelCode;

    private String ticketCategory;

    private String positionName;

//    private Integer feedbackStatus;

    private String rtcRoomId;

    private Integer closedLoopLevel;

    private Integer handleMethod;

    private String showFlag;

    private Integer status;

    private Integer expiredFlag;

    @JsonIgnore
    private List<String> deviceIdList;
    @JsonIgnore
    private List<String> positionCodes;
    @JsonIgnore
    private List<String> ticketCategoryList;
}
