package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工单统计请求参数
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketStatisticsReq implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 统计日期，格式：yyyy-MM-dd
     */
    private String searchDate;
}
