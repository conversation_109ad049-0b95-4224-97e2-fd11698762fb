package com.wormhole.hotelds.api.hotel.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 酒店绑定类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@AllArgsConstructor
@Getter
public enum BindHotelTypeEnum {

    NO_HOTEL(0, "没有酒店"),
    SINGLE_HOTEL(1, "有1间酒店"),
    MULTIPLE_HOTELS(2, "有多间酒店");

    private final Integer code;
    private final String description;

    /**
     * 根据酒店数量获取绑定类型
     *
     * @param hotelCount 酒店数量
     * @return 绑定类型枚举
     */
    public static BindHotelTypeEnum fromHotelCount(int hotelCount) {
        if (hotelCount == 0) {
            return NO_HOTEL;
        } else if (hotelCount == 1) {
            return SINGLE_HOTEL;
        } else {
            return MULTIPLE_HOTELS;
        }
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static BindHotelTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(NO_HOTEL);
    }
} 