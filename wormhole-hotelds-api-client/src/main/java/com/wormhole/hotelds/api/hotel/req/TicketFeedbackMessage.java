package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketFeedbackMessage {
    private String ticketNo;

    private Integer feedbackStatus;

    private String rtcRoomId;

    private Integer feedbackDimension;
}
