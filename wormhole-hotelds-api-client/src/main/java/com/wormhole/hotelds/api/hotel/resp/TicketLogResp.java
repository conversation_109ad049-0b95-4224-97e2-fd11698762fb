package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketLogResp {

    private String actionType;

    private Integer expiredFlag;

    private Integer status;

    private String createdAt;

    private String createdBy;

    private String createdByName;

    private List<TicketConservationRecordResp> conservationRecords;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TicketConservationRecordResp {
        private String name;
        private String content;
    }
}
