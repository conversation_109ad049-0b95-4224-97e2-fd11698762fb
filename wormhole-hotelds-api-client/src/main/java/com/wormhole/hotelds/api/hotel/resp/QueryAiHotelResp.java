package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class QueryAiHotelResp implements Serializable {
    /**
     * AI酒店状态列表
     */
    private List<AiHotelStatusDto> hotelStatusList;

    public static QueryAiHotelResp of(List<AiHotelStatusDto> hotelStatusList) {
        return QueryAiHotelResp.builder()
                .hotelStatusList(hotelStatusList)
                .build();
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class AiHotelStatusDto {

        /**
         * BDW酒店编码
         */
        private String bdwHotelCode;

        /**
         * 是否拥有（是否是AI住中酒店）
         */
        private Boolean owned;

        public static AiHotelStatusDto of(String bdwHotelCode, Boolean owned) {
            return AiHotelStatusDto.builder()
                    .bdwHotelCode(bdwHotelCode)
                    .owned(owned)
                    .build();
        }
    }
}
