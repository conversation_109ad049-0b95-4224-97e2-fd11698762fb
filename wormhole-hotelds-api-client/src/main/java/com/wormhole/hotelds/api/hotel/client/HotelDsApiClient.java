package com.wormhole.hotelds.api.hotel.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.HotelDsApiConstant;
import com.wormhole.hotelds.api.hotel.req.*;
import com.wormhole.hotelds.api.hotel.resp.*;
import com.wormhole.hotelds.core.model.req.DeviceHeartReq;
import com.wormhole.hotelds.core.model.resp.DeviceInitResp;
import com.wormhole.hotelds.core.model.resp.DeviceRtcInfoResp;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ConditionalOnClass(WebClient.class)
@Component
@Slf4j
public class HotelDsApiClient {

    @Resource(name = HotelDsApiConstant.HOTEL_DS_API_WEB_CLIENT)
    private WebClient webClient;

    public Mono<Boolean> deviceLogOut(String deviceId){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("device_id", deviceId);
        return webClientPost("/device/log_out", paramMap,null, new TypeReference<Boolean>() {
        });
    }


    public Mono<PageResult<TicketAdminListResp>> getTicketPageFeign(TicketAdminPageReq req){
        return webClientPost("/ticket_admin/page_feign", null,req, new TypeReference<PageResult<TicketAdminListResp>>() {
        });
    }

    public Mono<TicketDetailResp> getTicketDetailFeign(TicketInfoReq req){
        return webClientPost("/ticket_admin/detail_feign", null,req, new TypeReference<TicketDetailResp>() {
        });
    }
    public Mono<Boolean> feedbackInTicketService(TicketFeedbackMessage req){
        return webClientPost("/device/ticket_service_feedback", null,req, new TypeReference<Boolean>() {
        });
    }

    public Mono<Boolean> deviceHeartAlive(DeviceHeartReq deviceQo){
        return webClientPost("/device/heart_alive", null,deviceQo, new TypeReference<Boolean>() {
        });
    }
    public Mono<List<DeviceRtcInfoResp>> getAvailableDevices(SearchDeviceReq deviceQo){
        return webClientPost("/device/get_available_devices", null,deviceQo, new TypeReference<List<DeviceRtcInfoResp>>() {
        });
    }

    public Mono<DeviceRtcInfoResp> getRtcDeviceInfo(String deviceId, String clientType){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("device_id", deviceId);
        paramMap.put("client_type", clientType);
        return webClientPost("/device/get_rtc_device_info", paramMap,null, new TypeReference<DeviceRtcInfoResp>() {
        });
    }

    public Mono<Boolean> cleanDeviceTickets(String hotelCode, String positionCode){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("hotel_code", hotelCode);
        paramMap.put("position_code", positionCode);
        return webClientPost("/device/clean_device_tickets", paramMap,null, new TypeReference<Boolean>() {
        });
    }

    public Mono<DeviceInitResp> getDeviceBase(String deviceId){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("device_id", deviceId);
        return webClientPost("/device/init_device_info", paramMap,null, new TypeReference<DeviceInitResp>() {
        });
    }

    /**
     * 设备初始化信息V2(对象入参版本)
     * @param req 设备初始化请求对象
     * @return 设备初始化响应
     */
    public Mono<DeviceInitResp> initAppDeviceInfo(DeviceInitReq req){
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
        return webClientPost("/device/init_app_device_info", null, req, headers, new TypeReference<DeviceInitResp>() {
        });
    }

    public Mono<Boolean> bindDeviceUserInfo(BindDeviceUserInfoReq req){
        return webClientPost("/device/bind_device_user_info", null,req, new TypeReference<Boolean>() {
        });
    }

    public Mono<EmployeeHotelAccessResp> checkEmployeeHotelAccess(Integer employeeId, String hotelCode, String source){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("employee_id", String.valueOf(employeeId));
        paramMap.put("hotel_code", hotelCode);
        paramMap.put("source", source);
        return webClientPost("/employee/hasAccess", paramMap,null, new TypeReference<EmployeeHotelAccessResp>() {
        });
    }

    public Mono<UserHotelInfoResp> getUserHotelInfo(Integer employeeId, Integer type) {
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("employee_id", String.valueOf(employeeId));
        paramMap.put("type", String.valueOf(type));
        return webClientPost("/employee/getUserHotelInfo", paramMap,null, new TypeReference<UserHotelInfoResp>() {
        });
    }

    public Mono<Boolean> checkHotelAccess(String hotelCode, String source){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("hotel_code", hotelCode);
        paramMap.put("source", source);
        return webClientPost("/hotel/hasAccess", paramMap,null, new TypeReference<Boolean>() {
        });
    }

    public Mono<Boolean> checkInvitationCodeValid( String invitationCode){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("invitation_code", String.valueOf(invitationCode));
        return webClientPost("/invitation_code/checkValid", paramMap,null, new TypeReference<Boolean>() {
        });
    }

    public Mono<Result<Boolean>> createSosTicket(CreateTicketSimpleReq req, Map<String, String> headers) {
        return webClientPost("/tickets/create_sos_ticket", null, req, headers, new TypeReference<Result<Boolean>>() {
        });
    }

    public Mono<Boolean> sendDeviceRtcMessage(String deviceId,  String isEnable){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("device_id", deviceId);
        paramMap.put("isEnable", isEnable);
        return webClientPost("/send/device/rtc", paramMap,null, new TypeReference<Boolean>() {
        });
    }


    public Mono<Result<Boolean>> sendRefreshCommand(RefreshCommandReq req, Map<String, String> headers) {
        return webClientPost("/tickets/send_refresh_command", null, req, headers, new TypeReference<Result<Boolean>>() {
        });
    }


    public Mono<Result<MatchWormholeRoomsResp>> matchWormholeRooms(MatchWormholeRoomsReq req, Map<String, String> headers) {
        return webClientPost("/bdw_ai_customer/match_wormhole_rooms", null, req, headers, new TypeReference<Result<MatchWormholeRoomsResp>>() {
        });
    }


    public Mono<Result<QueryAiHotelResp>> queryAiHotels(QueryAiHotelReq req, Map<String, String> headers) {
        return webClientPost("/bdw_ai_customer/queryAiHotels", null, req, headers, new TypeReference<Result<QueryAiHotelResp>>() {
        });
    }

    public <T> Mono<T> webClientPost(String url, Map<String, String> paramMap, Object body, Map<String, String> headers, TypeReference<T> typeReference) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)){
            paramMap.forEach(params::add);
        }

        // 使用headers方法，它接受一个Consumer<HttpHeaders>
        return webClient.post()
                .uri(userBuilder -> userBuilder.path(url).queryParams(params).build())
                .headers(httpHeaders -> {
                    // 添加请求头
                    if (CollUtil.isNotEmpty(headers)) {
                        headers.forEach(httpHeaders::add);
                    }
                })
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->{
                    log.error("Remote service returned error status: {}", response.statusCode());
                    return response.bodyToMono(String.class)
                            .doOnNext(errorBody -> log.error("Error response body: {}", errorBody))
                            .flatMap(errorBody ->
                                    Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                            );
                })
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnError(e -> log.error("webClientPost request failed url {} params {} body {}", url, JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .doOnNext(res -> log.info("webClientPost for: url {} params {} body {} resp {}",url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(body), JacksonUtils.writeValueAsString(res)))
                .flatMap(result -> {
                    try {
                        WebClientResult errorValue = JacksonUtils.convertValue(result,WebClientResult.class);
                        // 正常响应obj，不一定能转化为WebClientResult；能转化为WebClientResult 的也不一定是响应体
                        if (errorValue!= null && StringUtils.isNotBlank(errorValue.getCode()) && StringUtils.isNotBlank(errorValue.getMsg()) && !errorValue.isSuccess()){
                            return Mono.error(new BusinessException(errorValue.getCode(), errorValue.getMsg()));
                        }
                    } catch (Exception e) {
                        log.error("webClientPost result parse error {}", JacksonUtils.writeValueAsString(result));
                    }
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    log.info("webClientPost result {}", JacksonUtils.writeValueAsString(resultData));
                    return Mono.just(resultData);
                })
                .switchIfEmpty(Mono.empty());
    }

    // 为了保持向后兼容，添加一个不带headers参数的重载方法
    public <T> Mono<T> webClientPost(String url, Map<String, String> paramMap, Object body, TypeReference<T> typeReference) {
        return webClientPost(url, paramMap, body, null, typeReference);
    }
    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    private static class WebClientResult {
        private String code;
        private String msg;
        private String traceId;

        public boolean isSuccess() {
            return  ResultCode.SUCCESS.getCode().equals(this.code);
        }

    }


}
