package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

import java.io.*;
import java.util.*;

/**
 * 用户酒店信息
 * 包含用户关联的酒店列表和酒店绑定类型
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserHotelInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店绑定类型
     * 0: 没有酒店
     * 1: 有1间酒店
     * 2: 大于1间酒店
     */
    private Integer bindHotelType;

    /**
     * 关联的酒店信息列表
     */
    private List<HotelInfo> hotels;

}