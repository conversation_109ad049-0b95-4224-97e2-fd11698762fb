package com.wormhole.hotelds.api.hotel.config;

import cn.hutool.extra.spring.SpringUtil;
import com.wormhole.hotelds.api.hotel.constant.HotelDsApiConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @date 2025-03-18 11:14:02
 * @Description:
 */
@Configuration
public class HotelDsApiConfig {


    @Bean(HotelDsApiConstant.HOTEL_DS_API_WEB_CLIENT)
    public WebClient rtcWebClient(Environment environment) {
        return WebClient.builder().baseUrl(getIngress())
                .build();
    }


    private String getIngress() {
        String DEV_INGRESS = "http://wormhole-hotelds-api.delonix.dev";
        String TEST_INGRESS = "http://wormhole-hotelds-api.delonix.test";
        String PROD_INGRESS = "http://wormhole-hotelds-api.delonix.prod";
        String INTL_PROD_INGRESS = "http://wormhole-hotelds-api.delonix-intl.prod";
        String TASK_INGRESS = "http://wormhole-hotelds-api.delonix.task";

        String activeProfile = SpringUtil.getActiveProfile();

        return switch (activeProfile) {
            case "test" -> TEST_INGRESS;
            case "prod" -> PROD_INGRESS;
            case "intl-prod" -> INTL_PROD_INGRESS;
            case "task" -> TASK_INGRESS;
            default -> DEV_INGRESS;
        };

    }


}
