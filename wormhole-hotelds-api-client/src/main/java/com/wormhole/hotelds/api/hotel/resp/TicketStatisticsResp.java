package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工单统计响应数据
 * 
 * <AUTHOR>
 * @Date 2025/7/29
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketStatisticsResp implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 酒店编码
     */
    private String hotelCode;
    
    /**
     * 酒店名称
     */
    private String hotelName;
    
    /**
     * 异常工单数量（紧急/客诉/预警）
     */
    private Integer abnormalCount;
    
    /**
     * 正常工单数量（闭环等级为2，且非异常类）
     */
    private Integer normalCount;
}
