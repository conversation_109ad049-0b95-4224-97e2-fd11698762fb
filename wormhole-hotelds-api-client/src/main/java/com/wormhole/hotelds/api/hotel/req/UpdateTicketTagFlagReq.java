package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Request for updating ticket tag flag
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTicketTagFlagReq implements Serializable {
    /**
     * Ticket number
     */
    private String ticketNo;
    
    /**
     * Tag flag (0=no tag, 1=already assisted, 2=priority mark)
     */
    private Integer tagFlag;
}
