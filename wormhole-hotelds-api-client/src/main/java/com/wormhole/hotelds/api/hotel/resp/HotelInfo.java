package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

import java.io.*;

/**
 * 酒店信息
 * 包含单个酒店的详细信息
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店代码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 是否需要进入接单修改页
     * 1: 需要进入接单修改页设置接单范围
     * 0: 不需要进入接单修改页
     */
    private Integer needTicketModify;
} 