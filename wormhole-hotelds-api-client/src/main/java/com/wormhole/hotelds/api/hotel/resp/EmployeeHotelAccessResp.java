package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 *
* @Author：flx
* @Date：2025/6/6  15:51
* @Description：EmployeeHotelAccessResp
*/
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EmployeeHotelAccessResp {

    private Boolean hasAccess;

    private String message;
}
