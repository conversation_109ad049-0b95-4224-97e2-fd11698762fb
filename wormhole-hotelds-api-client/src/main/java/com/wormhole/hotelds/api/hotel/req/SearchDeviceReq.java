package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchDeviceReq {
    /**
     * 设备类型 （前台设备 & 客房设备）
     */
    private String deviceType;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 设备code
     */
    private String deviceId;

    /**
     * 房间唯一编码
     */
    private String positionCode;

    /**
     * 员工账号类型：1总机 2分机
     */
    private Integer employeeType;

    /**
     * 分机处理的工单类型
     */
    private String ticketCategory;
}
