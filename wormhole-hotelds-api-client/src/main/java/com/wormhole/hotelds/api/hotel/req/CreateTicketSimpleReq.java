package com.wormhole.hotelds.api.hotel.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/23
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateTicketSimpleReq implements Serializable {
    private String rtcRoomId;

    private String deviceId;

    private String clientType;

    private String positionCode;

    private String hotelCode;

    private String rtcUserId;

    private Integer closedLoopLevel;

    private String completedBy;

    private String completedByName;

}
