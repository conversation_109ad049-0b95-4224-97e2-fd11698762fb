package com.wormhole.hotelds.api.hotel.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MatchWormholeRoomsResp implements Serializable {
    /**
     * 跳转类型：1-直接进入H5会话，2-展示确认弹窗，3-直接进入扫码
     */
    private Integer jumpType;

    /**
     * 虫洞酒店编码（jumpType=1,2时返回）
     */
    private String hotelCode;

    /**
     * 房间位置编码（jumpType=1,2时返回）
     */
    private String positionCode;

    private String jumpUrl;


    public static MatchWormholeRoomsResp of(Integer jumpType, String hotelCode, String positionCode,String jumpUrl) {
        MatchWormholeRoomsResp dto = new MatchWormholeRoomsResp();
        dto.setJumpType(jumpType);
        dto.setHotelCode(hotelCode);
        dto.setPositionCode(positionCode);
        dto.setJumpUrl(jumpUrl);
        return dto;
    }
}
