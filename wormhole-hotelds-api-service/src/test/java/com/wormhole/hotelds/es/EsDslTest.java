package com.wormhole.hotelds.es;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.model.entity.PluginCommentReportRecord;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsRecordStateEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.param.PluginCommentSearchParams;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsMonthReportVo;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsWeekReportVo;
import com.wormhole.hotelds.plugin.repository.PluginCommentReportRecordRepository;
import com.wormhole.hotelds.plugin.service.PluginCommentIndexService;
import com.wormhole.hotelds.plugin.service.PluginCommentReportRecordService;
import com.wormhole.hotelds.plugin.service.PluginCommentStatisticsService;
import com.wormhole.hotelds.plugin.service.PluginTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-20 19:29:41
 * @Description:
 */
@ActiveProfiles("dev")
@SpringBootTest(classes = {Application.class})
@Slf4j
public class EsDslTest {

    @Resource
    private PluginCommentIndexService pluginCommentIndexService;

    @Resource
    private PluginCommentProperties pluginCommentProperties;

    @Resource
    private PluginCommentStatisticsService pluginCommentStatisticsService;
    @Autowired
    private PluginCommentReportRecordRepository pluginCommentReportRecordRepository;

    @Autowired
    private PluginTaskService pluginTaskService;

    @Test
    public void avgIntervalCommentScore() {
        PluginCommentReportParams param = new PluginCommentReportParams();
        param.setYear(2025);
        param.setMonth(4);


        Integer monthsToCompare = pluginCommentProperties.getMonthsToCompare();
        Integer yearToCompare = pluginCommentProperties.getYearToCompare();

        // 根据年月设置开始、结束时间
        param.initDateTimeRangeByMonth();

        // 今年 近6月
        PluginCommentSearchParams thisYearSearchParams = param.cloneWithOffsetStartDateTime(-monthsToCompare, DateField.MONTH);
        // 往年
        PluginCommentSearchParams pluginCommentSearchParams = thisYearSearchParams.cloneWithOffsetDateTime(-yearToCompare, DateField.YEAR);

        System.out.println(11);
    }

    public static void main(String[] args) {
        Date date = DateUtil.parseDate("2025-03-03");
        System.out.println(DateUtil.weekOfYear(date));
    }

    @Test
    public void reportTest() {

        PluginCommentReportParams pluginCommentReportParams = new PluginCommentReportParams();
        pluginCommentReportParams.setYear(2025);
        pluginCommentReportParams.setMonth(4);
        pluginCommentReportParams.setHotelCode("HCBCF8G");
        pluginCommentReportParams.setReportIntervalType(PluginCommentStatisticsIntervalEnum.MONTH.getValue());

//        Mono<PluginCommentStatisticsWeekReportVo> pluginCommentStatisticsWeekReportVoMono
//                = pluginCommentStatisticsService.buildWeekReportData(pluginCommentReportParams);
//
//        PluginCommentStatisticsWeekReportVo block = pluginCommentStatisticsWeekReportVoMono.block();

        PluginCommentStatisticsMonthReportVo block = pluginCommentStatisticsService.buildMonthReportData(pluginCommentReportParams).block();

        System.out.println(block);


    }

    @Test
    public void test0001(){
        Long hw4MBMR = pluginTaskService.getHotelLastCollectionTime("HW4MBMR").block();

    }

    @Resource
    private PluginCommentReportRecordService pluginCommentReportRecordService;

    @Test
    public void test12312() {
        log.info("开始测试方法");

//        Flux<Boolean> booleanFlux = pluginCommentReportRecordService.listByStartIdAndState(
//                        0L, PluginCommentStatisticsRecordStateEnum.INIT.getValue(), 5
//                )
//                .doOnNext(record -> {
//                    log.info("获取到记录: {}", record.getId());
//                })
//                .doOnError(e -> {
//                    log.error("获取记录时出错", e);
//                })
//                .flatMap(record -> {
//                    log.info("开始处理记录: {}", record.getId());
//                    return pluginCommentStatisticsService.doCreateReport(record)
//                            .doOnNext(result -> log.info("记录 {} 处理结果: {}", record.getId(), result))
//                            .doOnError(e -> log.error("处理记录 {} 时出错", record.getId(), e));
//                });
//
//        // 重要：需要阻塞等待完成，否则方法可能在异步操作完成前就返回了
//        try {
//            List<Boolean> results = booleanFlux
//                    .doOnComplete(() -> log.info("所有记录处理完成"))
//                    .doOnError(e -> log.error("处理过程中出现错误", e))
//                    .collectList()
//                    .block();
//            log.info("最终结果: {}", results);
//        } catch (Exception e) {
//            log.error("执行过程中出现异常", e);
//        }
    }
}
