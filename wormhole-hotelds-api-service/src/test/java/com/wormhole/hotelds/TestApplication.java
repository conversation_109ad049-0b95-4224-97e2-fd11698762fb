package com.wormhole.hotelds;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * TestConfig
 *
 * <AUTHOR>
 * @version 2025/1/9
 */
@SpringBootConfiguration
@EnableAutoConfiguration
@EnableConfigurationProperties()
@ConfigurationPropertiesScan("com.wormhole")
@ComponentScan(basePackages = {"com.wormhole"})
public class TestApplication {
}
