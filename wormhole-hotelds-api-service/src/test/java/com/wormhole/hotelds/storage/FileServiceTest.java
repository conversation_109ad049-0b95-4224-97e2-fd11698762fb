//package com.wormhole.hotelds.storage;
//
//import cn.hutool.core.collection.CollUtil;
//import co.elastic.clients.elasticsearch._types.query_dsl.*;
//import com.wormhole.agent.core.util.Querys;
//import com.wormhole.common.util.JacksonUtils;
//import com.wormhole.hotelds.BaseTest;
//import com.wormhole.hotelds.plugin.model.param.PluginCommentSearchParams;
//import com.wormhole.hotelds.plugin.model.dto.PluginCommentSentimentAggregationDto;
//import com.wormhole.hotelds.plugin.service.PluginCommentIndexService;
//import com.wormhole.hotelds.storage.config.BucketProperties;
//import com.wormhole.hotelds.storage.file.FileAdapter;
//import com.wormhole.hotelds.storage.file.FileInput;
//import com.wormhole.hotelds.storage.model.*;
//import com.wormhole.storage.model.ObjectMetaData;
//import com.wormhole.storage.model.StorageParams;
//import com.wormhole.storage.service.OssObjectStorageService;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.elasticsearch.client.elc.*;
//import org.springframework.data.elasticsearch.core.*;
//import org.springframework.data.util.Pair;
//
//import java.io.File;
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//
//class FileServiceTest extends BaseTest {
//
//    private static final String HOTEL_CODE_LIST_PATH = "bdw/bdw-hotel-manage/hotel_info/%s/hotel_codes_%s.json";
//
//    @Resource
//    private FileService fileService;
//    @Resource
//    private OssObjectStorageService ossObjectStorageService;
//    @Resource
//    private BucketProperties bucketProperties;
//    @Autowired
//    private PluginCommentIndexService pluginCommentIndexService;
//
//    @Test
//    void testGetContent() {
//        String date = "2025-01-18";
//        String objectKey = String.format(HOTEL_CODE_LIST_PATH, date, date);
//        String content = fileService.getObjectContent(bucketProperties.getCommonBucketName(), objectKey, StandardCharsets.UTF_8.name()).block();
//        System.out.println(content);
//    }
//
//    @Test
//    void testUpload() {
//
////        String filePath = System.getProperty("user.home") + "/Documents/ai-paper/2409.14924v1.pdf";
//        String filePath = "/Users/<USER>/file/图标/腾讯云.png";
//        System.out.println("filePath: " + filePath);
//        File file = new File(filePath);
//        FileInput fileInput = new FileAdapter(file);
//        FileUploadResultDTO fileUploadResult = fileService.upload(ObjectFileTypeEnum.img.name(), fileInput).block();
//        System.out.println(JacksonUtils.writeValueAsString(fileUploadResult));
//    }
//
//    @Test
//    void testDownload() {
//        StorageParams storageParams = StorageParams.builder()
//                .bucketName(bucketProperties.getCommonBucketName())
//                .objectKey("wormhole/docs/20250120154926151a52ef536f.pdf")
//                .build();
//        String filePath = System.getProperty("user.home") + "/Documents/ai-paper/2409.14924-test.pdf";
//        File file = new File(filePath);
//        ObjectMetaData objectMetaData = ossObjectStorageService.getObject(storageParams, file).block();
//        System.out.println(JacksonUtils.writeValueAsString(objectMetaData));
//    }
//
//    @Test
//    void testDelete() {
//        StorageParams storageParams = StorageParams.builder()
//                .bucketName(bucketProperties.getCommonBucketName())
//                .objectKey("wormhole/docs/20250120154926151a52ef536f.pdf")
//                .build();
//        ossObjectStorageService.deleteObject(storageParams).block();
//    }
////
////    @Resource
////    private PluginCommentService pluginCommentService;
////
////    @Test
////    public void test111() {
////        String json = "{\"id\":\"3\",\"created_at\":\"2023-10-03 08:45:30\",\"updated_at\":\"2023-10-03 08:45:30\",\"created_by\":\"user3\",\"created_by_name\":\"Michael Brown\",\"updated_by\":\"user3\",\"updated_by_name\":\"Michael Brown\",\"comment_id\":\"comment_003\",\"hotel_code\":\"HOTEL_789\",\"username\":\"guest3\",\"check_in_date\":\"2023-10\",\"comment_date\":\"2023-10-03 08:45:30\",\"room_type\":\"Suite\",\"comment_channel\":\"Website\",\"comment_score\":4.9,\"environment_score\":4.8,\"facilities_score\":4.7,\"service_score\":4.9,\"hygiene_score\":4.9,\"comment_content\":\"完美的入住体验，设施一流，服务无可挑剔。\",\"result_comment_content\":\"用户对入住体验极为满意。\",\"hotel_reply_content\":\"感谢您的高度评价，期待您的再次光临！\",\"reply_date\":\"2023-10-04 11:15:00\",\"reply_department\":\"Customer Service\",\"scoreable\":true,\"row_status\":1,\"analysis_status\":2,\"platform\":\"Online\",\"sentiment_analysis\":{\"keywords\":[\"完美\",\"设施一流\",\"服务无可挑剔\"],\"sentiment\":\"positive\",\"score\":0.99,\"main_focus\":\"service\"},\"label_analysis\":[{\"tags\":[{\"id\":101,\"level\":1,\"name\":\"服务\"},{\"id\":106,\"level\":2,\"name\":\"前台服务\"},{\"id\":107,\"level\":3,\"name\":\"前台服务周到\"}],\"sentiment\":\"positive\",\"score\":0.95}],\"comment_length\":55,\"reply_length\":30,\"reply_staff\":\"Charlie\",\"time_to_reply\":1300,\"reply_relevance\":95,\"rating_category\":\"High\"}";
////
////
////        PluginCommentIndex pluginCommentIndex = JacksonUtils.readValue(json, PluginCommentIndex.class);
//    /// /        Map<String, Object> stringObjectMap = JacksonUtils.readValue(json);
//    /// /        System.out.println(11);
////
////
////        Mono<PluginCommentIndex> save = pluginCommentService.save(pluginCommentIndex);
////        save.block();
////    }
//
//    @Resource
//    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;
//
//    private Query buildCommonConditionQuery() {
//        return new Query.Builder()
//                .bool(t ->
//                        t.filter(Querys.range("comment_date", "2023-01-01 00:00:00", "2026-02-01 00:00:00"))
//                                .filter(Querys.terms("platform", CollUtil.toList("Online")))
//                                .filter(Querys.terms("hotel_code", CollUtil.toList("HOTEL_789")))
//                )
//                .build();
//    }
//
//    @Resource
//    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;
//
//
//    @Test
//    public void test111() {
////        ReactiveSearchHits<PluginCommentIndex> block = getCommentAggregations().block();
////
////        AggregationsContainer<?> aggregations = block.getAggregations();
////        ElasticsearchAggregation avgCommentScore = ((ElasticsearchAggregations) aggregations).get("avg_comment_score");
////        Aggregate aggregate = avgCommentScore.aggregation().getAggregate();
////
////        AvgAggregate avg = aggregate.avg();
////
////
////        ElasticsearchAggregation overdueNotReplyCount = ((ElasticsearchAggregations) aggregations).get("comment_score_range");
////        System.out.println(111);
//        PluginCommentSearchParams pluginCommentSearchParams = new PluginCommentSearchParams();
//        pluginCommentSearchParams.setStartDateTime("2023-01-01 00:00:00");
//        pluginCommentSearchParams.setEndDateTime("2026-01-01 00:00:00");
//        pluginCommentSearchParams.setHotelCode("HC001");
//        pluginCommentSearchParams.setChannels(CollUtil.toList("ZhiXing"));
//
//        List<Pair<String, Long>> block = pluginCommentIndexService.getLabelsWithSentiment(pluginCommentSearchParams, "负面")
//                .block();
//
//        System.out.println(1);
//    }
//
//    /**
//     * 获取评论聚合数据
//     *
//     * @return
//     */
//    @Test
//    public void df() {
//        PluginCommentSearchParams pluginCommentSearchParams = new PluginCommentSearchParams();
//        pluginCommentSearchParams.setStartDateTime("2023-01-01 00:00:00");
//        pluginCommentSearchParams.setEndDateTime("2026-01-01 00:00:00");
//        pluginCommentSearchParams.setHotelCode("HC001");
//        pluginCommentSearchParams.setChannels(List.of("Elong"));
//        PluginCommentSentimentAggregationDto block = pluginCommentIndexService.getSentimentAggregation(pluginCommentSearchParams).block();
//        System.out.println(block);
//
//    }
//
//
//
////    @Test
////    public void performCommentAnalysis() {
////        // 构建查询条件
////
////        Query query = buildCommonConditionQuery();
////
////        // 构建聚合
////        // 点评数
////        ValueCountAggregation totalCommentCountAgg = AggregationBuilders
////                .valueCount()
////                .field("id")
////                .build();
////
////        // 评论分范围
////
////        AggregationBuilders.fi
////
////        AggregationBuilders
////                .range()
////                .field("comment_score")
////                .ranges()
////                ;
////
////        new Aggregation(totalCommentCountAgg)
////        // 平均评分
////        AverageAggregation commentScore = AggregationBuilders
////                .avg()
////                .field("comment_score")
////                .build();
////
////        // 构建查询
////        NativeQuery nativeQuery = NativeQuery.builder()
////                .withQuery(query)
////                .withAggregation("total_comment_count",commentScore._toAggregation())
////                .build();
////
////        RangeAggregationBuilder commentScoreRangeAgg = AggregationBuilders
////                .range("comment_score_range")
////                .field("comment_score")
////                .addRange("bad_comment_score", 0, 2)
////                .addRange("middle_comment_score", 2, 3.5)
////                .addRange("good_comment_score", 3.5, 10)
////                .subAggregation(AggregationBuilders
////                        .filter("reply_count_agg", QueryBuilders.existsQuery("reply_date"))
////                        .subAggregation(AggregationBuilders
////                                .count("reply_count")
////                                .field("reply_date")));
////        // 平均评分
////        AvgAggregationBuilder avgCommentScoreAgg = AggregationBuilders
////                .avg("avg_comment_score")
////                .field("comment_score");
////        // 环境分范围
////        RangeAggregationBuilder environmentScoreRangeAgg = AggregationBuilders
////                .range("environment_score_range")
////                .field("environment_score")
////                .addRange("bad_environment_score", 0, 2)
////                .addRange("middle_environment_score", 2, 3.5)
////                .addRange("good_environment_score", 3.5, 10);
////        // 平均环境分
////        AvgAggregationBuilder avgEnvironmentScoreAgg = AggregationBuilders
////                .avg("avg_environment_score")
////                .field("environment_score");
////        // 服务分范围
////        RangeAggregationBuilder facilitiesScoreRangeAgg = AggregationBuilders
////                .range("facilities_score_range")
////                .field("facilities_score")
////                .addRange("bad_facilities_score", 0, 2)
////                .addRange("middle_facilities_score", 2, 3.5)
////                .addRange("good_facilities_score", 3.5, 10);
////        // 平均服务分
////        AvgAggregationBuilder avgFacilitiesScoreAgg = AggregationBuilders
////                .avg("avg_facilities_score")
////                .field("facilities_score");
////        // 卫生分范围
////        RangeAggregationBuilder hygieneScoreRangeAgg = AggregationBuilders
////                .range("hygiene_score_range")
////                .field("hygiene_score")
////                .addRange("bad_hygiene_score", 0, 2)
////                .addRange("middle_hygiene_score", 2, 3.5)
////                .addRange("good_hygiene_score", 3.5, 10);
////        // 平均卫生分
////        AvgAggregationBuilder avgHygieneScoreAgg = AggregationBuilders
////                .avg("avg_hygiene_score")
////                .field("hygiene_score");
////        // 评论字数范围
////        RangeAggregationBuilder commentLengthRangeAgg = AggregationBuilders
////                .range("comment_length_range")
////                .field("comment_length")
////                .addRange("short_comment_range", 0, 50)
////                .addRange("long_comment_range", 50, Integer.MAX_VALUE);
////        // 评论时效性范围
////        RangeAggregationBuilder timeToCommentRangeAgg = AggregationBuilders
////                .range("time_to_comment_range")
////                .field("time_to_comment")
////                .addRange("short_time_to_comment_range", 0, 24)
////                .addRange("long_time_to_comment_range", 24, Integer.MAX_VALUE);
////        // 回复时效性范围
////        RangeAggregationBuilder timeToReplyRangeAgg = AggregationBuilders
////                .range("time_to_reply_range")
////                .field("time_to_reply")
////                .addRange("short_time_to_reply_range", 0, 24)
////                .addRange("long_time_to_reply_range", 24, Integer.MAX_VALUE);
////        // 总回复数
////        ValueCountAggregationBuilder totalReplyCountAgg = AggregationBuilders
////                .count("total_reply_count")
////                .field("reply_date");
////        // 回复相关度平均值
////        AvgAggregationBuilder avgReplyRelevanceAgg = AggregationBuilders
////                .avg("avg_reply_relevance")
////                .field("reply_relevance");
////        // 平均回复字数
////        AvgAggregationBuilder avgReplyLengthAgg = AggregationBuilders
////                .avg("avg_reply_length")
////                .field("reply_length");
////        // 逾期未回复数
////        BoolQueryBuilder overdueNotReplyFilter = QueryBuilders.boolQuery()
////                .mustNot(QueryBuilders.existsQuery("reply_date"))
////                .filter(QueryBuilders.rangeQuery("comment_date")
////                        .lte("now-24h"));
////        // 逾期回复数
////        BoolQueryBuilder overdueReplyFilter = QueryBuilders.boolQuery()
////                .filter(QueryBuilders.rangeQuery("time_to_reply").gte(24));
////
////
////
////        // 执行查询
////        Flux<? extends AggregationContainer<?>> aggregate = reactiveElasticsearchTemplate.aggregate(nativeQuery, Map.class);
////        System.out.println("查询结果: " + searchHits);
////    }
////
////    private Aggregation createCommentScoreRangeAggregation() {
////        return Aggregation.of(a -> a
////                .range(r -> r
////                        .field("comment_score")
////                        .ranges(
////                                rr -> rr.key("bad_comment_score").to(2.0),
////                                rr -> rr.key("middle_comment_score").from(2.0).to(3.5),
////                                rr -> rr.key("good_comment_score").from(3.5).to(10.0)
////                        )
////                        .aggregations("reply_count_agg", subAgg -> subAgg
////                                .filter(f -> f
////                                        .filter(Query.of(q -> q
////                                                .exists(ExistsQuery.of(e -> e
////                                                        .field("reply_date")
////                                                ))
////                                        ))
////                                )
////                                .aggregations("reply_count", countAgg -> countAgg
////                                        .valueCount(vc -> vc
////                                                .field("reply_date")
////                                        )
////                                )
////                        )
////                )
////        );
////    }
//}