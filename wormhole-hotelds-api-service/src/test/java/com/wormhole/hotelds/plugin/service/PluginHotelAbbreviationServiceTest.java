package com.wormhole.hotelds.plugin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.dto.PluginHotelAbbreviationDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginHotelAbbreviationServiceTest {
    @Resource
    private PluginHotelAbbreviationService pluginHotelAbbreviationService;

    @Test
    void getHotelAbbreviation() throws JsonProcessingException {
        PluginHotelAbbreviationDTO pluginHotelAbbreviationDTO = new PluginHotelAbbreviationDTO();
        pluginHotelAbbreviationDTO.setExternalHotelId("HC001");
        pluginHotelAbbreviationDTO.setPlatform("Ctrip");
        pluginHotelAbbreviationDTO.setChannel("Ctrip");
        pluginHotelAbbreviationDTO.setHotelName("北京万豪酒店");
        String jsonResponse = pluginHotelAbbreviationService.executeWorkFlow(pluginHotelAbbreviationDTO).block();

        System.out.println("酒店简写: " + jsonResponse);
    }

}