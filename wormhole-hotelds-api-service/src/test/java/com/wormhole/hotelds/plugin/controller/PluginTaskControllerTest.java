package com.wormhole.hotelds.plugin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.knowledge.model.dto.OperatorInfo;
import com.wormhole.agent.knowledge.model.dto.PluginHotelDTO;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.api.hotel.util.ResourceFileUtils;
import com.wormhole.hotelds.plugin.model.dto.TaskDTO;
import com.wormhole.task.model.dto.EbkSingleCommentDTO;
import com.wormhole.task.model.dto.HotelPriceDTO;
import com.wormhole.task.model.dto.PluginOrderJsonDTO;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import dev.langchain4j.service.V;
import jakarta.annotation.Resource;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginTaskControllerTest {
    @Resource
    private PluginTaskController pluginTaskController;

    private static PluginHotelDTO hotelDTO = new PluginHotelDTO();

    private static OperatorInfo operatorInfo = new OperatorInfo();

    static {
        operatorInfo.setHotelCode("CTRIP1");
        operatorInfo.setHotelName("测试携程酒店");
        operatorInfo.setOperatorId("system1");
        operatorInfo.setOperatorName("系统");
//        operatorInfo.setExternalHotels();
    }

    static {
        String hotelPolicyJson = "{\n" +
                "        \"date\": {\n" +
                "            \"title\": \"入住及退房\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"title\": \"入住时间\",\n" +
                "                    \"description\": \"入住时间： 14:00后\",\n" +
                "                    \"transkey\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"title\": \"退房时间\",\n" +
                "                    \"description\": \"退房时间： 12:00前\",\n" +
                "                    \"transkey\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"beds\": {\n" +
                "            \"title\": \"儿童及加床\",\n" +
                "            \"description\": \"酒店允许客人携带儿童入住。\",\n" +
                "            \"menu\": [\n" +
                "                {\n" +
                "                    \"title\": \"使用现有床铺\",\n" +
                "                    \"description\": [\n" +
                "                        \"儿童可使用现有床铺\"\n" +
                "                    ],\n" +
                "                    \"details\": [\n" +
                "                        {\n" +
                "                            \"title\": \"12岁或以下的儿童\",\n" +
                "                            \"content\": [\n" +
                "                                {\n" +
                "                                    \"title\": \"费用\",\n" +
                "                                    \"content\": \"使用现有床铺免费\",\n" +
                "                                    \"bold\": true\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"dining\": {\n" +
                "            \"title\": \"早餐\",\n" +
                "            \"description\": \"\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"title\": \"菜单类型\",\n" +
                "                    \"description\": \"自助餐\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"title\": \"费用\",\n" +
                "                    \"description\": \"{0}¥68.00{/0} 每位\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"title\": \"营业时间\",\n" +
                "                    \"description\": \"06:30-09:30 [星期一 - 星期日]\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"addBreakfastDesc\": \"自助早餐（需付¥68.00）\",\n" +
                "            \"isProvide\": true,\n" +
                "            \"addBreakfastHover\": \"酒店提供自助早餐，每人每晚¥68.00，可到店加购选择。\"\n" +
                "        },\n" +
                "        \"pets\": {\n" +
                "            \"title\": \"宠物\",\n" +
                "            \"description\": \"不可携带宠物。\"\n" +
                "        },\n" +
                "        \"credit\": {\n" +
                "            \"title\": \"到店付款方式\",\n" +
                "            \"description\": \"酒店接受以下付款方式\",\n" +
                "            \"support\": [\n" +
                "                {\n" +
                "                    \"name\": \"威士(VISA)\",\n" +
                "                    \"type\": \"visa\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"万事达(Master)\",\n" +
                "                    \"type\": \"mastercard\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"大来(Diners Club)\",\n" +
                "                    \"type\": \"dinersclub\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"运通(AMEX)\",\n" +
                "                    \"type\": \"american\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"国内发行银联卡\",\n" +
                "                    \"type\": \"unionpay\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"name\": \"JCB\",\n" +
                "                    \"type\": \"jcb\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"checkInWay\": {\n" +
                "            \"title\": \"入住方式\",\n" +
                "            \"content\": [\n" +
                "                \"前台营业时间：24小时营业\"\n" +
                "            ]\n" +
                "        },\n" +
                "        \"ageLimit\": {\n" +
                "            \"title\": \"年龄限制\",\n" +
                "            \"description\": \"入住办理人需年满18岁\"\n" +
                "        },\n" +
                "        \"specialConcern\": {\n" +
                "            \"title\": \"入住提示\",\n" +
                "            \"description\": \"住宿提供方支持中国（含港澳台）及外国客人入住\"\n" +
                "        }\n" +
                "    }";

        String hotelFacilityJson = "[\n" +
                "        {\n" +
                "            \"type\": \"popular\",\n" +
                "            \"title\": \"热门设施\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"无线WIFI免费\",\n" +
                "                    \"id\": \"102\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 0,\n" +
                "                    \"icon\": \"ic_new_fa_wifi\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"行李寄存\",\n" +
                "                    \"id\": \"97\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 0,\n" +
                "                    \"icon\": \"ic_new_fa_baggage\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"24小时前台\",\n" +
                "                    \"id\": \"140\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_24h_service\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"会议厅\",\n" +
                "                    \"id\": \"6\",\n" +
                "                    \"imgList\": [\n" +
                "                        \"//ak-d.tripcdn.com/images/0206t1200087q7jm4C7DA_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/fd/hotel/g5/M01/F4/23/CggYsFcplmyAWgDOAAEVHtVbsWY792_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/020381200087q7jls9864_R_800_525.jpg\"\n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_business\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"电梯\",\n" +
                "                    \"id\": \"110\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"餐厅\",\n" +
                "                    \"id\": \"147\",\n" +
                "                    \"imgList\": [\n" +
                "                        \"//ak-d.tripcdn.com/images/1mc3u12000cm9sdq49DB8_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0206s1200087q7kuz77CE_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/020461200087q7kvc1E11_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203c12000ifq9g7c2B6F_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0205g12000ifprwda1888_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203712000ifqnx3lD110_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203512000ifq5ykg90ED_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0202t12000ifpvutbF9D8_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0204a12000ifpveg9815E_R_800_525.jpg\"\n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_breakfast\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"general\",\n" +
                "            \"title\": \"交通服务\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"娱乐活动设施\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"康体设施\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"accessibility\",\n" +
                "            \"title\": \"前台服务\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"行李寄存\",\n" +
                "                    \"id\": \"97\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 0,\n" +
                "                    \"icon\": \"ic_new_fa_baggage\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"叫醒服务\",\n" +
                "                    \"id\": \"98\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_wake_up\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"VIP通道入住\",\n" +
                "                    \"id\": \"131\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"24小时前台\",\n" +
                "                    \"id\": \"140\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_24h_service\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"礼宾服务\",\n" +
                "                    \"id\": \"127\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"前台服务语言\",\n" +
                "                    \"id\": \"138\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_polyglot\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"清洁服务\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"multilingual\",\n" +
                "            \"title\": \"餐饮服务\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"餐厅\",\n" +
                "                    \"id\": \"147\",\n" +
                "                    \"imgList\": [\n" +
                "                        \"//ak-d.tripcdn.com/images/1mc3u12000cm9sdq49DB8_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0206s1200087q7kuz77CE_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/020461200087q7kvc1E11_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203c12000ifq9g7c2B6F_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0205g12000ifprwda1888_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203712000ifqnx3lD110_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0203512000ifq5ykg90ED_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0202t12000ifpvutbF9D8_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/0204a12000ifpveg9815E_R_800_525.jpg\"\n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_breakfast\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"售货亭/便利店\",\n" +
                "                    \"id\": \"579\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"public\",\n" +
                "            \"title\": \"公共区\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"公用区wifi\",\n" +
                "                    \"id\": \"102\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 0,\n" +
                "                    \"icon\": \"ic_new_fa_wifi\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"电梯\",\n" +
                "                    \"id\": \"110\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"公共音响系统\",\n" +
                "                    \"id\": \"170\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"公共区域禁烟\",\n" +
                "                    \"id\": \"141\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"无烟楼层\",\n" +
                "                    \"id\": \"173\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_no_smoking\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"机器人服务\",\n" +
                "                    \"id\": \"364\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"商务服务\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"会议厅\",\n" +
                "                    \"id\": \"6\",\n" +
                "                    \"imgList\": [\n" +
                "                        \"//ak-d.tripcdn.com/images/0206t1200087q7jm4C7DA_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/fd/hotel/g5/M01/F4/23/CggYsFcplmyAWgDOAAEVHtVbsWY792_R_800_525.jpg\",\n" +
                "                        \"//ak-d.tripcdn.com/images/020381200087q7jls9864_R_800_525.jpg\"\n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_business\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"多功能厅\",\n" +
                "                    \"id\": \"164\",\n" +
                "                    \"imgList\": [\n" +
                "                        \"//ak-d.tripcdn.com/images/fd/hotel/g5/M04/F6/A4/CggYsVcplW-AUy-dABBc5L2Jo7E790_R_800_525.jpg\"\n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_multimedia\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"传真/复印\",\n" +
                "                    \"id\": \"129\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 1,\n" +
                "                    \"icon\": \"order_printer\",\n" +
                "                    \"value\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"婚宴服务\",\n" +
                "                    \"id\": \"137\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"无障碍设施服务\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"运动\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"\",\n" +
                "            \"title\": \"安全与安保\",\n" +
                "            \"content\": [\n" +
                "                {\n" +
                "                    \"facilityDesc\": \"公共区域监控\",\n" +
                "                    \"id\": \"177\",\n" +
                "                    \"imgList\": [\n" +
                "                        \n" +
                "                    ],\n" +
                "                    \"chargeType\": 2,\n" +
                "                    \"icon\": \"ic_new_fa_check\",\n" +
                "                    \"value\": 1\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"dining\",\n" +
                "            \"title\": \"儿童设施\",\n" +
                "            \"content\": [\n" +
                "                \n" +
                "            ]\n" +
                "        }\n" +
                "    ]";
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode hotelPolicy = mapper.readTree(hotelPolicyJson);
            JsonNode hotelFacility = mapper.readTree(hotelFacilityJson);

//            hotelDTO.setId("1");
//            hotelDTO.setHotelCode("CTRIP1");
//            hotelDTO.setHotelName("测试携程酒店");
//            hotelDTO.setHotelPolicy(hotelPolicy);
//            hotelDTO.setHotelFacility(hotelFacility);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Test
    void getLastTask() {
        TaskDTO taskDTO = new TaskDTO();
        taskDTO.setHotelCode("1");
        pluginTaskController.getLastTask(taskDTO)
                .doOnNext(result -> {
                    System.out.println(result);
                    assertNotNull(result);
                })
                .doOnError(throwable -> {
                    System.err.println("Error: " + throwable.getMessage());
                    fail("Test failed due to an error");
                })
                .block();
    }

    @Test
    void hotelInfoSync() {
        pluginTaskController.hotelInfoSync(hotelDTO)
                .doOnNext(result -> {
                    System.out.println(result);
                    assertNotNull(result);
                })
                .doOnError(throwable -> {
                    System.err.println("Error: " + throwable.getMessage());
                    fail("Test failed due to an error");
                })
                .block();
    }

//    @Test
//    void commentBatchSync(){
//        EbkBatchCommentDTO ebkBatchCommentDTO = new EbkBatchCommentDTO();
//        ebkBatchCommentDTO.setHotelCode("CTRIP1");
//        ebkBatchCommentDTO.setHotelName("测试携程酒店");
//        ebkBatchCommentDTO.setCommentFileUrl("https://file.c-ctrip.com/files/6/hotelcontract/0HN0j12000j6b66gl8F07.xls");
//        ebkBatchCommentDTO.setOperatorInfo(operatorInfo);
//        pluginTaskController.commentBatchSync(ebkBatchCommentDTO).doOnNext(result -> {
//                    System.out.println(result);
//                    assertNotNull(result);
//                })
//                .doOnError(throwable -> {
//                    System.err.println("Error: " + throwable.getMessage());
//                    fail("Test failed due to an error");
//                })
//                .block();
//    }

    @Test
    void commentSingleSync(){
        EbkSingleCommentDTO dto = new EbkSingleCommentDTO();
        dto.setHotelCode("CTRIP1");
        dto.setHotelName("测试携程酒店");
        dto.setBusinessCommentId(DigestUtils.md5Hex(dto.getCommentContent()+dto.getCommentDate()+dto.getUsername()));
        dto.setCommentChannel("ctrip");
        dto.setBusinessCommentId("BC12345");
        dto.setUsername("John Doe");
        dto.setCheckInDate("2025-03");
        dto.setCommentDate(LocalDateTime.of(2025, 4, 7, 12, 0));
        dto.setRoomType("Standard Room");
        dto.setCommentChannel("ctrip");
        dto.setCommentScore(BigDecimal.valueOf(4.5));
        dto.setEnvironmentScore(BigDecimal.valueOf(4.0));
        dto.setFacilitiesScore(BigDecimal.valueOf(4.2));
        dto.setServiceScore(BigDecimal.valueOf(4.6));
        dto.setHygieneScore(BigDecimal.valueOf(4.3));
        dto.setCommentContent("This is a great hotel!");
        dto.setHotelReplyContent("Thank you for your feedback!");
        dto.setReplyDate(LocalDateTime.of(2025, 4, 8, 10, 0));
        dto.setReplyDepartment("Customer Service");
        dto.setReplyStaff("Jane Smith");
        dto.setOperatorInfo(operatorInfo);
        pluginTaskController.commentSingleSync(dto).doOnNext(result -> {
                    System.out.println(result);
                    assertNotNull(result);
                })
                .doOnError(throwable -> {
                    System.err.println("Error: " + throwable.getMessage());
                    fail("Test failed due to an error");
                })
                .block();
    }

    @Test
    void hotelPriceSync() {
        HotelPriceDTO hotelPriceDTO = new HotelPriceDTO();
        hotelPriceDTO.setHotelCode("H0001");
        hotelPriceDTO.setHotelName("test H001");
        hotelPriceDTO.setPlatform("Ctrip");
        hotelPriceDTO.setChannel("Ctrip");
        hotelPriceDTO.setPhysicRoomMap(null);
        pluginTaskController.hotelPriceSync(hotelPriceDTO)
                .doOnNext(result -> {
                    System.out.println(result);
                    assertNotNull(result);
                })
                .doOnError(throwable -> {
                    System.err.println("Error: " + throwable.getMessage());
                    fail("Test failed due to an error");
                })
                .block();
    }

    @Test
    void orderSync() throws JsonProcessingException {
        PluginOrderJsonDTO pluginOrderJsonDTO = new PluginOrderJsonDTO();
        pluginOrderJsonDTO.setChannel("Ctrip");
        pluginOrderJsonDTO.setPlatform("Ctrip");
        pluginOrderJsonDTO.setHotelCode("HC001");
        pluginOrderJsonDTO.setHotelName("安徽高速酒店");

        String order = ResourceFileUtils.readFile("local/json/order.json");
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(order);
        pluginOrderJsonDTO.setOrder(jsonNode);
        Result<TaskScheduleDetailVO> block = pluginTaskController.orderSync(pluginOrderJsonDTO).block();
        System.out.println(block);
    }
}