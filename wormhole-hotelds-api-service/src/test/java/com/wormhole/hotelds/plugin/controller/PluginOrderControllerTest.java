package com.wormhole.hotelds.plugin.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.api.hotel.util.ResourceFileUtils;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderFormDateDTO;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderRecordDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginOrderRecordEntity;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTimeVO;
import com.wormhole.task.model.constant.PluginPlatformEnum;
import com.wormhole.task.model.dto.PluginCtripOrderDTO;
import com.wormhole.task.model.dto.PluginOrderJsonDTO;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import com.wormhole.task.model.vo.PluginOrderVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nd4j.shade.jackson.databind.JsonNode;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
public class PluginOrderControllerTest {
    @Resource
    private PluginOrderController pluginOrderController;


    @Test
    void getFormDate() {
        PluginOrderFormDateDTO pluginOrderFormDateDTO = new PluginOrderFormDateDTO();
        pluginOrderFormDateDTO.setHotelCode("HZEJNYJ");
        pluginOrderFormDateDTO.setPlatform("Ctrip");
        Result<PluginOrderTimeVO> block = pluginOrderController.getFromDate(pluginOrderFormDateDTO).block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

    @Test
    void fsd(){
        PluginOrderRecordDTO pluginOrderRecordDTO = new PluginOrderRecordDTO();
        pluginOrderRecordDTO.setPlatform(PluginPlatformEnum.CTRIP.getCode());
        pluginOrderRecordDTO.setHotelCode("HZEJNYJ");
        pluginOrderRecordDTO.setSourceType("Ebooking");
        pluginOrderRecordDTO.setIsEnd(true);
        Result<PluginOrderRecordEntity> block = pluginOrderController.record(pluginOrderRecordDTO).block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

    @Test
    void orderSync() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        String order = ResourceFileUtils.readFile("local/json/order.json");
        PluginOrderJsonDTO  pluginOrderJsonDTO =new PluginOrderJsonDTO();
        pluginOrderJsonDTO.setPlatform(PluginPlatformEnum.CTRIP.getCode());
        pluginOrderJsonDTO.setChannel("Ctrip");
        pluginOrderJsonDTO.setHotelCode("HC001");
        pluginOrderJsonDTO.setHotelName("测试酒店");
        pluginOrderJsonDTO.setOrder(objectMapper.readTree(order));

        Result<PluginOrderIndex> block = pluginOrderController.orderSync(pluginOrderJsonDTO).block();
        System.out.println(JacksonUtils.writeValuePretty(block));
    }

//    @Test
//    void queryList() {
//        PluginOrderQo pluginOrderQo = new PluginOrderQo();
//        pluginOrderQo.setHotelCode("HC001");
//        pluginOrderQo.setPlatform("Ctrip");
//        Result<PageResult<PluginOrderVO>> block = pluginOrderController.queryList(pluginOrderQo).block();
//        System.out.println("block = " + JacksonUtils.writeValueAsString(block));
//    }
}
