package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.param.PluginSimpleCommentSearchParams;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginSimpleCommentIndexServiceTest {
    @Resource
    private PluginSimpleCommentIndexService pluginSimpleCommentIndexService;

//    @Test
//    void negativeReviewDetail() {
//        PluginSimpleCommentSearchParams params = new PluginSimpleCommentSearchParams();
//        params.setHotelCode("HC001");
//        params.setStartDate("2025-04-01");
//        params.setEndDate("2025-06-01");
//        params.setTopN(1);
//        pluginSimpleCommentIndexService.negativeReviewDetail(params)
//                .doOnNext(System.out::println)
//                .block();
//    }
}