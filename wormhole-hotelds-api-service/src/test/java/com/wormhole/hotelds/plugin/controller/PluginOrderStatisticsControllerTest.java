package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTodayReportVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
public class PluginOrderStatisticsControllerTest {
    @Resource
    private PluginOrderStatisticsController pluginOrderStatisticsController;

    @Test
    void todayReport() {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode("HC001");
        pluginOrderQo.setPlatform("Ctrip");
        Result<PluginOrderTodayReportVO> block = pluginOrderStatisticsController.todayReport(pluginOrderQo).block();
        System.out.println("block = " + block);
    }
}
