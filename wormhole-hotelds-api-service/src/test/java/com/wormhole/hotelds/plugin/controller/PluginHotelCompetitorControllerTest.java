package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.dto.HotelCompetitorDTO;
import com.wormhole.hotelds.plugin.model.vo.HotelCompetitorVO;
import com.wormhole.hotelds.plugin.service.HotelCompetitorService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginHotelCompetitorControllerTest {
    @Resource
    private PluginHotelCompetitorController pluginHotelCompetitorController;
    @Resource
    private HotelCompetitorService hotelCompetitorService;

    @Test
    void addCompetitor() {
        HotelCompetitorDTO hotelCompetitorDTO = new HotelCompetitorDTO();
        hotelCompetitorDTO.setHotelCode("HQJS5XN");
        hotelCompetitorDTO.setCompetitorHotelId("1631575");
        hotelCompetitorDTO.setCompetitorHotelName("全季酒店(诸暨西施故里城市广场店)");
        hotelCompetitorDTO.setChannel("Ctrip");
        hotelCompetitorDTO.setPlatform("Ctrip");
        Result<HotelCompetitorVO> block = pluginHotelCompetitorController.addCompetitor(hotelCompetitorDTO).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    void batchAddCompetitor() {
        HotelCompetitorDTO hotelCompetitorDTO = new HotelCompetitorDTO();
        hotelCompetitorDTO.setHotelCode("test1");
        hotelCompetitorDTO.setCompetitorHotelId("444");
        hotelCompetitorDTO.setCompetitorHotelName("上海陆家嘴金融中心苹果酒店");
        hotelCompetitorDTO.setChannel("Ctrip");
        hotelCompetitorDTO.setPlatform("Ctrip");
        hotelCompetitorDTO.setSelectedStatus(0);
        hotelCompetitorDTO.setViewRate(3.0);

        HotelCompetitorDTO hotelCompetitorDTO1 = new HotelCompetitorDTO();
        hotelCompetitorDTO1.setHotelCode("test1");
        hotelCompetitorDTO1.setCompetitorHotelId("555");
        hotelCompetitorDTO1.setCompetitorHotelName("上海李家汇中心城际酒店");
        hotelCompetitorDTO1.setChannel("Ctrip");
        hotelCompetitorDTO1.setPlatform("Ctrip");
        hotelCompetitorDTO1.setSelectedStatus(0);
        hotelCompetitorDTO1.setViewRate(2.1);


        Result<List<HotelCompetitorVO>> block = pluginHotelCompetitorController.batchAddCompetitor(List.of(hotelCompetitorDTO, hotelCompetitorDTO1)).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    void listCompetitors() {
        String hotelCode="HTJOSYA";
        Integer selectAll =1;
        List<HotelCompetitorVO> block = hotelCompetitorService.listCompetitors(hotelCode, selectAll).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    void cancel() {
        pluginHotelCompetitorController.cancel("260").block();
    }

    @Test
    void testListCompetitors() {
        Result<List<HotelCompetitorVO>> block = pluginHotelCompetitorController.listCompetitors("test1", 1).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

//    @Test
//    void dfd(){
//        hotelCompetitorService.findCompetitorByUniqueKeys("test1","222","Ctrip")
//                .subscribe(competitor -> {
//                    System.out.println(competitor);
//                    assertNotNull(competitor);
//                });
//    }
}