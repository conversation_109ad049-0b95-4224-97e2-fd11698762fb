package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentSentimentEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.vo.AiInsight;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginCommentStatisticsServiceTest {
    @Resource
    private PluginCommentStatisticsService pluginCommentStatisticsService;

    @Test
    void llmApi() {
        Map<String,String> llmMap = Map.of("label", "前台服务差","last_week_count","1","two_week_age_count","4","content","前台服务一般;前台小慧不爱搭理人;前台工作人员服务态度差劲;前台小王专业素质差");
        Map<String,String> llmMap1 = Map.of("label", "离地铁站近","last_week_count","1","two_week_age_count","4","content","酒店就在地铁站附近，很方便;酒店就在地铁二号线，很容易找;很适合外面人旅游入住，离地铁站近;这位置爱了，就在地铁站旁边");

//        String block = pluginCommentStatisticsService.llmApi(llmMap, PluginCommentSentimentEnum.NEGATIVE.getCode()).block();
        String block1 = pluginCommentStatisticsService.llmApi(llmMap1, PluginCommentSentimentEnum.POSITIVE.getCode()).block();

//        System.out.println(block);
        System.out.println(block1);
    }

    @Test
    void aiInsight() {
        PluginCommentReportParams pluginCommentReportParams = new PluginCommentReportParams();
        pluginCommentReportParams.setHotelCode("HC001");
        pluginCommentReportParams.setYear(2025);
        pluginCommentReportParams.setWeek(10);

        pluginCommentReportParams.setStartDateTime("2023-03-01 00:00:00");
        pluginCommentReportParams.setEndDateTime("2025-05-31 23:59:59");

        AiInsight block = pluginCommentStatisticsService.aiInsight(pluginCommentReportParams).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

}