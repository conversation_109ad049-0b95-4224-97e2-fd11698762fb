package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.Application;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class PluginDoorControllerTest {
    @Resource
    private PluginDoorController pluginDoorController;

    @Test
    void saveLabel() {
        PluginCommentLabelEntity entity = new PluginCommentLabelEntity();
        entity.setId(50114l);
        entity.setLeaf(true);
        entity.setName("测试标签");
        entity.setLevel((short) 4);
        entity.setParentId(5011l);
        entity.setRowStatus(1);
        entity.setCreatedBy("system");
        entity.setUpdatedBy("system");
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setCreatedAt(LocalDateTime.now());

        Result<PluginCommentLabelEntity> block = pluginDoorController.saveLabel(entity).block();
        System.out.println(block);
    }
}