package com.wormhole.hotelds.plugin.service;

import com.google.common.collect.Lists;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.plugin.model.dto.HotelPriceQueryDTO;
import com.wormhole.hotelds.plugin.model.vo.HotelMinPriceVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
class HotelPriceIndexServiceTest {
    @Resource
    private HotelPriceIndexService hotelPriceIndexService;

    @Test
    void getMinHotelPrices() {
        List<HotelMinPriceVO> block = hotelPriceIndexService.getMinHotelPrices("HC0001",Lists.newArrayList("2204223", "448747"), "2025-05-22").block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }
    @Test
    void getHotelHistoryMinPrice() {
        List<HotelMinPriceVO> block = hotelPriceIndexService.getHotelHistoryMinPrice("HC0001", Lists.newArrayList("2204223", "448747"), "2025-05-22").block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    void queryHotelPriceDetails() {
        HotelPriceQueryDTO hotelPriceQueryDTO = new HotelPriceQueryDTO();
        hotelPriceQueryDTO.setHotelCode("HC008");
        hotelPriceQueryDTO.setCompetitorHotelId("448747");
        hotelPriceQueryDTO.setPriceDate("2025-05-22");
        List<HotelMinPriceVO> block = hotelPriceIndexService.queryHotelPriceDetails(hotelPriceQueryDTO).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }
}