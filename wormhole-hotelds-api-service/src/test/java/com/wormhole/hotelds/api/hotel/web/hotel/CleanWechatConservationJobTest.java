package com.wormhole.hotelds.api.hotel.web.hotel;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.api.hotel.job.CleanWechatConservationJob;
import com.wormhole.hotelds.api.hotel.web.model.req.GetTopNTicketReq;
import com.wormhole.hotelds.api.hotel.web.model.res.FocusTicketResp;
import com.wormhole.hotelds.api.hotel.web.service.TicketAdminService;
import com.wormhole.hotelds.api.hotel.web.service.WechatUserService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 11:17
 */
@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
public class CleanWechatConservationJobTest {

    @Resource
    private CleanWechatConservationJob cleanWechatConservationJob;

    @Resource
    private TicketAdminService ticketAdminService;

    @Resource
    private WechatUserService wechatUserService;

    @Test
    void testgenerateQRCodeAndUpload(){
        wechatUserService.generateQRCodeAndUpload("H001","101").block();
    }
    @Test
    void topN(){
        GetTopNTicketReq req = new GetTopNTicketReq();
        req.setHotelCode("H001");
        req.setStatus(1);
        req.setLimit(3);
        List<FocusTicketResp> block = ticketAdminService.getTopNFocusTickets(req).block();
        System.out.println(JacksonUtils.writeValueAsString( block));
    }
    @Test
    void testCleanWechatConservation() {
        cleanWechatConservationJob.cleanYesterdayWechatConservation();
    }
}
