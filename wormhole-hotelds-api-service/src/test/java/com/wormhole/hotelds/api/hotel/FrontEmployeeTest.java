package com.wormhole.hotelds.api.hotel;

import com.google.common.collect.Sets;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.api.hotel.web.model.req.EmployeeSingleReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetStaffRosterReq;
import com.wormhole.hotelds.api.hotel.web.model.req.SaveReceiveConfigReq;
import com.wormhole.hotelds.api.hotel.web.model.res.ReceiveTicketBlankResp;
import com.wormhole.hotelds.api.hotel.web.model.res.StaffRosterResp;
import com.wormhole.hotelds.api.hotel.web.service.EmployeeTicketMappingService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
@Slf4j
public class FrontEmployeeTest {
    @Autowired
    private EmployeeTicketMappingService employeeTicketMappingService;

    @Test
    public void q(){
        EmployeeSingleReq req = EmployeeSingleReq.builder()
                .userId("2")
                .hotelCode("HC001")
                .build();
        ReceiveTicketBlankResp block = employeeTicketMappingService.receiveTicketBlanks(req).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    public void s(){
        EmployeeSingleReq req = EmployeeSingleReq.builder()
                .userId("2")
                .hotelCode("HC001")
                .build();
        SaveReceiveConfigReq req1 = SaveReceiveConfigReq.builder()
                .ticketCategories(Sets.newHashSet("DELIVERY","CLEANING"))
                .areaCodes(Sets.newHashSet("HC001A8","DACJB9ULV"))
                .build();
        Boolean block = employeeTicketMappingService.saveReceiveTicketConfig(req1,req).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    public void testAssignTicketBlanks() {
        String hotelCode = "HC001";
        List<String> ticketBlanks = employeeTicketMappingService.assignTicketBlanks(hotelCode).block();
        System.out.println(JacksonUtils.writeValueAsString(ticketBlanks));
    }

    @Test
    public void getStaffRoster(){
        GetStaffRosterReq req = new GetStaffRosterReq();
        req.setBlock("A");
        StaffRosterResp hc001 = employeeTicketMappingService.getStaffRoster(req, "HC001").block();
        System.out.println(JacksonUtils.writeValueAsString(hc001));

    }

    @Test
    public void init(){
        employeeTicketMappingService.initAreaCodes().block();
    }

}
