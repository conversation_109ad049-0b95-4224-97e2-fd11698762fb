package com.wormhole.hotelds.api.hotel.job;

import com.wormhole.hotelds.api.hotel.web.service.HotelDailyAiStatisticsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.test.StepVerifier;

import java.time.LocalDate;

/**
 * 酒店每日AI统计任务测试
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@SpringBootTest
public class HotelAiDailyStatsJobTest {

    @Autowired
    private HotelDailyAiStatisticsService statisticsService;

    @Test
    public void testGenerateDailyStatistics() {
        // 测试统计昨天的数据
        LocalDate yesterday = LocalDate.now().minusDays(1);
        
        StepVerifier.create(statisticsService.generateDailyStatistics(yesterday))
                .verifyComplete();
    }

    @Test
    public void testGenerateSpecificDateStatistics() {
        // 测试统计指定日期的数据
        LocalDate specificDate = LocalDate.of(2025, 1, 26);
        
        StepVerifier.create(statisticsService.generateDailyStatistics(specificDate))
                .verifyComplete();
    }
} 