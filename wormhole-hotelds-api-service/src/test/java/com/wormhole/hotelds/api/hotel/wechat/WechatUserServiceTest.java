//package com.wormhole.hotelds.api.hotel.wechat;
//
//import com.wormhole.hotelds.Application;
//import com.wormhole.storage.autoconfigure.ObjectStorageProperties;
//import com.wormhole.storage.model.ObjectMetaData;
//import com.wormhole.storage.model.StorageParams;
//import com.wormhole.storage.service.OssObjectStorageService;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.imageio.ImageIO;
//import java.awt.*;
//import java.awt.image.BufferedImage;
//import java.io.File;
//import java.io.IOException;
//
///**
// * <AUTHOR>
// * @date 2025/4/9 19:58
// */
//@ActiveProfiles("dev")
//@SpringBootTest(classes = {Application.class})
//public class WechatUserServiceTest {
//
////    @Autowired
////    private OssObjectStorageService ossObjectStorageService;
////
////    @Autowired
////    private ObjectStorageProperties bucketProperties;
//
//    @Test
//    void putObject() {
//        StorageParams build = StorageParams.builder().bucketName("bdw-test").objectKey("wormhole/hotelds/api/README.md").build();
//        //项目根目录下的文件
//        File file = new File("src/main/resources/README.md");
//        ObjectMetaData block = ossObjectStorageService.putObject(build, file).block();
//        System.out.println(block);
//    }
//
//    @Test
//    void getObject() {
//        StorageParams build = StorageParams.builder().bucketName("bdw-test").objectKey("wormhole/hotelds/api/qrcode_101_1744254686457.png").build();
//        ObjectMetaData block1 = ossObjectStorageService.getObjectMetadata(build).block();
//        String block2 = ossObjectStorageService.generatePresignedUrl(build);
//        System.out.println(block1);
//        System.out.println(block2);
//    }
//
//    @Test
//    void createAndUploadQRCodeWithBackground() {
//        try {
//            File backgroundImageFile = new File("src/main/resources/background.jpeg");
//            File qrFile = new File("src/main/resources/wechatmini.jpeg");
//
//            // 1. 将二维码与背景图合成
//            BufferedImage backgroundImage = ImageIO.read(backgroundImageFile);
////                        BufferedImage qrImage = ImageIO.read(new ByteArrayInputStream(qrBytes));
//            BufferedImage qrImage = ImageIO.read(qrFile);
//
//            int x = (backgroundImage.getWidth() - qrImage.getWidth()) / 2;
//            int y = (backgroundImage.getHeight() - qrImage.getHeight()) / 2;
//
//            Graphics2D g = backgroundImage.createGraphics();
//            g.drawImage(qrImage, 330, 440, null);
//            g.dispose();
//
//            // 2. 保存为临时文件
//            String fileName = "/qrcode_101_" + System.currentTimeMillis() + ".png";
//            File tempFile = new File(System.getProperty("java.io.tmpdir"), fileName);
//            ImageIO.write(backgroundImage, "png", tempFile);
//
//            // 3. 构造存储参数
//            StorageParams storageParams = StorageParams.builder()
//                    .bucketName("bdw-test")
//                    .objectKey("wormhole/hotelds/api"+fileName)
//                    .build();
//
//            // 4. 上传并删除临时文件
//            ossObjectStorageService.putObject(storageParams, tempFile)
//                    .doFinally(signal -> tempFile.delete()).block();
//            System.out.println("QR code uploaded successfully: " + storageParams.getObjectKey());
//            String block2 = ossObjectStorageService.generatePresignedUrl(storageParams);
//            System.out.println(block2);
//        } catch (IOException e) {
//            System.out.println("Error creating or uploading QR code: " + e.getMessage());
//
//        }
//    }
//
//
//}
