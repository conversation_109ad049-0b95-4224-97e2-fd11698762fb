package com.wormhole.hotelds.api.hotel.web.hotel;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.Application;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.web.service.HotelMappingService;
import com.wormhole.hotelds.api.hotel.web.service.ServiceTicketService;
import com.wormhole.hotelds.api.hotel.web.service.TicketAdminService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:30
 */
@ActiveProfiles("dev")
@SpringBootTest(classes = {Application.class})
public class HotelMappingServiceTest {
    @Resource
    private HotelMappingService hotelMappingService;

    @Resource
    private ServiceTicketService serviceTicketService;

    @Resource
    private TicketAdminService ticketAdminService;

//    @Test
//    void testHotelMapping() {
//        HotelMappingRes mappingRes = hotelMappingService.getHotelMapping("HC001",null).block();
//        System.out.println(JacksonUtils.writeValueAsString(mappingRes));
//    }


    @Test
    void getPage() {
        TicketAdminPageReq req = TicketAdminPageReq.builder()
                .current(1)
                .pageSize(10)
                .hotelCode("H001")
                .build();
        PageResult<TicketAdminListResp> block = ticketAdminService.getTicketPage(req).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

}
