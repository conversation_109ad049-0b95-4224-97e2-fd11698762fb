{"form": {"layout": "vertical", "labelCol": {"span": 4}, "wrapperCol": {"span": 12}}, "schema": {"type": "object", "properties": {"back_config": {"type": "object", "x-validator": [], "name": "back_config", "title": "back_config", "x-designable-id": "k5tkflneod6", "x-index": 0, "properties": {"text": {"type": "string", "title": "文本", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "x-designable-id": "ftdax1r4t2y", "x-index": 0, "name": "text", "description": ""}, "#img_url": {"type": "Array<object>", "title": "图片", "x-decorator": "FormItem", "x-component": "Upload", "x-component-props": {"textContent": "Upload", "listType": "picture-card", "action": "/api/file/uploadFile", "maxCount": 1, "data": "{{{\"oss_file_type\": \"app\"}}}"}, "x-validator": [], "x-decorator-props": {}, "x-designable-id": "hg8fbuhdasu", "x-index": 1, "name": "#img_url", "x-reactions": {"dependencies": [], "fulfill": {"run": "$props({\n  onPreview(file) {\n    console.log(\"image_preview...file=\" + file)\n    $form.notify(\"image_preview\", file)\n  },\n})\n\n$effect(() => {\n  console.log($self, $self.value, $values)\n  const value = $self.value\n  const file = value && value.length > 0 ? value[0] : null\n  if (file) {\n    const fileUploadResult = file?.response?.data\n    if (file.status === \"done\" && fileUploadResult) {\n      console.log(`${file.name} file uploaded successfully.`)\n      $self.value = [fileUploadResult]\n      $values[\"back_config\"][\"img_url\"] = fileUploadResult.url\n    } else if (file.status === \"error\") {\n      console.error(`${file.name} file upload failed.`)\n    }\n  }\n}, [$self.value])\n"}}}}}, "custom_config": {"type": "object", "x-validator": [], "name": "custom_config", "title": "custom_config", "x-designable-id": "ys11e72yer3", "x-index": 1, "properties": {"padding": {"type": "object", "x-validator": [], "name": "padding", "title": "padding", "x-designable-id": "j9qlzjojp6w", "x-index": 0, "properties": {"top": {"type": "number", "title": "顶部偏移", "x-decorator": "FormItem", "x-component": "NumberPicker", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "top", "x-designable-id": "v7v246cpxo2", "x-index": 0}, "left": {"type": "number", "title": "左部偏移", "x-decorator": "FormItem", "x-component": "NumberPicker", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "left", "x-designable-id": "28s3l2sxizx", "x-index": 1}, "right": {"type": "number", "title": "右部偏移", "x-decorator": "FormItem", "x-component": "NumberPicker", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "right", "x-designable-id": "ogw5fpy7hey", "x-index": 2}, "bottom": {"type": "number", "title": "底部偏移", "x-decorator": "FormItem", "x-component": "NumberPicker", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "bottom", "x-designable-id": "djqa<PERSON><PERSON><PERSON><PERSON>", "x-index": 3}}}}}, "type": {"title": "类型", "x-decorator": "FormItem", "x-component": "Select", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "type", "enum": [{"children": [], "label": "appbar_1", "value": "appbar_1"}], "x-designable-id": "ib7woxumxzb", "x-index": 2}, "action_configs": {"type": "array", "x-decorator": "FormItem", "x-component": "ArrayCards", "x-component-props": {"title": "", "bordered": true}, "x-validator": [], "x-decorator-props": {}, "x-designable-id": "kd3pptw1sig", "x-index": 3, "name": "action_configs", "title": "action_configs", "items": {"type": "object", "x-designable-id": "xyob3rij9ze", "properties": {"zhby1q6szpo": {"type": "void", "x-component": "ArrayCards.Index", "x-designable-id": "zhby1q6szpo", "x-index": 0}, "type": {"type": "string", "title": "类型", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "type", "x-designable-id": "4oadx3xnw7w", "x-index": 1}, "text": {"type": "string", "title": "文本", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "text", "x-designable-id": "0a6bhswn77x", "x-index": 2}, "uri": {"type": "string", "title": "协议", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "uri", "x-designable-id": "pap0oqx4yb3", "x-index": 3}, "params": {"type": "object", "x-validator": [], "name": "params", "title": "params", "x-designable-id": "woku2lqjcgb", "x-index": 4, "properties": {"page_code": {"type": "string", "title": "页面编码", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "page_code", "x-designable-id": "4vs1m7uf5yz", "x-index": 0}}}, "#img_url": {"type": "Array<object>", "title": "图片", "x-decorator": "FormItem", "x-component": "Upload", "x-component-props": {"textContent": "Upload", "listType": "picture-card", "action": "/api/file/uploadFile", "maxCount": 1, "data": "{{{\"oss_file_type\": \"app\"}}}"}, "x-validator": [], "x-decorator-props": {}, "x-designable-id": "a9ilgckw45s", "x-index": 5, "name": "#img_url", "x-reactions": {"dependencies": [], "fulfill": {"run": "$props({\n  onPreview(file) {\n    console.log(\"image_preview...file=\" + file)\n    $form.notify(\"image_preview\", file)\n  },\n})\n\n$effect(() => {\n  console.log($self.parent, $self.value, $values)\n  const value = $self.value\n  const file = value && value.length > 0 ? value[0] : null\n  if (file) {\n    const fileUploadResult = file?.response?.data\n    if (file.status === \"done\" && fileUploadResult) {\n      console.log(`${file.name} file uploaded successfully.`)\n      $self.value = [fileUploadResult]\n      $self.parent.value[\"img_url\"] = fileUploadResult.url\n    } else if (file.status === \"error\") {\n      console.error(`${file.name} file upload failed.`)\n    }\n  }\n}, [$self.value])\n"}}}, "ri609grs8s8": {"type": "void", "title": "Addition", "x-component": "ArrayCards.Remove", "x-designable-id": "ri609grs8s8", "x-index": 6}, "wg3y9eifeif": {"type": "void", "title": "Addition", "x-component": "ArrayCards.MoveDown", "x-designable-id": "wg3y9eifeif", "x-index": 7}, "islsfvp6cp8": {"type": "void", "title": "Addition", "x-component": "ArrayCards.MoveUp", "x-designable-id": "islsfvp6cp8", "x-index": 8}}}, "properties": {"buuasqaipry": {"type": "void", "title": "Addition", "x-component": "ArrayCards.Addition", "x-designable-id": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x-index": 0}}}, "title_config": {"type": "object", "x-validator": [], "x-designable-id": "shaw8jht6y9", "x-index": 4, "name": "title_config", "title": "title_config", "properties": {"text": {"type": "string", "title": "标题文本", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "name": "text", "x-designable-id": "oder1twdy4v", "x-index": 0}}}}, "x-designable-id": "4drfkq6qnx0"}}