请根据以下评论相关变量，对用户的差评进行深入分析，并提出可行的改进建议。最后以 JSON 格式输出，字段包括 comment_analysis（问题分析）和 comment_suggestion（优化建议）。
差评关键词：${label}
本周出现次数：${last_week_count}
上周出现次数：${two_week_age_count}
相关评论内容如下：
${content}
分析要求：
概括主要问题点（如：房间隔音差影响睡眠体验）；
指出评论中体现的用户真实痛点和使用场景；
结合数据变化趋势，说明问题是否在加重；
提出至少1-2条可执行的优化建议，如设施改进、服务调整等。
输出格式要求（JSON）：
{
"comment_analysis": "",
"comment_suggestion": ["",""]
}
请根据上述内容填充 comment_analysis 和 comment_suggestion，语言自然，逻辑清晰，尽量简洁明了。