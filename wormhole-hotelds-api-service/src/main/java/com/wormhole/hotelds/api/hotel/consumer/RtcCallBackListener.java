package com.wormhole.hotelds.api.hotel.consumer;

import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.service.handler.RtcCallbackService;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Component
@RocketMQMessageListener(topic = "${rocketmq.topic.callEventInfo:call_event_info_topic}",
        consumerGroup = "${rocketmq.consumer.callEventInfo:hotelds_api_call_event_info_group}")
@Slf4j
public class RtcCallBackListener extends AbstractReactiveMessageListener<MessageBody> {
    @Autowired
    private RtcCallbackService rtcCallbackService;

    @Override
    protected Mono<Void> processMessage(MessageBody payload) {
        CallInfoMessage callInfoMessage = JacksonUtils.convertValue(payload.getData(), CallInfoMessage.class);
        log.info("Received RTC call status callback: {}", JacksonUtils.writeValueAsString(callInfoMessage));
        return rtcCallbackService.handleRtcCallStatusCallback(callInfoMessage)
                .doOnSuccess(result -> {
                    log.info("RTC call status callback processed successfully: {}", JacksonUtils.writeValueAsString(callInfoMessage));
                }).doOnError(e -> log.error("Error processing RTC call status callback: {}", JacksonUtils.writeValueAsString(callInfoMessage), e))
                .then();

    }
}
