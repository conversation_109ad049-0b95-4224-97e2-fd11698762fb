package com.wormhole.hotelds.api.hotel.constant;

import cn.hutool.core.date.DatePattern;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
public interface RedisConstant {

    String CONVERSATION_TICKETS_KEY = "wormhole:conversation:tickets:%s";

    String CALLER_TO_CALLEE_COUNT_KEY = "wormhole:caller_to_callee:%s:%s";

    String USER_DETAIL_INFO_KEY = "wormhole:user:info:%s";

    String USER_LOGIN_TOKEN_KEY = "user:login:token:%s:%s";

    String USER_LOGIN_REFRESH_TOKEN_KEY = "user:login:refreshToken:%s:%s";

    String HDS_USER_TOKEN_KEY = "hds:user:token:%s:%s";

    String CALLBACK_CALL_INFO_MESSAGE_KEY = "wormhole:callback:call_info_message:%s:%s";


    String HOTEL_COMMENT_SCAN_KEY = "wormhole:external_hotel_comment_scan:%s";

    String HOTEL_COMMENT_SYNC_LOCK = "hotel_comment_sync_lock:%s";

    String HOTEL_ROOM_CODE_KEY = "wormhole:room_code:%s";

    String GENERATE_ABBREVIATION_HOTEL_NAME = "wormhole:HOTEL:GENERATE_ABBREVIATION_HOTEL_NAME:%s:%s";

    class PluginRedisKeyConstant {

        private static String PREFIX = "wormhole:PLUGIN";


        /**
         * 生成报告的分布式锁
         */
        public static String getCreateReportLockKey(PluginCommentStatisticsIntervalEnum intervalEnum, String hotelCode
                , String channels, LocalDateTime startDateTime, LocalDateTime endDateTime) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
            return String.format("%s:%s:%s:%s:%s:%s:%s", PREFIX, "CREATE_REPORT", intervalEnum.name(), hotelCode, channels
                    , startDateTime.format(formatter), endDateTime.format(formatter));
        }


    }

    String CALL_FEEDBACK_KEY = "hds-api:call_feedback_with_rtc_room_id:%s";

    String ORDER_ROOM_CACHE_KEY  = "wormhole:order_room:%s:%s";

    String HOTEL_DAILY_TICKET_STAT_KEY = "wormhole:hotel:daily_ticket_stat:%s:%s";

    interface DailyTicketField{
        String AI_CALL_COUNT = "ai_call_count";
        String RETURN_CALL_COUNT = "return_call_count";
        String TICKET_COUNT = "ticket_count";
        String COMPLETED_TICKET_COUNT = "completed_ticket_count";
        String INQUIRY_COUNT = "inquiry_count";
        String INQUIRY_COMPLETED_COUNT = "inquiry_completed_count";
        String SERVICE_NEED_COUNT = "service_need_count";
        String SERVICE_NEED_COMPLETED_COUNT = "service_need_completed_count";
        String COMPLAINT_COUNT = "complaint_count";
        String COMPLAINT_COMPLETED_COUNT = "complaint_completed_count";
        String EMERGENCY_COUNT = "emergency_count";
        String EMERGENCY_COMPLETED_COUNT = "emergency_completed_count";
    }
}
