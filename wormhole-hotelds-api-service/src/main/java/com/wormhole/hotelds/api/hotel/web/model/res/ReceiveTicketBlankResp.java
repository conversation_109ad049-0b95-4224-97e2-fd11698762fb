package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveTicketBlankResp {

    /**
     * 工单类型配置标题
     */
    private String categoryTitle;
    /**
     * 工单类型配置描述
     */
    private String categoryDesc;
    /**
     * 工单类型筛选框
     */
    private List<BlankNode> categoryBlanks;
    /**
     * 位置配置标题
     */
    private String positionTitle;
    /**
     * 位置配置描述
     */
    private String positionDesc;
    /**
     * 位置筛选框
     */
    private List<BlankNode> positionBlanks;

}
