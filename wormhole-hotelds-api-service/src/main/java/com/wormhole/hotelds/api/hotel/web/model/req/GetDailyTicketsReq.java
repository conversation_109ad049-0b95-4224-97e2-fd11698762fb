package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetDailyTicketsReq {
    /**
     * 酒店代码
     */
    private String hotelCode;
    /**
     * 业务日期，yyyy-MM-dd，可为空
     */
    private String bizDate;

}