package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * AI消息反馈请求
 * <AUTHOR>
 * @Date 2025/7/30
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CallLogFeedbackReq implements Serializable {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 反馈状态(0未反馈 1:点赞 2:点踩)
     */
    private Integer feedbackStatus;
    
    /**
     * 反馈类型，多个用逗号分隔
     * 例如: "语言表达不清晰,缺乏人情味,回答内容不贴题"
     */
    private String feedbackTypes;
    
    /**
     * 反馈内容(可选)
     */
    private String feedbackContent;
}
