package com.wormhole.hotelds.api.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @author: joker.liu
 * @date: 2025/6/17
 * @Description:
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "wormhole.hotel.knowledge")
public class HotelKnowledgeProperties {

    /**
     * 搜索策略
     */
    private Integer strategy = 2;

    /**
     * embedding模型
     */
    private String embeddingModel = "wormhole/bge-embedding-m3";

    /**
     * rerank模型
     */
    private String rerankModel = "wormhole/bge-reranker-v2-m3";

    /**
     * 是否使用rerank
     */
    private Boolean useRerank = true;

    /**
     * 是否改写
     */
    private Boolean useRewrite = false;

    /**
     * 是否返回文档
     */
    private Boolean returnDocuments = true;

    /**
     * 最大分词数
     */
    private Integer maxChunksPerDoc = 1024;

    /**
     * 覆盖token数
     */
    private Integer overlapTokens = 80;

}
