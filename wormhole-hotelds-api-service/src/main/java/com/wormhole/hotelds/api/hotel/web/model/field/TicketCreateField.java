package com.wormhole.hotelds.api.hotel.web.model.field;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@AllArgsConstructor
@Getter
public enum TicketCreateField {
    SERVICE_CATEGORY("service_category", "工单服务类别"),
    SERVICE_SUBCATEGORY("service_subcategory", "工单服务子类别"),
    GUEST_REQUEST("guest_request", "客人请求详情"),
    HOTEL_CODE("hotel_code", "酒店代码标识符"),
    ROOM_CODE("room_code", "房间代码标识符"),
    RTC_ROOM_ID("rtc_room_id", "RTC房间标识符"),
    DEVICE_ID("device_id", "设备标识符"),
    CONVERSATION_ID("conversation_id", "会话标识符"),
    CLIENT_REQ_ID("client_req_id", "客户端请求标识符"),
    USER_ID("user_id", "用户标识符"),
    USER_NAME("user_name", "用户名称");


    ;
    private String field;
    private String desc;
}
