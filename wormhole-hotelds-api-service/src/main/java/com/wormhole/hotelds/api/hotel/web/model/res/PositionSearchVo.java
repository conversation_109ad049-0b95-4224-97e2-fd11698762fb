package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PositionSearchVo implements Serializable {

    private String positionCode;
    private String hotelCode;
    private String positionFullName;
}
