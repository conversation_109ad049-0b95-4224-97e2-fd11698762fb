package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Preconditions;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.WormholeConfiguration;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.OrderRoomRedisDao;
import com.wormhole.hotelds.api.hotel.constant.JumpTypeEnum;
import com.wormhole.hotelds.api.hotel.resp.MatchWormholeRoomsResp;
import com.wormhole.hotelds.api.hotel.resp.QueryAiHotelResp;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDevicePositionQO;
import com.wormhole.hotelds.api.hotel.req.MatchWormholeRoomsReq;
import com.wormhole.hotelds.api.hotel.req.QueryAiHotelReq;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.enums.ExternalChannelEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.model.qo.HdsHotelMappingQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */

@Service
@Slf4j
public class BdwAiCustomerService {

    @Autowired
    private OrderRoomRedisDao orderRoomRedisDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;
    @Autowired
    private HdsHotelMappingDao hdsHotelMappingDao;
    @Autowired
    private WormholeConfiguration wormholeConfiguration;

    public Mono<MatchWormholeRoomsResp> matchWormholeRooms(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        log.info("matchWormholeRooms request: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsReq));

        String bdwHotelCode = matchWormholeRoomsReq.getBdwHotelCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(bdwHotelCode), "bdwHotelCode不能为空");
        String childOrderNo = matchWormholeRoomsReq.getChildOrderNo();
        Preconditions.checkArgument(StringUtils.isNotBlank(childOrderNo), "childOrderNo不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(matchWormholeRoomsReq.getRoomNo()), "roomNo不能为空");

        return orderRoomRedisDao.get(bdwHotelCode, childOrderNo)
                .flatMap(hotelPositionSimpleDto -> {
                    String hotelCode = hotelPositionSimpleDto.getHotelCode();
                    String positionCode = hotelPositionSimpleDto.getPositionCode();
                    return Mono.just(MatchWormholeRoomsResp.of(JumpTypeEnum.H5_SESSION.getCode(),
                            hotelCode, positionCode,
                            getJumpUrl(JumpTypeEnum.H5_SESSION.getCode(), hotelCode, positionCode, bdwHotelCode, childOrderNo)));
                }).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                .switchIfEmpty(
                        Mono.defer(() -> hdsHotelMappingDao.findOne(getHotelMappingBuild(matchWormholeRoomsReq))
                                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "没有找到对应的BDW酒店")))
                                .flatMap(hdsHotelMappingEntity ->
                                        hdsDevicePositionDao.findOne(getPositionBuild(matchWormholeRoomsReq.getRoomNo(), hdsHotelMappingEntity.getHotelCode()))
                                                .map(hdsDevicePositionEntity ->
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                hdsDevicePositionEntity.getHotelCode(),
                                                                hdsDevicePositionEntity.getPositionCode(),
                                                                getJumpUrl(JumpTypeEnum.CONFIRM_DIALOG.getCode(),
                                                                        hdsDevicePositionEntity.getHotelCode(),
                                                                        hdsDevicePositionEntity.getPositionCode(), bdwHotelCode, childOrderNo))
                                                )
                                                .switchIfEmpty(Mono.just(
                                                        MatchWormholeRoomsResp.of(JumpTypeEnum.SCAN_CODE.getCode(), null, null,null)
                                                        )))
                                ).doOnNext(matchWormholeRoomsResp -> log.info("matchWormholeRooms response: {}", JacksonUtils.writeValueAsString(matchWormholeRoomsResp)))
                        );

    }

    public String getJumpUrl(Integer jumpType, String hotelCode, String positionCode, String bdwHotelCode, String childOrderNo) {
        if (Objects.equals(JumpTypeEnum.H5_SESSION.getCode(), jumpType) || Objects.equals(JumpTypeEnum.CONFIRM_DIALOG.getCode(), jumpType)) {
            return String.format(wormholeConfiguration.getBdwAiChatH5Url(), hotelCode, positionCode, bdwHotelCode, childOrderNo);
        }
        return null;
    }


    public Mono<QueryAiHotelResp> queryAiHotels(QueryAiHotelReq queryAiHotelReq) {
        List<String> bdwHotelCodes = queryAiHotelReq.getBdwHotelCodes();
        Preconditions.checkArgument(CollUtil.isNotEmpty(bdwHotelCodes), " bdwHotelCodes不能为空");
        HdsHotelMappingQO hotelMappingBuild = getHotelMappingBuild(queryAiHotelReq);
        return hdsHotelMappingDao.findList(hotelMappingBuild).map(mappingEntities -> QueryAiHotelResp.of(getAiHotelStatusDtos(mappingEntities, bdwHotelCodes)))
                .switchIfEmpty(Mono.defer(() -> {
                    List<QueryAiHotelResp.AiHotelStatusDto> collect = bdwHotelCodes.stream().map(e -> QueryAiHotelResp.AiHotelStatusDto.of(e, false)).collect(Collectors.toList());
                    return Mono.just(QueryAiHotelResp.of(collect));
                }));

    }

    @NotNull
    private static List<QueryAiHotelResp.AiHotelStatusDto> getAiHotelStatusDtos(List<HdsHotelMappingEntity> mappingEntities, List<String> bdwHotelCodes) {
        Set<String> hasAiExternalIds = mappingEntities.stream()
                .map(HdsHotelMappingEntity::getExternalId)
                .collect(Collectors.toSet());
        List<QueryAiHotelResp.AiHotelStatusDto> aiHotelStatusList = bdwHotelCodes.stream()
                .map(bdwHotelCode -> QueryAiHotelResp.AiHotelStatusDto.of(
                        bdwHotelCode,
                        hasAiExternalIds.contains(bdwHotelCode)
                ))
                .collect(Collectors.toList());
        return aiHotelStatusList;
    }

    @NotNull
    private static HdsDevicePositionQO getPositionBuild(String roomNo, String hotelCode) {
        HdsDevicePositionQO hdsDevicePositionQO = new HdsDevicePositionQO();
        hdsDevicePositionQO.setFullPositionName(roomNo);
        hdsDevicePositionQO.setHotelCode(hotelCode);
        hdsDevicePositionQO.setDeviceAppType(DeviceTypeEnum.ROOM.getCode());
        return hdsDevicePositionQO;
    }

    private static HdsHotelMappingQO getHotelMappingBuild(MatchWormholeRoomsReq matchWormholeRoomsReq) {
        return HdsHotelMappingQO.builder()
                .externalId(matchWormholeRoomsReq.getBdwHotelCode())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

    private static HdsHotelMappingQO getHotelMappingBuild(QueryAiHotelReq queryAiHotelReq) {
        return HdsHotelMappingQO.builder()
                .externalIds(queryAiHotelReq.getBdwHotelCodes())
                .channel(ExternalChannelEnum.BDW.getCode())
                .platform(ExternalChannelEnum.BDW.getCode())
                .build();
    }

}
