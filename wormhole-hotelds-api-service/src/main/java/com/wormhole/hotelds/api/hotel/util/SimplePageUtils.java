package com.wormhole.hotelds.api.hotel.util;

import com.google.common.base.CaseFormat;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.wormhole.hotelds.api.hotel.constant.OrderDirectionEnum;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.query.QueryCondition;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class SimplePageUtils {
    /**
     * m
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static int getOffset(int pageNo, int pageSize) {
        Preconditions.checkArgument(pageNo >= 1, "current必须大于等于1");
        Preconditions.checkArgument(pageSize >= 0, "pageSize必须大于等于0");
        return (pageNo - 1) * pageSize;
    }

    /**
     * 检查分页参数是否合法
     *
     * @param queryInfo
     */
    public static void checkPageInfo(QueryCondition queryInfo) {
        Preconditions.checkArgument(Objects.nonNull(queryInfo), "queryInfo must not be null");
        Preconditions.checkArgument(Objects.nonNull(queryInfo.getCurrent()) && queryInfo.getCurrent() >= 1, "current必须大于等于1");
        Preconditions.checkArgument(Objects.nonNull(queryInfo.getPageSize()) && queryInfo.getPageSize() >= 0, "pageSize必须大于等于0");
    }

    /**
     * 对于分页查询的接口，如果用户没有传入分页参数，则初始化分页参数
     *
     * @param queryInfo
     */
    public static void initPageInfo(QueryCondition queryInfo) {
        Preconditions.checkArgument(Objects.nonNull(queryInfo), "queryInfo must not be null");
        if (Objects.isNull(queryInfo.getCurrent())) {
            queryInfo.setCurrent(1);
        }
        if (Objects.isNull(queryInfo.getPageSize())) {
            queryInfo.setPageSize(SystemConstant.DEFAULT_PAGE_SIZE);
        }
    }

    public static <T> List<T> getPageList(List<T> dataList, int pageNo, int pageSize) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        int size = dataList.size();
        Integer offset = getOffset(pageNo, pageSize);
        int fromIndex = Math.min(offset, size);
        int toIndex = Math.min(fromIndex + pageSize, size);
        return dataList.subList(fromIndex, toIndex);
    }

    public static String getOrderByClause(QueryCondition queryInfo, String defaultValue) {
        Preconditions.checkArgument(Objects.nonNull(queryInfo), "queryInfo must not be null");
        if (StringUtils.isNotBlank(queryInfo.getOrderByClause())) {
            return queryInfo.getOrderByClause();
        }
        if (StringUtils.isNotBlank(queryInfo.getSort())) {
            OrderDirectionEnum orderDirectionEnum = OrderDirectionEnum.valueFrom(queryInfo.getOrder(), OrderDirectionEnum.DESC);
            // 将前端传入的驼峰格式转为下划线模式
            return Joiner.on(" ").join(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, queryInfo.getSort()), orderDirectionEnum.name());
        }
        return defaultValue;
    }

    public static String getOrderByClause(QueryCondition queryInfo) {
        return getOrderByClause(queryInfo, null);
    }

    public static Integer getOffset(QueryCondition queryInfo) {
        Preconditions.checkArgument(Objects.nonNull(queryInfo), "queryInfo must not be null");
        if (Objects.nonNull(queryInfo.getCurrent()) && Objects.nonNull(queryInfo.getPageSize())) {
            return getOffset(queryInfo.getCurrent(), queryInfo.getPageSize());
        }
        return 0;
    }

}