package com.wormhole.hotelds.api.hotel.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum OrderDirectionEnum {
    /**
     * 正序
     */
    ASC("ascend"),
    /**
     * 倒序
     */
    DESC("descend");

    private static final Map<String, OrderDirectionEnum> ITEM_MAP = new HashMap<>(2);

    private static final Map<String, OrderDirectionEnum> ALIAS_ITEM_MAP = new HashMap<>(2);

    static {
        ITEM_MAP.put(OrderDirectionEnum.ASC.name(), OrderDirectionEnum.ASC);
        ITEM_MAP.put(OrderDirectionEnum.DESC.name(), OrderDirectionEnum.DESC);

        ALIAS_ITEM_MAP.put(OrderDirectionEnum.ASC.orderAlias, OrderDirectionEnum.ASC);
        ALIAS_ITEM_MAP.put(OrderDirectionEnum.DESC.orderAlias, OrderDirectionEnum.DESC);
    }

    private String orderAlias;

    OrderDirectionEnum(String orderAlias) {
        this.orderAlias = orderAlias;
    }

    public String getOrderAlias() {
        return orderAlias;
    }

    public static String convert(String order) {
        if (StringUtils.isBlank(order)) {
            return order;
        }
        OrderDirectionEnum orderDirectionEnum = ALIAS_ITEM_MAP.get(order);
        if (Objects.nonNull(orderDirectionEnum)) {
            return orderDirectionEnum.name();
        }
        return order;
    }

    public static OrderDirectionEnum defaultDescIfBlank(String order) {
        order = convert(order);
        if (StringUtils.isBlank(order)) {
            return OrderDirectionEnum.DESC;
        }
        OrderDirectionEnum item = ITEM_MAP.get(order.toUpperCase());
        return item != null ? item : OrderDirectionEnum.DESC;
    }

    public static OrderDirectionEnum valueFrom(String order) {
        order = convert(order);
        if (StringUtils.isNotBlank(order)) {
            return ITEM_MAP.get(order.toUpperCase());
        }
        return null;
    }

    public static OrderDirectionEnum valueFrom(String order, OrderDirectionEnum defaultValue) {
        order = convert(order);
        if (StringUtils.isBlank(order)) {
            return defaultValue;
        }
        OrderDirectionEnum value = ITEM_MAP.get(order.toUpperCase());
        return value != null ? value : defaultValue;
    }

    public static boolean isValid(String order) {
        order = convert(order);
        if (StringUtils.isBlank(order)) {
            return false;
        }
        return ITEM_MAP.containsKey(order.toUpperCase());
    }
}