package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import java.time.LocalDate;

/**
 * 服务满意度趋势数据VO
 * 用于返回酒店每周服务满意度趋势统计
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceSatisfactionTrendVO {
    /** 本周开始日期（周一） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate weekStart;
    /** 本周结束日期（周日） */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate weekEnd;
    /** 年中的第几周（ISO标准） */
    private int weekOfYear;
    /** 本周内工单的总数 */
    private int totalTicketCount;
    /** 本周内投诉工单总数 */
    private int complaintTicketCount;
    /** 本周内客诉预警工单总数 */
    private int warningTicketCount;
    /** 本周内OTA差评总数 */
    private int otaNegativeCount;
    /** 满意度比率，保留两位小数（如0.95表示95%） */
    private double satisfactionRate;
} 