package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025/4/24
 */
@Repository
public class CallbackInfoRedisDao {

    @Autowired
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


    public Mono<Boolean> saveCallInfoMsg(String rtcRoomId, RtcCallStatusEnum rtcCallStatusEnum, CallInfoMessage request){
        String key = String.format(RedisConstant.CALLBACK_CALL_INFO_MESSAGE_KEY, rtcRoomId,rtcCallStatusEnum.getCode());
        return reactiveRedisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(request), Duration.ofMinutes(30));
    }

    public Mono<Boolean> hasCallInfoMsg(String rtcRoomId, RtcCallStatusEnum rtcCallStatusEnum){
        String key = String.format(RedisConstant.CALLBACK_CALL_INFO_MESSAGE_KEY, rtcRoomId,rtcCallStatusEnum.getCode());
        return reactiveRedisTemplate.hasKey(rtcRoomId);
    }


    public Mono<CallInfoMessage> getCallInfoMsg(String rtcRoomId, RtcCallStatusEnum rtcCallStatusEnum) {
        String key = String.format(RedisConstant.CALLBACK_CALL_INFO_MESSAGE_KEY, rtcRoomId,rtcCallStatusEnum.getCode());
        return reactiveRedisTemplate.opsForValue().get(key)
                .flatMap(value -> {
                    if (StringUtils.isNotBlank(value)) {
                        try {
                            CallInfoMessage message = JacksonUtils.convertValue(value, CallInfoMessage.class);
                            return Mono.just(message);
                        } catch (Exception e) {
                            return Mono.error(e);
                        }
                    }
                    return Mono.empty();
                });
    }

}
