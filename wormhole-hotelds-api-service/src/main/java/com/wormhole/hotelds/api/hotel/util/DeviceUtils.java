package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;

public class DeviceUtils {

    private final static Joiner JOINER = Joiner.on("").skipNulls();
    public static String getPositionFullName(HdsDevicePositionEntity position) {
        if (position == null) return null;
        return JOINER.join(StrUtil.isNotBlank(position.getBlock()) ? (position.getBlock() + " ") : null,
                StrUtil.isNotBlank(position.getArea()) ? (position.getArea() + " ") : null ,
                position.getPositionName());
    }

    public static String getBlockFloorName(HdsDevicePositionEntity position) {
        if (position == null) return null;
        return JOINER.join(StrUtil.isNotBlank(position.getBlock()) ? position.getBlock() : null,
                StrUtil.isNotBlank(position.getArea()) ? position.getArea()  : null);
    }
}
