package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.core.model.entity.HdsTicketLogsEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.List;

@Repository
public interface TicketLogsRepository extends ReactiveCrudRepository<HdsTicketLogsEntity, Long>{

    /**
     * 批量插入
     *
     * @param entities   日志
     * @return Flux<HdsTicketLogsEntity>
     */
    Flux<HdsTicketLogsEntity> saveAll(List<HdsTicketLogsEntity> entities);
}
