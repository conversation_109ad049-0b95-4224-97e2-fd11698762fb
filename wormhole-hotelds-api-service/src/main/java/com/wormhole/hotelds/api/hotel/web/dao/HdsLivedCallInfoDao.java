package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.hotelds.core.model.entity.HdsLivedRtcCallInfo;
import com.wormhole.hotelds.core.model.entity.HdsLivedRtcCallInfoFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;


@Repository
@Slf4j
public class HdsLivedCallInfoDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsLivedRtcCallInfo> insert(HdsLivedRtcCallInfo entity) {
        return r2dbcEntityTemplate.insert(entity);
    }


    public Mono<HdsLivedRtcCallInfo> selectByRtcRoomId(String rtcRoomId) {
        Criteria criteria = Criteria.where(HdsLivedRtcCallInfoFieldEnum.rtc_room_id.name()).is(rtcRoomId);
        Query query = Query.query( criteria).sort(Sort.by(Sort.Direction.DESC, HdsLivedRtcCallInfoFieldEnum.id.name())).limit(1);
        return r2dbcEntityTemplate.selectOne(query, HdsLivedRtcCallInfo.class);

    }

    public Mono<HdsLivedRtcCallInfo> updateEntity(HdsLivedRtcCallInfo message) {
        // todo 仅修改2个状态
        return r2dbcEntityTemplate.update(message);
    }
}
