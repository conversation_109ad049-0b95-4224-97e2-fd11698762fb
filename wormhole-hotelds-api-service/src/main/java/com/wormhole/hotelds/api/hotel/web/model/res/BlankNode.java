package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlankNode {
    /**
     * 节点唯一标识
     */
    private String key;

    /**
     * 节点名称
     */
    private String name;
    /**
     * 节点描述
     */
    private String desc;
    /**
     * 是否选中：1-选中，0-未选中
     */
    private Integer checked;

    /**
     * 是否为叶子节点，为true则子节点列表为空
     */
    private Integer isLeaf;

    /**
     * 子节点列表
     */
    private List<BlankNode> children;

    @JsonIgnore
    private Integer sort;
}
