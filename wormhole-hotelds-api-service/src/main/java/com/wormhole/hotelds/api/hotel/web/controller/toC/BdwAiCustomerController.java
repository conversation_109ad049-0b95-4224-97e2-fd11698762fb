package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.resp.MatchWormholeRoomsResp;
import com.wormhole.hotelds.api.hotel.resp.QueryAiHotelResp;
import com.wormhole.hotelds.api.hotel.req.MatchWormholeRoomsReq;
import com.wormhole.hotelds.api.hotel.req.QueryAiHotelReq;
import com.wormhole.hotelds.api.hotel.config.WormholeConfiguration;
import com.wormhole.hotelds.api.hotel.web.service.BdwAiCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */
@RestController
@RequestMapping("/bdw_ai_customer")
public class BdwAiCustomerController {

    @Autowired
    private BdwAiCustomerService bdwAiCustomerService;
    @Autowired
    private WormholeConfiguration wormholeConfiguration;
    @Autowired
    private TicketProperties ticketProperties;


    @PostMapping("/match_wormhole_rooms")
    public Mono<Result<MatchWormholeRoomsResp>> matchWormholeRooms(@RequestBody MatchWormholeRoomsReq matchWormholeRoomsReq) {
        return bdwAiCustomerService.matchWormholeRooms(matchWormholeRoomsReq).flatMap(Result::success);
    }

    @PostMapping("/query_ai_hotels")
    public Mono<Result<QueryAiHotelResp>> queryAiHotels(@RequestBody QueryAiHotelReq queryAiHotelReq) {
        return bdwAiCustomerService.queryAiHotels(queryAiHotelReq).flatMap(Result::success);
    }

    @GetMapping("/getText")
    public Mono<List<String>> getText() {
        return Mono.just(ticketProperties.getTicketDescList());
    }


}
