package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import java.time.LocalDate;

/**
 * 近7天工单数量趋势数据VO
 * 用于返回指定酒店每日工单数量及相关统计
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketsTrendVO {
    /** 业务统计日期 */
    @JsonFormat(pattern = "MM.dd")
    private LocalDate businessDate;
    /** AI通话数量 */
    private int aiCallCount;
    /** 回拨通话数量 */
    private int returnCallCount;
    /** 总工单数量 */
    private int ticketCount;
    /** 已完成工单数量 */
    private int completedTicketCount;
} 