package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeEntity;
import com.wormhole.hotelds.core.model.entity.HdsWechatQrCodeFieldEnum;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/17 19:11
 */

@Repository
public class HdsWechatQrCodeDao {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsWechatQrCodeEntity> queryByHotelCode(String hotelCode, String positionCode) {
        Criteria criteria = Criteria.where(HdsWechatQrCodeFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsWechatQrCodeFieldEnum.position_code.name()).is(positionCode);

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsWechatQrCodeEntity.class).next();
    }

    public Mono<HdsWechatQrCodeEntity> insertWechatQrCodeEntity(HdsWechatQrCodeEntity qrCodeEntity) {
        return r2dbcEntityTemplate.insert(qrCodeEntity);
    }

    public Mono<HdsWechatQrCodeEntity> findQrCodeByScene(String scene) {
        Criteria criteria = Criteria.where(HdsWechatQrCodeFieldEnum.id.name()).is(scene);
        Query query = Query.query(criteria).limit(1);

        return r2dbcEntityTemplate.selectOne(query, HdsWechatQrCodeEntity.class)
                .switchIfEmpty(Mono.error(new BusinessException("HOTEL-DS-API-001", "二维码无效或已过期")));
    }

}
