package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 聚焦工单响应实体类
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FocusTicketResp {
    /**
     * 工单ID
     */
    private Long ticketId;

    /**
     * 工单no
     */
    private String ticketNo;

    /**
     * 房号
     */
    private String positionFullName;

    /**
     * 问题摘要
     */
    private String guestRequest;

    /**
     * 等待时长(分钟)
     */
    private Integer waitMinutes;

    private String waitMinutesDesc;

    /**
     * 工单类型代码
     */
    private String serviceCategory;

    /**
     * 工单类型显示
     */
    private String serviceCategoryName;

    /**
     * 工单类型显示简称
     */
    private String serviceCategoryShow;

    /**
     * 客诉预警；超时
     */
    private List<String> focusTagText;

    private Integer overdueFlag;

    /**
     * 标签颜色：紫红（急）、红（客诉）、黄（客诉预警）、橙（超时）
     */
    private String focusTagColor;

    private String wordColor;

    /**
     * 创建时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String createdAt;

    /**
     * 完成时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String completedTime;
}