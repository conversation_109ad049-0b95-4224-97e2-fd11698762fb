package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.config.TicketProperties;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.enums.TicketFeedbackDimension;
import com.wormhole.hotelds.api.hotel.req.TicketFeedbackMessage;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallLogDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallFeedbackDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.web.model.res.TicketContentVo;
import com.wormhole.hotelds.api.hotel.constant.CallOperatorTypeEnum;
import com.wormhole.hotelds.api.hotel.constant.ExpiredFlagEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDeviceQO;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.web.dao.repository.ServiceTicketRepository;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.HdsCallFeedbackEntity;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketFieldEnum;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeviceInRoomService {

    @Autowired
    private ServiceTicketRepository serviceTicketRepository;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Autowired
    private RtcHelper rtcHelper;

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private HdsCallLogDao hdsCallLogDao;

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Autowired
    private HdsDeviceDao hdsDeviceDao;

    @Autowired
    private HdsCallFeedbackDao hdsCallFeedbackDao;

    @Autowired
    private TicketProperties ticketProperties;
    public Mono<Boolean> callFeedback(HdsServiceTicketEntity entity) {
        if (entity.getReturnRoomId() != null && TicketUtils.judgeCallNeedFeedback(entity)){
            // 3个小时内一通电话只允许推送一次
            String callFeedbackKey = String.format(RedisConstant.CALL_FEEDBACK_KEY, entity.getReturnRoomId());
            return Mono.zip(reactiveStringRedisTemplate.opsForValue().setIfAbsent(callFeedbackKey, entity.getTicketNo(), Duration.ofHours(ticketProperties.getCallFeedbackHour())), getRtcUserIdByTicket(entity))
                    .flatMap(tuple2 ->{
                        Boolean lock = tuple2.getT1();
                        String toRtcUserId = tuple2.getT2();
                        // 确保获取到锁并且RTC用户ID不为空才进行推送
                        if (Boolean.TRUE.equals(lock) && StringUtils.isNotBlank(toRtcUserId)) {
                            // 首次推送反馈
                            return sendCallFeedbackMessage(toRtcUserId, entity.getReturnRoomId())
                                    .thenReturn(true);
                        }
                        if (!Boolean.TRUE.equals(lock)) {
                            log.info("通话已经反馈过 rtcRoomId: {}", entity.getRtcRoomId());
                        } else if (StringUtils.isBlank(toRtcUserId)) {
                            log.info("未找到客房设备无法推送反馈 positionCode: {} ticketNo {}", entity.getPositionCode(),entity.getTicketNo());
                        }
                        return Mono.just(false);
                    })
                    .flatMap(result -> updateFeedbackCache(callFeedbackKey, entity.getTicketNo()));
        }
        return Mono.just(false);
    }
    /**
     * 发送反馈消息
     */
    private Mono<Void> sendCallFeedbackMessage(String toRtcUserId, String rtcRoomId) {
        log.info("sendFeedbackMessage rtcRoomId: {} toRtcUserId {}", rtcRoomId,toRtcUserId);
        CallbackMessage message = new CallbackMessage();
        TicketContentVo vo = new TicketContentVo();
        TicketContentVo.TicketFeedBackVo feedBackVo = new TicketContentVo.TicketFeedBackVo();
        feedBackVo.setContent(SystemConstant.CALL_FEEDBACK_PUSH_TXT);
        feedBackVo.setRtcRoomId(rtcRoomId);
        feedBackVo.setFeedbackShow(true);
        feedBackVo.setFeedbackDimension(TicketFeedbackDimension.CALL.getCode());
        vo.setFeedbackList(Lists.newArrayList(feedBackVo));
        message.setCommand(SystemConstant.TICKET_FEEDBACK_COMMAND)
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(vo);

        String messageJson = JacksonUtils.writeValueAsString(message);

        rtcHelper.sendUnicastTextMessageAsync(toRtcUserId, messageJson)
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(result -> log.info("callFeedback success: userId={}, message={}", toRtcUserId, messageJson))
                .doOnError(error -> log.error("callFeedback error: userId={}, message={}, error={}", toRtcUserId, messageJson, error.getMessage()))
                .subscribe();
        return Mono.empty();
    }

    /**
     * 更新反馈缓存
     */
    private Mono<Boolean> updateFeedbackCache(String callFeedbackKey, String ticketNo) {
        return reactiveStringRedisTemplate.opsForValue().get(callFeedbackKey)
                .defaultIfEmpty("")
                .flatMap(ticketNos -> {
                    Set<String> tickets = new HashSet<>();
                    if (!ticketNos.isEmpty()) {
                        tickets.addAll(Arrays.asList(ticketNos.split(",")));
                    }
                    tickets.add(ticketNo);
                    log.info("通话反馈触发更新 key: {}, 新增工单: {}", callFeedbackKey, ticketNo);
                    return reactiveStringRedisTemplate.opsForValue().set(
                            callFeedbackKey,
                            String.join(",", tickets),
                            Duration.ofHours(3)
                    );
                });
    }

    private Mono<String> getRtcUserIdByTicket(HdsServiceTicketEntity entity) {
        return hdsDeviceDao.findOne(HdsDeviceQO.builder()
                            .positionCodes(Collections.singleton(entity.getPositionCode()))
                            .deviceAppType(Collections.singleton(DeviceTypeEnum.ROOM.getCode())).deviceStatus(DeviceStatusEnum.ONLINE.getCode()).build())
                    .switchIfEmpty(Mono.empty())
                    .map(device ->  DeviceInitRtcUtil.getRtcUserId(DeviceTypeEnum.ROOM.getCode(), entity.getDeviceId()));
    }

    public Mono<Boolean> pushTicketFeedbackNote(CallInfoMessage callbackReq) {
        // 前台座机回拨电话挂断
        try {
            if (ObjectUtil.equal(callbackReq.getInitiatorType(), CallOperatorTypeEnum.FRONT_DESK.getCode()) && ObjectUtil.equal(callbackReq.getCallStatus(), RtcCallStatusEnum.FINISHED.getCode())) {
                log.info("pushTicketFeedbackNote getAllReturnTickets {}", JacksonUtils.writeValueAsString(callbackReq));
                return getAllReturnTickets(callbackReq.getRtcRoomId()).flatMap(tickets -> getServiceTicketsFeedbackVo(tickets, callbackReq.getReceiverUserId()));
            } else if (ObjectUtil.equal(callbackReq.getInitiatorType(), CallOperatorTypeEnum.GUEST_ROOM.getCode())
                    && (ObjectUtil.equal(callbackReq.getCallStatus(), RtcCallStatusEnum.FINISHED.getCode()))) {
                // 客房咨询电话挂断
                return guestEndCallback(callbackReq);
            } else if(ObjectUtil.equal(callbackReq.getInitiatorType(), CallOperatorTypeEnum.GUEST_ROOM.getCode())
                    && (Objects.equals(callbackReq.getCallStatus(), RtcCallStatusEnum.CANCELLED.getCode())
                    || Objects.equals(callbackReq.getCallStatus(), RtcCallStatusEnum.REJECTED.getCode()))){
                return guestCancelCallBack(callbackReq);
            }
        } catch (Exception e) {
            log.error("pushTicketFeedbackNote error req {}", JacksonUtils.writeValueAsString(callbackReq), e);
            return Mono.just(false);
        }
        return Mono.just(true);
    }

    public Mono<Boolean> guestCancelCallBack(CallInfoMessage callbackReq) {
        HdsTicketQO hdsTicketQO = HdsTicketQO
                .builder()
                .ticketStatus(TicketStatus.PENDING.getCode())
                .rtcRoomId(callbackReq.getRtcRoomId())
                .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                .serviceSubcategory("EM_SOS")
                .hasFeedback(false)
                .hasEndOfCall(true)
                .build();
        return hdsServiceTicketDao.findList(hdsTicketQO).flatMap(tickets -> {
            if (CollectionUtil.isEmpty(tickets)) {
                log.info("没有sos类型工单,req:{}",JacksonUtils.writeValueAsString(callbackReq));
                return Mono.just(false);
            }

            TicketContentVo.TicketFeedBackVo contentVo = new TicketContentVo.TicketFeedBackVo();
            contentVo.setContent("我们已收到您的紧急呼叫，工作人员会尽快联系您，请留意接听。");
            contentVo.setFeedbackShow(false);
            List<TicketContentVo.TicketFeedBackVo> guestEndTickets = new ArrayList<>();
            guestEndTickets.add(contentVo);

            TicketContentVo res = new TicketContentVo();
            res.setFeedbackList(guestEndTickets);
            return rtcPushFeedback(callbackReq.getInitiatorUserId(), res);
        });
    }

    public Mono<Boolean> feedbackInTicketService(TicketFeedbackMessage message) {
        if (ObjectUtil.equal(message.getFeedbackDimension(), TicketFeedbackDimension.CALL.getCode())){
            return insertFeedbackByCall(message);
        }
        log.info("updateTicketFeedbackStatus req {}",JacksonUtils.writeValueAsString(message));
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(message.getTicketNo()), "工单号不能为空");
        return hdsServiceTicketDao.updateTicketFeedbackStatus(message.getTicketNo(),message.getFeedbackStatus())
                .flatMap(num -> Mono.just(num> 0))
                .onErrorResume(e -> {
                    log.error("insertTicketLog error {}",JacksonUtils.writeValueAsString(message),e);
                    return Mono.just(false);
                });
    }

    private Mono<Boolean> insertFeedbackByCall(TicketFeedbackMessage message) {
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(message.getRtcRoomId()), "评价通话id不能为空");
        return hdsServiceTicketDao.findOne(HdsTicketQO.builder().returnRoomId(message.getRtcRoomId()).build())
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "该通话未处理工单，无法反馈")))
                .flatMap(ticket -> {
                    HdsCallFeedbackEntity feedback = new HdsCallFeedbackEntity();
                    feedback.setFeedbackStatus(message.getFeedbackStatus());
                    feedback.setFeedbackTime(LocalDateTime.now());
                    feedback.setRtcRoomId(message.getRtcRoomId());
                    feedback.setHotelCode(ticket.getHotelCode());
                    feedback.setPositionCode(ticket.getPositionCode());
                    return hdsCallFeedbackDao.insert(feedback).thenReturn(true);
                });
    }

    private Mono<Boolean> getServiceTicketsFeedbackVo(List<HdsServiceTicketEntity> tickets,String toUserId) {
        List<TicketContentVo.TicketFeedBackVo> contentVos = tickets.stream().map(ticket -> {
            ServiceCategory byCode = ServiceCategory.getByCode(ticket.getServiceCategory(), false);
            TicketContentVo.TicketFeedBackVo contentVo = new TicketContentVo.TicketFeedBackVo();
            contentVo.setContent(String.format(byCode.getFeedbackText(), ticket.getGuestRequest()));
            contentVo.setTicketNo(ticket.getTicketNo());
            return contentVo;
        }).toList();
        TicketContentVo vo = new TicketContentVo();
        vo.setFeedbackList(contentVos);
        log.info("pushTicketFeedbackNote getServiceTicketsFeedbackVo size {}",CollectionUtil.size(contentVos));
        return rtcPushFeedback(toUserId, vo);
    }

    private Mono<Boolean> rtcPushFeedback(String rtcUserId, TicketContentVo data) {
        if (data == null || CollectionUtil.isEmpty(data.getFeedbackList())){
            log.info("pushTicketFeedbackNote  empty userId={}", rtcUserId);
            return Mono.just(true);
        }
        CallbackMessage message = new CallbackMessage();
        message.setCommand("ticket_feedback")
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(data);
        rtcHelper.sendUnicastTextMessageAsync(rtcUserId, JacksonUtils.writeValueAsString(message))
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(result -> log.info("pushTicketFeedbackNote  success: userId={} {}", rtcUserId, JacksonUtils.writeValueAsString(message)))
                .doOnError(error -> log.info("pushTicketFeedbackNote error: userId={} {}", rtcUserId, JacksonUtils.writeValueAsString(message), error))
                .subscribe();
        return Mono.just(true);
    }

    private Mono<Boolean> guestEndCallback(CallInfoMessage callbackReq) {
        log.info("pushTicketFeedbackNote guestEndCallback {}",JacksonUtils.writeValueAsString(callbackReq));
        // 查询所有当前通话创建的服务类工单 + 查询当前通话创建的咨询类工单 + 查询当前通话紧急工单
        return Mono.zip(hdsServiceTicketDao.getServiceTicketsByCall(callbackReq, ServiceType.SERVICE),
                        hdsServiceTicketDao.completedTicketsInCallByReq(callbackReq.getRtcRoomId(), Lists.newArrayList(ServiceType.EMERGENCY.name())),
                        inQueryTicketsWithAnswer(callbackReq),
                        hdsServiceTicketDao.getServiceTicketsByCall(callbackReq, ServiceType.OTHER))
                .flatMap(tuple -> {
                    Set<String> serviceTickets = tuple.getT1();
                    List<HdsServiceTicketEntity> emergencyTickets = tuple.getT2();
                    List<TicketContentVo.TicketFeedBackVo> t3 = tuple.getT3();
                    Set<String> otherTickets = tuple.getT4();
                    List<TicketContentVo.TicketFeedBackVo> guestEndTickets = new ArrayList<>();
                    log.info("pushTicketFeedbackNote queryInCall serviceTickets={},emergencyTickets={},inQuery={}",
                            JacksonUtils.writeValueAsString(serviceTickets),
                            JacksonUtils.writeValueAsString(emergencyTickets),
                            JacksonUtils.writeValueAsString(t3));
                    if (CollectionUtil.isNotEmpty(serviceTickets)) {
                        String content = String.format("您的「%s」服务已通知酒店，请耐心等待。",String.join(",",serviceTickets));
                        TicketContentVo.TicketFeedBackVo contentVo = new TicketContentVo.TicketFeedBackVo();
                        contentVo.setContent(content);
                        contentVo.setFeedbackShow(false);
                        guestEndTickets.add(contentVo);
                    }
                    List<TicketContentVo.TicketFeedBackVo> ticketContentVos = emergencyTickets.stream().map(ele -> {
                        TicketContentVo.TicketFeedBackVo ticketContentVo = new TicketContentVo.TicketFeedBackVo();
                        String msg = String.format(ServiceCategory.EMERGENCY.getFeedbackText(), ele.getGuestRequest());
                        ticketContentVo.setContent(msg);
                        ticketContentVo.setTicketNo(ele.getTicketNo());
                        return ticketContentVo;
                    }).toList();
                    if (CollectionUtil.isNotEmpty(ticketContentVos)){
                        guestEndTickets.addAll(ticketContentVos);
                    }
                    if (CollectionUtil.isNotEmpty(t3)){
                        guestEndTickets.addAll(t3);
                    }
                    if (CollectionUtil.isNotEmpty(otherTickets)) {
                        String content = String.format("您的「%s」服务已通知酒店，请耐心等待。",String.join(",",otherTickets));
                        TicketContentVo.TicketFeedBackVo contentVo = new TicketContentVo.TicketFeedBackVo();
                        contentVo.setContent(content);
                        contentVo.setFeedbackShow(false);
                        guestEndTickets.add(contentVo);
                    }
                    guestEndTickets.addAll(ticketContentVos);
                    TicketContentVo res = new TicketContentVo();
                    res.setFeedbackList(guestEndTickets);
                    return Mono.just(res);
                })
                .flatMap(data ->  rtcPushFeedback(callbackReq.getInitiatorUserId(), data));
    }

    private Mono<List<TicketContentVo.TicketFeedBackVo>> inQueryTicketsWithAnswer(CallInfoMessage callbackReq){
        return hdsServiceTicketDao.completedTicketsInCallByReq(callbackReq.getRtcRoomId(), Lists.newArrayList(ServiceType.INQUIRY.name()))
                .flatMap(tickets -> {
                    if (CollectionUtil.isEmpty(tickets)){
                        return Mono.just(new ArrayList<>());
                    }
                    String conversationId = tickets.get(0).getConversationId();
                    Map<String, HdsServiceTicketEntity> reqIdTicketMap = tickets.stream()
                            .collect(Collectors.toMap(HdsServiceTicketEntity::getClientReqId, Function.identity(),(v1, v2)->v1));
                    return hdsCallLogDao.findTicketsAnswerInCall(conversationId, reqIdTicketMap.keySet())
                            .flatMap(list -> {
                                List<TicketContentVo.TicketFeedBackVo> res = new ArrayList<>();
                                for (HdsCallLogEntity callLogEntity : list) {
                                    TicketContentVo.TicketFeedBackVo vo = new TicketContentVo.TicketFeedBackVo();
                                    vo.setTicketNo(reqIdTicketMap.get(callLogEntity.getClientReqId()).getTicketNo());
                                    vo.setContent(callLogEntity.getContent());
                                    res.add(vo);
                                }
                                return Mono.just(res);
                            });
                });

    }

    private Mono<List<HdsServiceTicketEntity>> getAllReturnTickets(String rtcRoomId) {
        // 构建查询条件
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.return_room_id.name()).is(rtcRoomId)
                .and(HdsServiceTicketFieldEnum.expired_flag.name()).is(0)
                .and(HdsServiceTicketFieldEnum.status.name()).is(1)
                .and(HdsServiceTicketFieldEnum.user_type.name()).is(0);
        // 构建查询对象
        Query query = Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, HdsServiceTicketFieldEnum.id.name()));
        // 执行查询并返回结果列表
        return r2dbcEntityTemplate.select(query, HdsServiceTicketEntity.class)
                .collectList();
    }


}
