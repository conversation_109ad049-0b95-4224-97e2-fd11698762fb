package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.agent.core.enums.UserStatus;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.agent.core.model.entity.UserFieldEnum;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.UserReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Repository
public class UserDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    public Mono<List<UserEntity>> findList(UserReq userReq) {
        Criteria criteria = getCriteria(userReq);
        return r2dbcEntityTemplate.select(Query.query(criteria),UserEntity.class).collectList();
    }


    public Mono<UserEntity> findOne(UserReq userReq) {
        Criteria criteria = getCriteria(userReq);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1),UserEntity.class);
    }
    public Criteria getCriteria(UserReq userReq) {
        Criteria criteria = Criteria.empty();

        if(StringUtils.isNotBlank(userReq.getUserId())) {
            criteria = criteria.and(UserFieldEnum.user_id.name()).is(userReq.getUserId());
        }
        if(CollUtil.isNotEmpty(userReq.getUserIds())) {
            criteria = criteria.and(UserFieldEnum.user_id.name()).in(userReq.getUserIds());
        }
        if(Objects.nonNull(userReq.getStatus())) {
            criteria = criteria.and(UserFieldEnum.status.name()).is(UserStatus.active);
        }
        criteria = criteria.and(UserFieldEnum.rowStatus.name()).is(RowStatusEnum.VALID.getId());
        return criteria;
    }

    public Mono<Map<String, UserEntity>> fetchBdwUserInfo(Set<String> userIds){
        UserReq userReq = new UserReq();
        userReq.setUserIds(userIds);
        userReq.setStatus(UserStatus.active);
        return findList(userReq).map(results -> results.stream()
                .collect(Collectors.toMap(UserEntity::getUserId, Function.identity(), (e1, e2) -> e1)));
    }

}
