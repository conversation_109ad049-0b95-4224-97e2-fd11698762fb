package com.wormhole.hotelds.api.hotel.constant;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.core.enums.ReplyExceptionEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.TicketClosedLoopLevel;
import com.wormhole.hotelds.core.enums.TicketStatus;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 关注工单
 */
@AllArgsConstructor
@Getter
public enum FocusTagEnum {
    /**
     * 紧急
     */
    EMERGENCY("紧急",ColorEnum.RED.getHexCode()),
    /**
     * 投诉
     */
    COMPLAINT("投诉",ColorEnum.RED.getHexCode()),
    
    /**
     * 客诉预警
     */
    WANING_COMPLAINT("客诉预警",ColorEnum.YELLOW.getHexCode()),
    
    /**
     * 超时
     */
    OVERDUE("超时",ColorEnum.YELLOW.getHexCode()),
    ;
    

    
    private final String tagText;

    private final String tagColor;

    public static String getFocusTagColor(HdsServiceTicketEntity ticket){
        if (ticket == null) return null;
        // 已完成的灰色
        if (ObjectUtil.equal(ticket.getStatus(), TicketStatus.COMPLETED.getCode())) return ColorEnum.GRAY.getHexCode();
        // 紧急优先
        FocusTagEnum focusTagEnum = judgeFocusTag(ticket);
        if (focusTagEnum != null){
            return focusTagEnum.getTagColor();
        }
        return null;
    }

    public static List<String> getExtraTagShow(HdsServiceTicketEntity ticket){
        List<String> extraTagShow = new ArrayList<>();
        if (ObjectUtil.notEqual(ticket.getReplyExceptionType(), ReplyExceptionEnum.NORMAL.getCode())){
            extraTagShow.add( WANING_COMPLAINT.getTagText());
        }
        return extraTagShow;
    }

    public static FocusTagEnum judgeFocusTag(HdsServiceTicketEntity ticket) {
        if (ticket == null) return null;
        if(ObjectUtil.equal(ticket.getServiceCategory(), ServiceCategory.EMERGENCY.name())){
            return EMERGENCY;
        }
        if(ObjectUtil.equal(ticket.getServiceCategory(), ServiceCategory.COMPLAINT.name())){
            return COMPLAINT;
        }
        if (ObjectUtil.notEqual(ticket.getReplyExceptionType(), ReplyExceptionEnum.NORMAL.getCode())){
            return WANING_COMPLAINT;
        }
        if (ObjectUtil.equal(ticket.getOverdueFlag(), YesNoEnum.YES.getCode())){
            return OVERDUE;
        }
        return null;
    }
}