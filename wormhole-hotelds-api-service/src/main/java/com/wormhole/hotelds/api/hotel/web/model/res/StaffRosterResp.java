package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.api.hotel.resp.BlankResp;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StaffRosterResp {
    private Integer emptyNum;

    private List<BlankResp> columnTitles;

    private List<RowDetail> rowDetails;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class RowDetail {
        private String code;
        private String name;
        private List<TicketStaffDetail> ticketDetails;

        @Data
        @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
        public static class TicketStaffDetail {
            private boolean emptyFlag;
            private String ticketCategory;
            private String ticketName;
            private List<String> employeeNames;
        }

    }
}
