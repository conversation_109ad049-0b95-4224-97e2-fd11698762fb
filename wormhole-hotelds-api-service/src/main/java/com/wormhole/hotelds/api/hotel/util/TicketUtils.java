package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.api.hotel.constant.ColorEnum;
import com.wormhole.hotelds.core.enums.ReplyExceptionEnum;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.ServiceType;
import com.wormhole.hotelds.core.enums.TicketClosedLoopLevel;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;

import java.util.Objects;

import static com.wormhole.hotelds.core.enums.ServiceCategory.COMPLAINT;

public class TicketUtils {

    public static boolean autoComplete(HdsServiceTicketEntity entity){
        Integer closedLoopLevel = entity.getClosedLoopLevel();
        Integer userType = entity.getUserType();
        TicketClosedLoopLevel level = TicketClosedLoopLevel.getByCode(closedLoopLevel);
        // 创建时完成：ai闭环、sos直通、员工创建
        return  Objects.equals(level, TicketClosedLoopLevel.L1) || Objects.equals(level, TicketClosedLoopLevel.L4) || Objects.equals(userType, 1);
    }

    public static boolean judgeCallNeedFeedback(HdsServiceTicketEntity entity) {
        // 投诉类工单+回复异常工单
        return ObjectUtil.equal(entity.getServiceCategory(), ServiceCategory.COMPLAINT.getCode())
                || ObjectUtil.equal(entity.getReplyExceptionType(), ReplyExceptionEnum.T1.getCode())
                || ObjectUtil.equal(entity.getReplyExceptionType(), ReplyExceptionEnum.T2.getCode());
    }

    public static String getShowTextColor(HdsServiceTicketEntity ticket) {
        if(Objects.equals(ticket.getStatus(),0)) {
            if(Objects.equals(ServiceType.EMERGENCY.name(), ticket.getServiceType()) || Objects.equals(COMPLAINT.getCode(), ticket.getServiceCategory())) {
                return ColorEnum.RED.getHexCode();
            }else {
                return ticket.getOverdueFlag() == 1  ? ColorEnum.ORANGE.getHexCode() :ColorEnum.BLUE.getHexCode();
            }
        } else {
            // 已完成都为灰色
            return ColorEnum.GRAY.getHexCode();
        }
    }
}
