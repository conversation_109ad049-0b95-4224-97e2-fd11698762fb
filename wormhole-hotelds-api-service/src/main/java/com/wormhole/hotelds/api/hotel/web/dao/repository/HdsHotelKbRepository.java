package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.api.hotel.web.model.entity.HdsHotelKbEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@Repository
public interface HdsHotelKbRepository extends ReactiveCrudRepository<HdsHotelKbEntity, Long> {

    Mono<HdsHotelKbEntity> findByHotelCode(String hotelCode, Integer rowStatus);

    Mono<HdsHotelKbEntity> findBySpaceCodeAndHotelCode(String spaceCode, String hotelCode, Integer rowStatus);
}
