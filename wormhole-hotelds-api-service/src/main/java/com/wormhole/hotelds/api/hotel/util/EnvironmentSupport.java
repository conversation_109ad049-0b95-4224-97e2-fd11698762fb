package com.wormhole.hotelds.api.hotel.util;

import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.wormhole.hotelds.api.hotel.constant.EnviromentEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

@Component
public class EnvironmentSupport implements EnvironmentAware {

    private static Environment staticEnvironment;

    @Override
    public void setEnvironment(@NotNull Environment environment) {
        staticEnvironment = environment;
    }

    public static String getActiveProfile() {
        String activeProfile = System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME);
        if (StringUtils.isBlank(activeProfile)) {
            return String.join(",", staticEnvironment.getActiveProfiles());
        }
        return activeProfile;
    }

    /**
     * 获取环境标识
     *
     * @return
     */
    public static String getEnv() {
        String activeProfile = System.getProperty(AbstractEnvironment.ACTIVE_PROFILES_PROPERTY_NAME);
        Optional<String> first;
        if (StringUtils.isBlank(activeProfile)) {
            String[] activeProfiles = staticEnvironment.getActiveProfiles();
            first = Arrays.stream(activeProfiles).filter(EnviromentEnum::isValid).findFirst();
        } else {
            first = Splitter.on(",").splitToList(activeProfile).stream().filter(EnviromentEnum::isValid).findFirst();
        }
        activeProfile = first.orElse(null);
        Preconditions.checkArgument(StringUtils.isNotBlank(activeProfile), "spring.profiles.active must not be blank");
        return activeProfile;
    }

}
