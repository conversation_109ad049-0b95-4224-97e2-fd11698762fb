package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoomInfoBaseVo {
    /**
     * 房间编码
     */
    private String positionCode;

    /**
     * 房间名称
     * 如101/前台/西餐厅
     */
    private String roomName;

    private Integer sortOrder;

}
