package com.wormhole.hotelds.api.hotel.util;

import com.wormhole.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;

@Component
@Slf4j
public class HotelApiRedisUtils {

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    public Mono<Void> deleteRedisKey(String redisKey) {
        return reactiveStringRedisTemplate.opsForValue().delete(redisKey)
                .doOnError(e -> log.error("删除redis缓存异常 {}: {}", redisKey, e.getMessage()))
                .onErrorMap(e -> new BusinessException("REDIS-ERROR-001", "redis服务访问失败"))
                .then();
    }

    public Mono<String> getString(String redisKey){
        return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .filter(StringUtils::isNotBlank)
                .doOnNext(cache -> log.info("redisKey from redis success {}  {}", redisKey, cache))
                .switchIfEmpty(Mono.just(StringUtils.EMPTY));
    }

    public Mono<Boolean> lock(String redisKey,String value,Integer milliseconds){
        return reactiveStringRedisTemplate.opsForValue().setIfAbsent(redisKey,value, Duration.ofMillis(milliseconds))
                .switchIfEmpty(Mono.just(false))
                .doOnSuccess(res -> log.info("redisKey lock redis success {} {} ", redisKey, res))
                .doOnError(e -> log.error("redisKey lock redis fail {}  ", redisKey,e))
                .onErrorResume(e -> Mono.just(false));

    }

}
