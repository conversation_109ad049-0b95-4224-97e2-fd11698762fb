package com.wormhole.hotelds.api.hotel.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnBean(XxlJobProperties.class)
public class WormholeXxlJobConfiguration {

    @Resource
    private XxlJobProperties xxlJobProperties;

    @Value("${spring.application.name}")
    private String appName;

    @Bean("wormholeXxlJobSpringExecutor")
    @ConditionalOnMissingBean
    public XxlJobSpringExecutor xxlJobSpringExecutor() {
        XxlJobSpringExecutor executor = new XxlJobSpringExecutor();
        executor.setAppname(appName);
        executor.setAdminAddresses(xxlJobProperties.getAddresses());
        executor.setPort(xxlJobProperties.getPort());
        executor.setLogRetentionDays(xxlJobProperties.getLogRetentionDays());
        return executor;
    }
}
