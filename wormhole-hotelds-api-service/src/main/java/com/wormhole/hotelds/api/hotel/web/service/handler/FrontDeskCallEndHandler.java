//package com.wormhole.hotelds.api.hotel.web.ticket.handler;
//
//import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
//import com.wormhole.channel.consts.message.CallInfoMessage;
//import com.wormhole.common.util.JacksonUtils;
//import com.wormhole.hotelds.api.hotel.constant.CallOperatorTypeEnum;
//import com.wormhole.hotelds.api.hotel.web.service.CallBackBizService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Mono;
//
//import java.util.Objects;
//
//@Component
//@Slf4j
//public class FrontDeskCallEndHandler implements RtcCallbackHandler {
//
//    @Autowired
//    private CallBackBizService callBackBizService;
//
//    @Override
//    public boolean canHandle(CallInfoMessage request) {
//        return Objects.equals(request.getCallStatus(), RtcCallStatusEnum.FINISHED.getCode())
//               && Objects.equals(request.getInitiatorType(), CallOperatorTypeEnum.FRONT_DESK.getCode());
//    }
//
//    @Override
//    public Mono<Void> handle(CallInfoMessage request) {
//        log.info("FrontDeskCallEndHandler.handle,request:{}", JacksonUtils.writeValueAsString(request));
//        return handleInitiatorDeviceTicket(request)
//                .doOnError(e -> log.error("处理发起设备工单异常", e))
//                .doOnSuccess(aVoid -> log.info("处理发起设备工单成功"))
//                .onErrorResume(e -> Mono.empty());
//    }
//
//    private Mono<Void> handleInitiatorDeviceTicket(CallInfoMessage request) {
//        return callBackBizService.handleFrontDeskCallback(request);
//    }
//}
