package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.core.model.entity.HdsInvitationCodeEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @Author：flx
 * @Date：2025/5/22 16:41
 * @Description：InvitationCodeRepository
 */
@Repository
public interface InvitationCodeRepository extends ReactiveCrudRepository<HdsInvitationCodeEntity, Integer> {

    Mono<HdsInvitationCodeEntity> findByInvitationCode(String invitationCode);
}
