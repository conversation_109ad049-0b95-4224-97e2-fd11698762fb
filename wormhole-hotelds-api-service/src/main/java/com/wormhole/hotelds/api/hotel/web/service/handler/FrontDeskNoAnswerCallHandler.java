//package com.wormhole.hotelds.api.hotel.web.ticket.handler;
//
//import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
//import com.wormhole.channel.consts.message.CallInfoMessage;
//import com.wormhole.common.util.JacksonUtils;
//import com.wormhole.hotelds.api.hotel.constant.CallOperatorTypeEnum;
//import com.wormhole.hotelds.api.hotel.web.service.CallBackBizService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.ReactiveRedisTemplate;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Mono;
//
//import java.time.Duration;
//import java.util.Objects;
//
//import static com.wormhole.hotelds.api.hotel.constant.RedisConstant.CALLER_TO_CALLEE_COUNT_KEY;
//
//@Component
//@Slf4j
//public class FrontDeskNoAnswerCallHandler implements RtcCallbackHandler {
//    @Autowired
//    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;
//
//    @Autowired
//    private CallBackBizService callBackBizService;
//    @Override
//    public boolean canHandle(CallInfoMessage request) {
//        Integer statusCode = request.getCallStatus();
//        return (Objects.equals(statusCode, RtcCallStatusEnum.CANCELLED.getCode()) ||
//                Objects.equals(statusCode, RtcCallStatusEnum.REJECTED.getCode())) && Objects.equals(CallOperatorTypeEnum.FRONT_DESK.getCode(), request.getInitiatorType());
//    }
//
//    @Override
//    public Mono<Void> handle(CallInfoMessage request) {
//        log.info("FrontDeskNoAnswerCallHandler.handle,request:{}", JacksonUtils.writeValueAsString(request));
//        return handleNoAnwserCall(request)
//                .doOnError(e -> log.error("处理无应答通话异常", e))
//                .onErrorResume(e -> Mono.empty());
//    }
//
//    private Mono<Void> handleNoAnwserCall(CallInfoMessage request) {
//        String redisKey = String.format(CALLER_TO_CALLEE_COUNT_KEY,
//                request.getInitiatorUserId(),
//                request.getReceiverUserId());
//
//        return reactiveRedisTemplate.opsForValue().increment(redisKey, 1)
//                .doOnNext(count -> log.info("未接通计数增加: redisKey={}, currentCount={}", redisKey, count))
//                .flatMap(count -> {
//                    Mono<Boolean> expireMono = reactiveRedisTemplate.expire(redisKey, Duration.ofMinutes(60));
//
//                    if (count >= 3) {
//                        return expireMono.then(callBackBizService.handleFrontDeskCallback(request)
//                                        .doOnSuccess(v -> log.info("处理发起方设备票据完成"))
//                                        .doOnError(e -> log.error("处理发起方设备票据失败", e)))
//                                .then(reactiveRedisTemplate.delete(redisKey)
//                                        .doOnSuccess(v -> log.info("删除redisKey: {}", redisKey)))
//                                .then(Mono.empty());
//                    }
//
//                    return expireMono.then(Mono.empty());
//                });
//    }
//
//
//}
