package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateTicketReq {
    private String serviceType;
    private String serviceCategory;
    private String serviceSubcategory;
    private Integer closedLoopLevel;
    private Integer replyExceptionType;


    private String guestRequest;

    private String hotelCode;
    private String positionCode;
    private String rtcRoomId;

    private String deviceId;
    private String conversationId;
    private String clientReqId;

    private Integer userType; // 0: 普通用户下单，1:员工下单

    private String userId; // 用户id 填充为工单创建人
    private String userName;
    private Integer createType = 0; //  0 自动创建 1 手动创建
    private String clientType;

    /**
     * 紧急通话的接收方
     */

    private String completedBy;

    private String completedByName;

    private String serviceKeywords;
}
