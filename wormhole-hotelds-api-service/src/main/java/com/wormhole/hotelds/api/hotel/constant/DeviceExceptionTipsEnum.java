package com.wormhole.hotelds.api.hotel.constant;

import com.wormhole.hotelds.core.enums.DeviceStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/27
 */
@AllArgsConstructor
@Getter
public enum DeviceExceptionTipsEnum {

    /**
     * 待激活
     * 如果相应房间未激活客房pad，点击拨打电话触发弹窗"当前房间未激活客房Pad，暂无法通过本功能拨打客房电话。"
     */
    INACTIVE(0, "待激活", "当前房间未激活客房Pad，暂无法通过本功能拨打客房电话。"),

    /**
     * 在线但忙线
     * 如果点击拨打电话后客房pad占线，触发弹窗"当前房间设备正在通话中，请稍后再拨。"
     */
    BUSY_LINE(1, "忙线", "当前房间设备正在通话中，请稍后再拨。"),

    /**
     * 离线
     * 当前房间设备已离线，无法拨打客房电话。如需帮助，请联系技术支持。
     */
    OFFLINE(2, "离线", "当前房间设备已离线，无法拨打客房电话。如需帮助，请联系技术支持。"),

    EMPTY(null, "没有设备", "系统暂未获取该房间客人联系方式，建议通过其他方式联系客人。"),;


    private final Integer code;
    private final String desc;
    private final String message;

    // 静态映射表，用于快速查找
    private static final Map<Integer, DeviceExceptionTipsEnum> CODE_MAP = new HashMap<>();

    static {
        for (DeviceExceptionTipsEnum item : values()) {
            CODE_MAP.put(item.getCode(), item);
        }
    }


    /**
     * 根据设备状态码获取对应的提示信息
     * 使用映射来代替条件判断，提高可维护性和性能
     *
     * @param deviceStatus 设备状态码
     * @return 对应的提示信息，如果没有匹配则返回UNKNOWN的提示
     */
    public static String getDeviceExceptionTips(Integer deviceStatus) {
        // 设备状态为null时返回INACTIVE的消息
        if (deviceStatus == null) {
            return INACTIVE.getMessage();
        }

        // 根据DeviceStatusEnum的状态码映射到本枚举
        if (Objects.equals(deviceStatus, DeviceStatusEnum.PENDING_ACTIVATION.getCode())) {
            return INACTIVE.getMessage();
        }

        if (Objects.equals(deviceStatus, DeviceStatusEnum.OFFLINE.getCode())) {
            return OFFLINE.getMessage();
        }
        return null;
    }
}
