package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.res.HotelMappingRes;
import com.wormhole.hotelds.api.hotel.web.service.HotelMappingService;
import com.wormhole.hotelds.plugin.model.dto.HotelMappingDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:42
 */
@RestController
@RequestMapping("/hotel_mapping")
@Slf4j
public class HotelMappingController {

    @Resource
    private HotelMappingService hotelMappingService;


    @GetMapping("/getHotelMapping")
    public Mono<Result<HotelMappingRes>> getHotelMapping(
            @RequestParam(value = "hotelCode") String hotelCode,
            @RequestParam(value = "channel", required = false) String channel,
            @RequestParam(value = "platform", required = false) String platform) {
        log.info("接收到获取酒店映射请求，hotelCode: {} {} {}", hotelCode, channel,platform);
        return hotelMappingService.getHotelMapping(hotelCode, channel,platform).flatMap(Result::success);
    }


    @PostMapping("/save")
    public Mono<Result<Boolean>> save(@RequestBody HotelMappingDTO hotelMappingDTO) {
        return hotelMappingService.save(hotelMappingDTO).flatMap(Result::success);
    }
}
