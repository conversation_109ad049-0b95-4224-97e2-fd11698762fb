package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceBindResp {
    private String hotelCode;

    private String hotelName;

    private String positionCode;

    private String positionFullName;

    private String deviceId;

    private String deviceType;

    private String deviceSn;

    private String imei;

    private LocalDateTime bindTime;
}
