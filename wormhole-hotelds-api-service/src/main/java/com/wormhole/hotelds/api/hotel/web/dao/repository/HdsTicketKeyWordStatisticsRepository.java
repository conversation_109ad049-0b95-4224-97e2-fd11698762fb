package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.ticket.model.HdsTicketKeyWordStatistics;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Repository
public interface HdsTicketKeyWordStatisticsRepository extends ReactiveCrudRepository<HdsTicketKeyWordStatistics, Long> {

    @Query("""
            SELECT position_code, SUM(ticket_count) as total_count 
            FROM hds_ticket_keyword_statistics 
            WHERE hotel_code = :hotelCode 
            AND business_date >= :startDate 
            AND business_date <= :endDate 
            AND service_category = :serviceCategory
            AND row_status = 1 
            GROUP BY position_code
            ORDER BY total_count DESC,position_code desc
            LIMIT 3
            """)
    Flux<TopRoomStatisticDTO> findTopRoomsTicketCount(String hotelCode,
                                                      LocalDate startDate,
                                                      LocalDate endDate,
                                                      String serviceCategory);


    @Query("""
            SELECT position_code, service_keyword, SUM(ticket_count) as total_count 
            FROM hds_ticket_keyword_statistics 
            WHERE hotel_code = :hotelCode 
            AND business_date >= :startDate 
            AND business_date <= :endDate 
            AND service_category = :serviceCategory
            AND position_code IN (:positionCodes)
            AND row_status = 1 
            GROUP BY  position_code,service_keyword
            ORDER BY total_count DESC 
            """)
    Flux<RoomKeywordStatisticDTO> findRoomKeyWordTicketCount(String hotelCode,
                                                             LocalDate startDate,
                                                             LocalDate endDate,
                                                             String serviceCategory,
                                                             List<String> positionCodes);


    @Query("""
            SELECT service_subcategory, SUM(ticket_count) as total_count 
            FROM hds_ticket_keyword_statistics 
            WHERE hotel_code = :hotelCode 
            AND business_date >= :startDate 
            AND business_date <= :endDate 
            AND service_category = :serviceCategory
            AND row_status = 1 
            GROUP BY service_subcategory 
            ORDER BY total_count DESC ,service_subcategory desc 
            LIMIT 3
            """)
    Flux<TopServiceCategoryStatisticDTO> findTopServiceCategoryTicketCount(String hotelCode,
                                                                           LocalDate startDate,
                                                                           LocalDate endDate,
                                                                           String serviceCategory);


    @Query("""
            SELECT service_subcategory,service_keyword, SUM(ticket_count) as total_count 
            FROM hds_ticket_keyword_statistics 
            WHERE hotel_code = :hotelCode 
            AND business_date >= :startDate 
            AND business_date <= :endDate 
            AND service_category = :serviceCategory
            AND  service_subcategory IN (:serviceSubCategories)
            AND row_status = 1 
            GROUP BY service_subcategory,service_keyword 
            ORDER BY total_count DESC 
            """)
    Flux<ServiceSubcategoryKeywordStatisticDTO> findServiceSubcategoryKeyWordTicketCount(String hotelCode,
                                                                                         LocalDate startDate,
                                                                                         LocalDate endDate,
                                                                                         String serviceCategory,
                                                                                         List<String>  serviceSubCategories);

    @Query("""
        SELECT service_keyword, SUM(ticket_count) as total_count 
        FROM hds_ticket_keyword_statistics 
        WHERE hotel_code = :hotelCode 
        AND business_date >= :startDate 
        AND business_date <= :endDate 
        AND service_category = :serviceCategory
        AND row_status = 1 
        GROUP BY service_keyword 
        ORDER BY total_count DESC
        LIMIT :limit
        """)
    Flux<KeywordStaticsDTO> findKeyWordStatics(String hotelCode,
                                               LocalDate startDate,
                                               LocalDate endDate,
                                               String serviceCategory,
                                               int limit);
}
