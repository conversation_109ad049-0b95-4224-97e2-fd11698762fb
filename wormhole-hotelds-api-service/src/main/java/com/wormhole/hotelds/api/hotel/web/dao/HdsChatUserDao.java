package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.wormhole.hotelds.core.model.entity.HdsChatUserEntity;
import com.wormhole.hotelds.core.model.entity.HdsChatUserFieldEnum;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/17 19:11
 */

@Repository
public class HdsChatUserDao {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsChatUserEntity> queryById(String userId) {
        Criteria criteria = Criteria.where(HdsChatUserFieldEnum.id.name()).is(userId)
                .and(HdsChatUserFieldEnum.status.name()).is(1);
        Query query = Query.query(criteria).limit(1);

        return r2dbcEntityTemplate.selectOne(query, HdsChatUserEntity.class);
    }

    public Mono<List<HdsChatUserEntity>> queryByAccountList(Collection<String> accountList) {
        if (CollectionUtil.isEmpty(accountList)) {
            return Mono.just(Collections.emptyList());
        }
        Criteria criteria = Criteria.where(HdsChatUserFieldEnum.account.name()).in(accountList);
        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.select(query, HdsChatUserEntity.class).collectList();
    }


    public Mono<Map<String, HdsChatUserEntity>> fetchWechatUserInfo(Set<String> userIds) {
        return queryByAccountList(userIds).map(results -> results.stream()
                .collect(Collectors.toMap(HdsChatUserEntity::getAccount, Function.identity(), (e1, e2) -> e1)));
    }

}
