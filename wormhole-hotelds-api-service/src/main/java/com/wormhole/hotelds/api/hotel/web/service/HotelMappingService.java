package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.model.res.HotelMappingRes;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.model.dto.HotelMappingDTO;
import com.wormhole.hotelds.plugin.model.qo.HdsHotelMappingQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 * @date 2025/4/23 11:20
 */
@Service
@Slf4j
public class HotelMappingService {


    @Autowired
    private HdsHotelMappingDao hotelMappingDao;


    public Mono<HotelMappingRes> getHotelMapping(String hotelCode, String channel, String platform) {
        HdsHotelMappingQO build = HdsHotelMappingQO.builder()
                .hotelCode(hotelCode)
                .channel(channel)
                .platform(platform)
                .build();
        return hotelMappingDao.findOne(build)
                .map(this::convertToHotelMappingRes);
    }

    private HotelMappingRes convertToHotelMappingRes(HdsHotelMappingEntity entity) {
        return HotelMappingRes.builder()
                .hotelCode(entity.getHotelCode())
                .hotelName(entity.getHotelName())
                .externalId(entity.getExternalId())
                .externalName(entity.getExternalName())
                .hotelCode(entity.getHotelCode())
                .channel(entity.getChannel())
                .platform(entity.getPlatform())
                .build();
    }


    public Mono<Boolean> save(HotelMappingDTO hotelMappingDTO) {
        log.info("hotelMappingDTO:{}", JacksonUtils.writeValueAsString(hotelMappingDTO));
        HdsHotelMappingQO hotelMappingQO = HdsHotelMappingQO.builder()
                .hotelCode(hotelMappingDTO.getHotelCode())
                .channel(hotelMappingDTO.getChannel())
                .platform(hotelMappingDTO.getPlatform())
                .build();
        return hotelMappingDao.findOne(hotelMappingQO)
                .flatMap(entity -> {
                    entity.setHotelName(hotelMappingDTO.getHotelName());
                    entity.setExternalId(hotelMappingDTO.getExternalId());
                    entity.setExternalName(hotelMappingDTO.getExternalName());
                    return hotelMappingDao.save(entity);
                }).switchIfEmpty(
                        hotelMappingDao.save(hotelMappingDTO.toEntity()))
                .then(Mono.just(true));
    }




}
