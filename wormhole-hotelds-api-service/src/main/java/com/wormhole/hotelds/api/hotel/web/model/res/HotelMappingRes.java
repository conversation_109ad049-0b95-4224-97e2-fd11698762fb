package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:19
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
public class HotelMappingRes implements Serializable {

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 外部系统酒店id
     */
    private String externalId;

    /**
     * 外部系统酒店名称
     */
    private String externalName;

    /**
     * 外部集团或系统标识
     */
    private String channel;

    private String platform;

}

