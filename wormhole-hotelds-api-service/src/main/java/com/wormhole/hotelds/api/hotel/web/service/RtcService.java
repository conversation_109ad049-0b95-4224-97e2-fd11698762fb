package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketCommandMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Service
@Slf4j
public class RtcService {

    @Autowired
    private RtcHelper rtcHelper;

    public Mono<Void> sendTicketCommandMessage(Collection<String> rtcUserIds, TicketCommandMessage ticketCommandMessage) {
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.TICKET_COMMAND.getCode())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(ticketCommandMessage);
        return sendDevice(rtcUserIds,callbackMessage);
    }

    /**
     * 向多个用户发送消息
     *
     * @param userIds 用户ID列表
     * @param callbackMessage 回调消息
     * @return 完成信号
     */
    public Mono<Void> sendDevice(Collection<String> userIds, CallbackMessage callbackMessage) {
        if (userIds == null || userIds.isEmpty() || callbackMessage == null) {
            log.error("发送消息失败: 用户列表为空或消息为空");
            return Mono.empty();
        }

        String message = JacksonUtils.writeValueAsString(callbackMessage);
        if (StrUtil.isBlank(message)) {
            log.error("发送消息失败: 序列化消息为空");
            return Mono.empty();
        }

        log.info("开始发送广播消息: userIds={}, messageType={}", userIds, callbackMessage);

        for (String userId : userIds) {
            rtcHelper.sendUnicastTextMessageAsync(userId, message)
                    .subscribeOn(Schedulers.boundedElastic())
                    .doOnSuccess(result -> log.info("发送广播消息成功: userId={}", userId))
                    .doOnError(error -> log.error("发送广播消息失败: userId={}, error={}",
                            userId, error.getMessage(), error))
                    .subscribe();
        }

        return Mono.empty();
    }
}
