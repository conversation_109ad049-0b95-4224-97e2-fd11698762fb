package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.hotelds.core.model.entity.HdsCallFeedbackEntity;
import com.wormhole.hotelds.core.model.entity.HdsCallFeedbackFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

@Repository
@Slf4j
public class HdsCallFeedbackDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsCallFeedbackEntity> insert(HdsCallFeedbackEntity entity) {
        return r2dbcEntityTemplate.insert(entity);
    }

    public Mono<List<HdsCallFeedbackEntity>> selectByRtcRoomId(String returnRoomId) {
        if (returnRoomId == null) return Mono.just(new ArrayList<>());
        Criteria criteria = Criteria.where(HdsCallFeedbackFieldEnum.rtc_room_id.name()).is(returnRoomId);
        Query query = Query.query( criteria).sort(Sort.by(Sort.Direction.DESC, HdsCallFeedbackFieldEnum.id.name())).limit(1);
        return r2dbcEntityTemplate.select(query, HdsCallFeedbackEntity.class).collectList();
    }
}
