package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketContentVo {

        private List<TicketFeedBackVo> feedbackList;

        @Data
        @Accessors(chain = true)
        @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
        public static class TicketFeedBackVo {

                private String ticketNo;

                private String content;

                private String rtcRoomId;

                private Integer feedbackDimension = 1;

                private boolean feedbackShow = true;
        }


}
