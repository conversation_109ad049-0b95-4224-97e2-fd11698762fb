package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AfterAllTicketCreateReq implements Serializable {
    private String rtcRoomId;
    private String conversationId;
}
