package com.wormhole.hotelds.api.hotel.constant;


import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FeedbackTypeEnum {

    LANGUAGE_EXPRESSION_NOT_CLEAR(1, "语言表达不清晰"),
    LACK_OF_HUMANITY(2, "缺乏人情味"),
    ANSWER_CONTENT_NOT_RELEVANT(3, "回答内容不贴题");


    private final Integer type;

    private final String desc;

    public static String getDescByType(Integer type) {
        return Arrays.stream(FeedbackTypeEnum.values())
                .filter(feedbackTypeEnum -> ObjectUtil.equal(type, feedbackTypeEnum.getType()))
                .findFirst()
                .map(FeedbackTypeEnum::getDesc)
                .orElse("");

    }
}
