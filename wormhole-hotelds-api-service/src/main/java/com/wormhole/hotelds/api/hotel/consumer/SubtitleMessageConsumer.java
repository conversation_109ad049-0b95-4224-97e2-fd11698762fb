package com.wormhole.hotelds.api.hotel.consumer;

import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.event.SubtitleEvent;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.service.CallLogService;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/5/6 11:44
 */

@Component
@RocketMQMessageListener(
        topic = "subtitle_message",
        consumerGroup = "hotelds_api_subtitle_message_group"
)
@Slf4j
public class SubtitleMessageConsumer extends AbstractReactiveMessageListener<MessageBody> {

    @Resource
    private CallLogService callLogService;

    @Override
    protected Mono<Void> processMessage(MessageBody payload) {
        SubtitleEvent subtitleEvent = JacksonUtils.convertValue(payload.getData(), SubtitleEvent.class);
        log.info("SubtitleMessageConsumer processMessage: {}", JacksonUtils.writeValueAsString(subtitleEvent));
        return callLogService.saveSubtitleEventCallLog(subtitleEvent)
                .doOnSuccess(result -> {
                    log.info("SubtitleMessageConsumer processMessage processed successfully: {}", JacksonUtils.writeValueAsString(subtitleEvent));
                }).doOnError(e -> log.error("Error SubtitleMessageConsumer processMessage: {}", JacksonUtils.writeValueAsString(subtitleEvent), e))
                .then();

    }
}
