package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.hotelds.api.hotel.web.model.req.GetCallLogQO;
import com.wormhole.hotelds.api.hotel.enums.CallMessageType;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsCallLogFieldEnum;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Repository
@Slf4j
public class HdsCallLogDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<List<HdsCallLogEntity>> findTicketsAnswerInCall(String conversationId, Collection<String> clientReqIdList) {
        // 构建查询条件
        Criteria criteria = Criteria.where(HdsCallLogFieldEnum.conversation_id.name()).is(conversationId)
                .and(HdsCallLogFieldEnum.client_req_id.name()).in(clientReqIdList)
                .and(HdsCallLogFieldEnum.message_type.name()).is(CallMessageType.ASSISTANT.getCode());
        // 构建查询对象
        Query query = Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, HdsCallLogFieldEnum.id.name()));
        // 执行查询并返回结果列表
        return r2dbcEntityTemplate.select(query, HdsCallLogEntity.class)
                .collectList();
    }

    public Mono<List<HdsCallLogEntity>> findList(GetCallLogQO qo, PageRequest pageRequest) {

        Query query = getQuery(qo, pageRequest);
        Sort sort = Sort.by(Sort.Direction.ASC, HdsCallLogFieldEnum.created_at.name());
        return r2dbcEntityTemplate.select(query.sort(sort),
                        HdsCallLogEntity.class)
                .collectList();
    }

    public Mono<HdsCallLogEntity> findOne(GetCallLogQO qo) {

        Query query = getQuery(qo, null);
        return r2dbcEntityTemplate.selectOne(query.limit(1),
                        HdsCallLogEntity.class);
    }

    @NotNull
    private static Query getQuery(GetCallLogQO qo, PageRequest pageRequest) {
        Criteria criteria = Criteria.empty();
        if (StringUtils.isNotBlank(qo.getConversationId())) {
            criteria = criteria.and(HdsCallLogFieldEnum.conversation_id.name()).is(qo.getConversationId());
        }
        if (StringUtils.isNotBlank(qo.getHotelCode())) {
            criteria = criteria.and(HdsCallLogFieldEnum.hotel_code.name()).is(qo.getHotelCode());
        }
        if (StringUtils.isNotBlank(qo.getRtcRoomId())) {
            criteria = criteria.and(HdsCallLogFieldEnum.rtc_room_id.name()).is(qo.getRtcRoomId());
        }
        if (Objects.nonNull(qo.getMaxId())) {
            criteria = criteria.and(HdsCallLogFieldEnum.id.name()).greaterThan(qo.getMaxId());
        }
        if(Objects.equals(qo.getRtcRoomIdIsNull(),Boolean.TRUE)) {
            criteria = criteria.and(HdsCallLogFieldEnum.rtc_room_id.name()).isNull();
        }

        Query query = Query.query(criteria);
        if (Objects.nonNull(pageRequest)) {
            query = query.with(pageRequest);
        }
        return query;
    }


    public Mono<Long> findMaxIdWithTicket(String conversationId) {
        log.info("findMaxIdWithTicket for conversation id: {}", conversationId);

        Criteria criteria = Criteria.where(HdsCallLogFieldEnum.conversation_id.name()).is(conversationId)
                .and(HdsCallLogFieldEnum.related_ticket_no.name()).isNotNull()
                .and(HdsCallLogFieldEnum.rtc_room_id.name()).isNull();

        return r2dbcEntityTemplate.select(HdsCallLogEntity.class)
                .matching(Query.query(criteria)
                        .columns(HdsCallLogFieldEnum.id.name())
                        .sort(Sort.by(Sort.Direction.DESC, HdsCallLogFieldEnum.id.name()))
                        .limit(1))
                .first()
                .map(HdsCallLogEntity::getId)
                .defaultIfEmpty(0L);
    }

    public Mono<Long> count(GetCallLogQO qo) {
        Query query = getQuery(qo, null);

        return r2dbcEntityTemplate.count(query, HdsCallLogEntity.class);
    }

    public Flux<HdsCallLogEntity> queryByClientReqIdList(CallMessageType callMessageType, Collection<String> clientReqIdList) {
        // 构建查询条件
        Criteria criteria = Criteria.where(HdsCallLogFieldEnum.client_req_id.name()).in(clientReqIdList)
                .and(HdsCallLogFieldEnum.message_type.name()).is(callMessageType.getCode());
        // 构建查询对象
        Query query = Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, HdsCallLogFieldEnum.id.name()));
        // 执行查询并返回结果列表
        return r2dbcEntityTemplate.select(query, HdsCallLogEntity.class);
    }

    public Mono<HdsCallLogEntity> updateCallLogWithTicketInfo(HdsCallLogEntity callLog, HdsServiceTicketEntity ticket) {
        // 设置日志绑定字段
        Query query = Query.query(Criteria.where(HdsCallLogFieldEnum.id.name()).is(callLog.getId()));

        return r2dbcEntityTemplate.update(HdsCallLogEntity.class)
                .matching(query)
                .apply(createUpdateFromCallLog(callLog))
                .thenReturn(callLog)
                .doOnSuccess(v -> log.info("绑定成功: ticketNo={}, logId={}", ticket.getTicketNo(), callLog.getId()))
                .onErrorResume(e -> {
                    log.error("绑定失败: ticketNo={}, error={}", ticket.getTicketNo(), e.getMessage());
                    return Mono.empty();
                });
    }

    private Update createUpdateFromCallLog(HdsCallLogEntity callLog) {
        return Update.update(HdsCallLogFieldEnum.service_type.name(), callLog.getServiceType())
                .set(HdsCallLogFieldEnum.service_category.name(), callLog.getServiceCategory())
                .set(HdsCallLogFieldEnum.service_subcategory.name(), callLog.getServiceSubcategory())
                .set(HdsCallLogFieldEnum.service_category_name.name(), callLog.getServiceCategoryName())
                .set(HdsCallLogFieldEnum.service_subcategory_name.name(), callLog.getServiceSubcategoryName())
                .set(HdsCallLogFieldEnum.related_ticket_no.name(), callLog.getRelatedTicketNo());
    }

    /**
     * 根据messageId更新反馈信息
     */
    public Mono<Long> updateFeedbackByMessageId(String messageId, Integer feedbackStatus, String feedbackTypes, String feedbackContent) {
        Criteria criteria = Criteria.where(HdsCallLogFieldEnum.message_id.name()).is(messageId);
        Query query = Query.query(criteria);

        Update update = Update.update(HdsCallLogFieldEnum.feedback_status.name(), feedbackStatus)
                .set(HdsCallLogFieldEnum.feedback_types.name(), feedbackTypes)
                .set(HdsCallLogFieldEnum.feedback_content.name(), feedbackContent);

        return r2dbcEntityTemplate.update(HdsCallLogEntity.class)
                .matching(query)
                .apply(update)
                .doOnSuccess(count -> log.info("更新反馈信息成功: messageId={}, feedbackStatus={}, feedbackTypes={}",
                        messageId, feedbackStatus, feedbackTypes))
                .onErrorResume(e -> {
                    log.error("更新反馈信息失败: messageId={}, error={}", messageId, e.getMessage());
                    return Mono.just(0L);
                });
    }

}
