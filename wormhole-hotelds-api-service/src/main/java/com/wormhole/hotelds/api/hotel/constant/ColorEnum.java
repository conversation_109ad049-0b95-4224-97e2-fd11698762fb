package com.wormhole.hotelds.api.hotel.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统颜色枚举，定义了标准的界面颜色
 */
@AllArgsConstructor
@Getter
public enum ColorEnum {
    
    /**
     * 蓝色
     */
    BLUE("#165DFF", "蓝色"),
    
    /**
     * 红色
     */
    RED("#F53F3F", "红色"),
    
    /**
     * 绿色
     */
    GREEN("#00B42A", "绿色"),
    
    /**
     * 灰色
     */
    GRAY("#898989", "灰色"),
    /**
     * 橙色
     */
    ORANGE("#f88b48","橙色" ) ,

    /**
     * 紫红色
     */
    PURPLE_RED("#EB008B", "紫红色"),
    /**
     * 黄色
     */
    YELLOW("#FFD700", "黄色"),
    /**
     * 黑色
     */
    BLACK("#000000", "黑色"),
    /**
     * 白色
     */
    WHITE("#FFFFFF", "白色")
    ;

    private final String hexCode;
    private final String description;


}