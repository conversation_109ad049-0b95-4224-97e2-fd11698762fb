package com.wormhole.hotelds.api.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * &#064;date  2025/4/9 13:50
 */
@Data
@ConfigurationProperties(WechatProperties.CONFIG_PREFIX)
@RefreshScope
public class WechatProperties {

    public static final String CONFIG_PREFIX = "wormhole.wechat.miniapp";

    private String qrcodePage;

    private String distance = "1000"; // 1 km in meters

    private String botCode = "bot_347bec7d61b2ff46823b2501cbf1f1b9";

}
