package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.date.DateUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class DateUtils{
    /**
     * 将 LocalDate 按照指定格式转换为字符串
     * @param date 待转换的日期
     * @param pattern 日期格式模式（如 "yyyy-MM-dd"、"yyyy/MM/dd"）
     * @return 格式化后的日期字符串
     */
    public static String formatLocalDate(LocalDate date, String pattern) {
        if (date == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

    public static String formatLocalDateTime(LocalDateTime date, String pattern) {
        if (date == null || pattern == null || pattern.isEmpty()) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(formatter);
    }

    public static void main(String[] args) {
        System.out.println(LocalDateTime.now());
        System.out.println(minutesBetweenNow(LocalDateTime.now()));
        System.out.println(minutesBetweenNow(DateUtil.parseLocalDateTime("2025-07-07 10:39:00", "yyyy-MM-dd HH:mm:ss")));
    }

    /**
     * 计算指定时间和现在相距的分钟数，向下取整，不足1分钟即为0
     * @param dateTime 指定的时间
     * @return 相距的分钟数（向下取整）
     */
    public static long minutesBetweenNow(LocalDateTime dateTime) {
        if (dateTime == null) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        return Math.abs(ChronoUnit.MINUTES.between(dateTime, now));
    }
}
