package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.api.hotel.req.UpdateReceiveStatusReq;
import com.wormhole.hotelds.api.hotel.web.model.req.EmployeeSingleReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetStaffRosterReq;
import com.wormhole.hotelds.api.hotel.web.model.req.SaveReceiveConfigReq;
import com.wormhole.hotelds.api.hotel.web.model.res.ReceiveTicketBlankResp;
import com.wormhole.hotelds.api.hotel.web.model.res.StaffRosterResp;
import com.wormhole.hotelds.api.hotel.web.service.EmployeeTicketMappingService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/account_ticket_mapping")
public class AccountTicketMappingController {

    @Autowired
    private EmployeeTicketMappingService employeeTicketMappingService;


    @PostMapping("/update_receive_status")
    public Mono<Result<Boolean>> updateReceiveStatus(@RequestBody UpdateReceiveStatusReq req){
        return employeeTicketMappingService.updateReceiveStatus(req).flatMap(Result::success);
    }

    @GetMapping("/receive_ticket/show_entry")
    public Mono<Result<Boolean>> showConfigEntry(){
        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> employeeTicketMappingService.showConfigEntry(header.getHotelCode(),header.getUserId()))
                .flatMap(Result::success);
    }

    @PostMapping("/receive_ticket/blanks")
    public Mono<Result<ReceiveTicketBlankResp>> receiveTicketBlanks(){
        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> {
                    EmployeeSingleReq build = getEmployeeSingleReq(header);
                    return employeeTicketMappingService.receiveTicketBlanks(build);
                })
                .flatMap(Result::success);
    }

    private static EmployeeSingleReq getEmployeeSingleReq(HeaderUtils.HeaderInfo header) {
        return EmployeeSingleReq.builder().hotelCode(header.getHotelCode()).userId(header.getUserId()).build();
    }

    @PostMapping("/receive_ticket/save")
    public Mono<Result<Boolean>> saveReceiveTicketConfig(@RequestBody SaveReceiveConfigReq req){
        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> employeeTicketMappingService.saveReceiveTicketConfig(req,getEmployeeSingleReq(header)))
                .flatMap(Result::success);
    }

    @PostMapping("/assign_ticket/blocks")
    public Mono<Result<List<String>>> assignTicketBlanks(){
        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> employeeTicketMappingService.assignTicketBlanks(header.getHotelCode()))
                .flatMap(Result::success);
    }

    @PostMapping("/assign_ticket/staff_roster")
    public Mono<Result<StaffRosterResp>> getStaffRoster(@RequestBody GetStaffRosterReq req){
        return HeaderUtils.getHeaderInfo()
                .flatMap(header -> employeeTicketMappingService.getStaffRoster(req,header.getHotelCode()))
                .flatMap(Result::success);
    }

    @PostMapping("/test/area_codes/init")
    public Mono<Result<Boolean>> initAreaCodes() {
        return employeeTicketMappingService.initAreaCodes().flatMap(Result::success);
    }
}
