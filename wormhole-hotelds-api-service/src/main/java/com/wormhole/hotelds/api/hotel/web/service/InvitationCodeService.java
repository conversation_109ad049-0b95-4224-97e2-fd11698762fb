package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.hotelds.api.hotel.web.dao.repository.InvitationCodeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author：flx
 * @Date：2025/5/22 16:51
 * @Description：InvitationCodeService
 */
@Service
@Slf4j
public class InvitationCodeService {

    @Resource
    private InvitationCodeRepository invitationCodeRepository;

    public Mono<Boolean> checkValid(String invitationCode) {
        return invitationCodeRepository.findByInvitationCode(invitationCode)
                .map(entity -> {
                    // 检查邀请码状态，只有状态为1(有效)才返回true
                    return entity != null && Objects.equals(entity.getStatus(), 1);
                })
                .defaultIfEmpty(false);
    }
}
