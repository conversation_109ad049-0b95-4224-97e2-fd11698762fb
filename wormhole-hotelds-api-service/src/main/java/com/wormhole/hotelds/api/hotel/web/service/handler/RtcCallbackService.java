package com.wormhole.hotelds.api.hotel.web.service.handler;

import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RtcCallbackService {
    
    private final List<RtcCallbackHandler> handlers;

    public Mono<Void> handleRtcCallStatusCallback(CallInfoMessage request) {
        log.info("RTC通话状态回调: {}", JacksonUtils.writeValueAsString(request));

        if (StringUtils.isBlank(request.getRtcRoomId())
                || StringUtils.isBlank(request.getInitiatorUserId())
                || Objects.isNull(request.getInitiatorType())
//                || StringUtils.isBlank(request.getReceiverUserId())
//                || Objects.isNull(request.getReceiverType()) todo 根据状态检查字段的方法
                || Objects.isNull(request.getCallStatus())) {
            log.error("CallInfoMessage 数据不完整，request:{}", JacksonUtils.writeValueAsString(request));
            return Mono.empty();
        }

        // 找出所有可以处理此请求的处理器
        List<Mono<Void>> operations = handlers.stream()
                .filter(handler -> handler.canHandle(request))
                .map(handler -> handler.handle(request))
                .collect(Collectors.toList());

        if (operations.isEmpty()) {
            return Mono.empty();
        }

        // 并行执行所有操作，等待全部完成
        return Mono.when(operations).then();
    }
}