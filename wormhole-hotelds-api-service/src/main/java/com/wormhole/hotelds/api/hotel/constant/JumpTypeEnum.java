package com.wormhole.hotelds.api.hotel.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum JumpTypeEnum {
    H5_SESSION(1, "直接进入H5会话"),
    CONFIRM_DIALOG(2, "展示确认弹窗"),
    SCAN_CODE(3, "直接进入扫码");
    
    private final Integer code;
    private final String description;
    

    public static JumpTypeEnum valueOf(Integer code) {
        for (JumpTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的跳转类型: " + code);
    }
}