package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MaintenanceReportRes {
    
    /**
     * 前3名维修房间统计
     */
    private List<Top3MaintenanceRoom> top3MaintenanceRoom = new ArrayList<>();
    
    /**
     * 前3名维修系统统计
     */
    private List<Top3MaintenanceSystem> top3MaintenanceSystem = new ArrayList<>();


    public MaintenanceReportRes addTop3MaintenanceRoom(Top3MaintenanceRoom top3MaintenanceRoom)
    {
        this.top3MaintenanceRoom.add(top3MaintenanceRoom);
        return this;
    }

    public MaintenanceReportRes addTop3MaintenanceSystem(Top3MaintenanceSystem top3MaintenanceSystem)
    {
        this.top3MaintenanceSystem.add(top3MaintenanceSystem);
        return this;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Top3MaintenanceRoom {

        /**
         * 房间编码
         */
        private String roomCode;

        /**
         * 房间名称
         */
        private String roomName;

        private Integer totalCount;

        /**
         * 维修详细信息列表
         */
        private List<MaintenanceDetailInfo> maintenanceDetailInfos;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public  static class Top3MaintenanceSystem {

        /**
         * 服务类别编码
         */
        private String serviceCategory;

        /**
         * 服务类别名称
         */
        private String serviceCategoryName;

        private Integer totalCount;


        /**
         * 维修详细信息列表
         */
        private List<MaintenanceDetailInfo> maintenanceDetailInfos;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class MaintenanceDetailInfo {

        /**
         * 服务关键词
         */
        private String serviceKeyword;

        /**
         * 数量/次数
         */
        private Integer count;
    }


}