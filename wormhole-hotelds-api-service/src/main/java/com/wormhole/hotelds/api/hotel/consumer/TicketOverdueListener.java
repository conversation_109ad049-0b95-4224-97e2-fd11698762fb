package com.wormhole.hotelds.api.hotel.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.SystemConstant;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.constant.ExpiredFlagEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.web.model.req.SingleTicketReq;
import com.wormhole.hotelds.api.hotel.web.service.CallBackBizService;
import com.wormhole.hotelds.core.enums.TicketStatus;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Component
@RocketMQMessageListener(topic = SystemConstant.TICKET_OVERDUE_EVENT_TOPIC,
        consumerGroup =  SystemConstant.TICKET_OVERDUE_CONSUMER_GROUP)
@Slf4j
public class TicketOverdueListener extends AbstractReactiveMessageListener<SingleTicketReq> {
    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private CallBackBizService callBackBizService;
    @Override
    protected Mono<Void> processMessage(SingleTicketReq payload) {
        SingleTicketReq ticketReq = JacksonUtils.convertValue(payload, SingleTicketReq.class);
        log.info("TicketOverdueListener monitor payload {}   ", JacksonUtils.writeValueAsString(payload));
        TicketInfoReq infoReq = new TicketInfoReq();
        BeanUtils.copyProperties(ticketReq, infoReq);
        return hdsServiceTicketDao.findOneTicket(infoReq).flatMap(ticket -> {
            if (ticket == null || ObjectUtil.equal(ticket.getStatus(), TicketStatus.COMPLETED.getCode())) {
                return Mono.empty();
            }
            // 工单超时 + 发送工单刷新指令
            HdsTicketQO hdsTicketQO = HdsTicketQO
                    .builder()
                    .ticketStatus(TicketStatus.PENDING.getCode())
                    .rtcRoomId(ticket.getRtcRoomId())
                    .expiredFlag(ExpiredFlagEnum.NORMAL.getCode())
                    .ticketNos(Lists.newArrayList(ticket.getTicketNo()))
                    .hasEndOfCall(true)
                    .build();
            return hdsServiceTicketDao.updateTicketOverdue(ticket.getTicketNo())
                    .then(callBackBizService.sendTicketCommandRefresh(hdsTicketQO,false));
        });
    }
}
