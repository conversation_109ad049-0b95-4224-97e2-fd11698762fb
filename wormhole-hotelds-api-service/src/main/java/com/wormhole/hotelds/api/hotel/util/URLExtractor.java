package com.wormhole.hotelds.api.hotel.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class URLExtractor {

    public static Set<String> getSubUrls(String url) {
        Set<String> subUrls = new HashSet<>();
        try {
            // 解析网页
            Document document = Jsoup.connect(url).get();
            // 获取所有<a>标签
            Elements links = document.select("a[href]");
            
            for (Element link : links) {
                String subUrl = link.attr("abs:href"); // 获取绝对路径的 URL
                if (subUrl.startsWith("http")) { // 只添加有效的 URL
                    subUrls.add(subUrl);
                }
            }
        } catch (IOException e) {
            System.err.println("无法访问 URL: " + url);
            e.printStackTrace();
        }
        return subUrls.stream().limit(3).collect(Collectors.toSet());
    }

    public static void main(String[] args) {
        String url = "https://example.com"; // 替换成你想要解析的 URL
        Set<String> subUrls = getSubUrls(url);
        for (String subUrl : subUrls) {
            System.out.println(subUrl);
        }
    }
}
