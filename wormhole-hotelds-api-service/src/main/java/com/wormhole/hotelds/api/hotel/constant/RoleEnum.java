package com.wormhole.hotelds.api.hotel.constant;

import lombok.*;

import java.util.*;
import java.util.stream.*;

@Getter
@AllArgsConstructor
public enum RoleEnum {

    ADMIN("ADMIN", "管理员"),
    SERVICE_PROVIDER("SERVICE_PROVIDER", "服务商"),
    HOTEL_ADMIN("HOTEL_ADMIN", "AI客服管理员"),
    HOTEL_STAFF("HOTEL_STAFF", "AI客服员工"),

    OTA_AGENT_ADMIN("OTA_AGENT_ADMIN", "OTA智能体管理员"),
    OTA_AGENT_STAFF("OTA_AGENT_STAFF", "OTA智能体员工");

    private final String code;
    private final String name;

    private static final Map<String, RoleEnum> CODE_MAP = Stream.of(values())
            .collect(Collectors.toMap(RoleEnum::getCode, e -> e));

    public static RoleEnum fromCode(String code) {
        return CODE_MAP.getOrDefault(code, null);
    }
}
