package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.*;
import com.wormhole.hotelds.api.hotel.resp.*;
import com.wormhole.hotelds.api.hotel.web.service.EmployeeService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.*;
import java.util.List;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/16 19:54
 */
@RestController
@RequestMapping("/employee")
public class EmployeeController {

    @Resource
    private EmployeeService employeeService;

    @PostMapping("/hasAccess")
    public Mono<EmployeeHotelAccessResp> hasAccess(@RequestParam("employee_id") Integer employeeId,
                                                   @RequestParam(value = "hotel_code" , required = false) String hotelCode,
                                                   @RequestParam("source") String source) {
        return employeeService.hasAccess(employeeId, hotelCode, source);
    }

    /**
     * 获取用户酒店信息
     *
     * @param employeeId
     * @param type 员工类型 1-集团 2-服务商 3-酒店
     * @return
     */
    @PostMapping("/getUserHotelInfo")
    public Mono<UserHotelInfoResp> getUserHotelInfo(@RequestParam("employee_id") Integer employeeId,
                                                    @RequestParam("type") Integer type) {
        return employeeService.getUserHotelInfo(employeeId, type);
    }

    /**
     * 根据员工ID获取酒店列表
     *
     * @return 酒店列表
     */
    @GetMapping("/getHotelsByEmployeeId")
    public Mono<Result<List<HotelInfo>>> getHotelsByEmployeeId() {
        return employeeService.getHotelsByEmployeeId().flatMap( Result::success);
    }
}
