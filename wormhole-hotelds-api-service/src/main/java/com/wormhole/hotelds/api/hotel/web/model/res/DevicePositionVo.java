package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DevicePositionVo {
    private String hotelCode;

    private String hotelName;

    private String positionCode;

    private String positionName;

    private String deviceId;

    private LocalDateTime currentTime;

    private String hotelPhone;
}
