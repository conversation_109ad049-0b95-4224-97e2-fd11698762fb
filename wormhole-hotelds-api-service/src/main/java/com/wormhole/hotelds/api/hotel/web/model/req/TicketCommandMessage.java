package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketCommandMessage implements Serializable {
    private Boolean refresh = false; //是否刷新
    private Boolean hasSound = false; //是否有声音

    // 是否有新增的sos未完成订单
    @Builder.Default
    private Boolean hasSos = false;
    // 刷新频率
    private Integer refreshIntervalSeconds = 5;
}
