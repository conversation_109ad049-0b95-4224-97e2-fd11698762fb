package com.wormhole.hotelds.api.hotel.util;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

public class MathUtils {

    /**
     * 四舍五入
     *
     * @param value
     * @param scale
     * @return
     */
    public static double round(double value, int scale) {
        return BigDecimal.valueOf(value).setScale(scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 除法
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    public static double division(double a, double b, int scale) {
        if (b != 0) {
            return BigDecimal.valueOf(a).divide(BigDecimal.valueOf(b), scale, RoundingMode.HALF_UP).doubleValue();
        }
        return BigDecimal.ZERO.doubleValue();
    }

    public static Long addValue(Long a, Long b) {
        if (Objects.isNull(a)) {
            return Optional.ofNullable(b).orElse(0L);
        }
        if (Objects.isNull(b)) {
            return Optional.of(a).orElse(0L);
        }
        return a + b;
    }

    public static Double addValue(Double a, Double b) {
        if (Objects.isNull(a)) {
            return Optional.ofNullable(b).orElse(0D);
        }
        if (Objects.isNull(b)) {
            return Optional.of(a).orElse(0D);
        }
        return new BigDecimal(a).add(new BigDecimal(b)).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    public static void main(String[] args) {
        System.out.println(division(0d, 6d, 2));
        System.out.println(division(1d, 6d, 2));

        // 1.result = 35.35
        BigDecimal round = new BigDecimal("35.3456").round(new MathContext(4, RoundingMode.HALF_UP));
        System.out.println(round);

        // 2.result = 35.3456
        BigDecimal bigDecimal = new BigDecimal("35.3456").setScale(4, RoundingMode.HALF_UP);
        System.out.println(bigDecimal);
    }


}
