package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KbSearchReq implements Serializable {

    /**
     * 空间code
     */
    private String spaceCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 最小分数
     */
    private Number minScore;

    /**
     * topK
     */
    private Integer topK;

    /**
     * 查询问题
     */
    private String query;

}
