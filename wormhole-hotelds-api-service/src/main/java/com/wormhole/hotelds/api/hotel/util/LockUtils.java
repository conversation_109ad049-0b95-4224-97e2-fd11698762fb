package com.wormhole.hotelds.api.hotel.util;

import cn.hutool.core.date.SystemClock;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LockUtils {

    @Autowired
    private ReactiveStringRedisTemplate redisTemplate;

    public Mono<Boolean> lock(String lockKey, Integer milliseconds) {
        Duration lockTime = Duration.ofMillis(milliseconds);
        return redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "lock", lockTime)
                .doOnNext(locked -> {
                    if (locked) {
                        log.info("Acquired lock for lockKey: {}", lockKey);
                    } else {
                        log.info("Failed to acquire lock for lockKey: {}", lockKey);
                    }
                });
    }

    public Mono<Void> unLock(String lockKey) {
        return redisTemplate.delete(lockKey)
                .doOnSuccess(result -> log.info("Released lock for lockKey: {}", lockKey))
                .then();
    }

    public <T> Mono<T> lock(String lockKey, Integer milliseconds, Supplier<Mono<T>> supplier) {
        return lock(lockKey, milliseconds)
                .filter(lockResult -> lockResult)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.CONFLICT, "操作繁忙，请稍后再试")))
                .flatMap(locked -> supplier.get()
                        .doFinally(signalType -> unLock(lockKey).subscribe()));
    }

    public <T> Mono<T> tryLock(String lockKey, long waitTimeMs, Integer leaseTimeMs, Supplier<Mono<T>> supplier) {
        return attemptLockWithAction(lockKey, waitTimeMs, leaseTimeMs, SystemClock.now(), supplier)
                .doOnNext(result -> {
                    if (result == null) {
                        log.warn("Try lock failed after waiting for lockKey: {}", lockKey);
                    }
                });
    }

    private <T> Mono<T> attemptLockWithAction(String lockKey, long waitTimeMs, Integer leaseTimeMs, long startTime, Supplier<Mono<T>> supplier) {
        return lock(lockKey, leaseTimeMs)
                .flatMap(locked -> {
                    if (locked) {
                        // 成功获取锁，执行业务逻辑
                        return supplier.get()
                                .doFinally(signalType -> unLock(lockKey).subscribe());
                    } else {
                        // 检查是否超时
                        long elapsedTime = SystemClock.now() - startTime;
                        if (elapsedTime >= waitTimeMs) {
                            return Mono.empty(); // 超时未获取锁，返回空结果
                        }
                        // 等待一段时间后重试
                        return Mono.delay(Duration.ofMillis(100)) // 等待 100ms 后重试
                                .then(attemptLockWithAction(lockKey, waitTimeMs, leaseTimeMs, startTime, supplier));
                    }
                });
    }

}