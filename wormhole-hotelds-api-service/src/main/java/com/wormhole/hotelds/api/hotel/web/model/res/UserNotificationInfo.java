package com.wormhole.hotelds.api.hotel.web.model.res;

import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@Data
public class UserNotificationInfo {
    private List<String> userIds;
    private boolean hasSound = false;

    public static UserNotificationInfo build(boolean defaultSoundEnabled, HdsServiceTicketEntity entity, List<String> userIds ) {
        UserNotificationInfo userNotificationInfo = new UserNotificationInfo();
        userNotificationInfo.setUserIds(userIds);
        userNotificationInfo.setHasSound(defaultSoundEnabled && !TicketUtils.autoComplete(entity));
        return userNotificationInfo;
    }

}
