package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.hotelds.api.hotel.web.dao.repository.TicketLogsRepository;
import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Repository
public class HdsTicketLogsDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Autowired
    private TicketLogsRepository ticketLogsRepository;

    public Mono<List<HdsTicketLogsEntity>> getLogsByTicketNo(String ticketNo) {
        Criteria criteria = Criteria.where(HdsServiceTicketFieldEnum.ticket_no.name()).is(ticketNo);
        Query logsQuery = Query.query(criteria).sort(Sort.by(Sort.Direction.DESC, HdsServiceTicketFieldEnum.id.name()));
        return r2dbcEntityTemplate.select(logsQuery, HdsTicketLogsEntity.class).collectList();
    }

    public Mono<Boolean> batchInsert(List<HdsTicketLogsEntity> info) {
        return ticketLogsRepository.saveAll(info).then(Mono.just(true));
    }
}
