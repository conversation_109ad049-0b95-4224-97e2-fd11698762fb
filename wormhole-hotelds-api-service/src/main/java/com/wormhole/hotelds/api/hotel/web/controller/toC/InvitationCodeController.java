package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.hotelds.api.hotel.web.service.InvitationCodeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * @Author：flx
 * @Date：2025/5/22 16:46
 * @Description：InvitationCodeController
 */
@RestController
@RequestMapping("/invitation_code")
public class InvitationCodeController {

    @Resource
    private InvitationCodeService invitationCodeService;

    @PostMapping("/checkValid")
    public Mono<Boolean> checkValid(@RequestParam("invitation_code") String invitationCode) {
        return invitationCodeService.checkValid(invitationCode);
    }
}
