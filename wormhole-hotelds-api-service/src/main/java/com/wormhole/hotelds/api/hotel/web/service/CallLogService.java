package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.client.chat.client.AgentApiClient;
import com.wormhole.agent.client.chat.params.query.BotInfoQuery;
import com.wormhole.agent.client.chat.response.BotInfoResponse;
import com.wormhole.channel.consts.event.SubtitleEvent;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.FeedbackTypeEnum;
import com.wormhole.hotelds.api.hotel.enums.CallMessageType;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallLogDao;
import com.wormhole.hotelds.api.hotel.web.model.req.CallLogFeedbackReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetCallLogQO;
import com.wormhole.hotelds.api.hotel.web.model.req.PageCallLogQO;
import com.wormhole.hotelds.api.hotel.web.model.req.SimpleCallLogReq;
import com.wormhole.hotelds.api.hotel.web.model.res.HdsCallLogSimpleVO;
import com.wormhole.hotelds.api.hotel.web.model.res.HdsCallLogVO;
import com.wormhole.hotelds.api.hotel.web.dao.repository.ServiceTicketRepository;
import com.wormhole.hotelds.core.enums.CallTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Service
@Slf4j
public class CallLogService {

    @Autowired
    private HdsCallLogDao hdsCallLogDao;
    @Autowired
    private AgentApiClient agentApiClient;

    @Autowired
    private ServiceTicketRepository serviceTicketRepository;

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;



    public Mono<PageResult<HdsCallLogVO>> page(PageCallLogQO pageCallLogQO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(pageCallLogQO.getConversationId()), "会话id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(pageCallLogQO.getHotelCode()), "酒店code不能为空");

        GetCallLogQO query = GetCallLogQO.builder()
                .conversationId(pageCallLogQO.getConversationId())
                .hotelCode(pageCallLogQO.getHotelCode())
                .build();

        return serviceTicketRepository.findById(NumberUtils.toLong(pageCallLogQO.getTicketId()))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Ticket not found")))
                .map(ticket -> {
                    query.setRtcRoomId(ticket.getRtcRoomId());
                    if (ticket.getRtcRoomId() == null){
                        query.setRtcRoomIdIsNull(true);
                    }
                    return query;
                })
                .flatMap(callQuery -> getPageResultMono(pageCallLogQO, callQuery));
    }

    private Mono<PageResult<HdsCallLogVO>> getPageResultMono(PageCallLogQO pageCallLogQO, GetCallLogQO query ) {
        PageRequest pageRequest = PageRequest.of(pageCallLogQO.getCurrentPage() - 1, pageCallLogQO.getPageSize());
        log.info("CallLogService.getPageResultMono: query: {}", JacksonUtils.writeValueAsString(query));
        Mono<List<HdsCallLogEntity>> listMono = hdsCallLogDao.findList(query, pageRequest);
        Mono<Long> countMono = hdsCallLogDao.count(query);

        return Mono.zip(listMono, countMono)
                .flatMap(tuple -> {
                    List<HdsCallLogEntity> entities = tuple.getT1();
                    Long totalCount = tuple.getT2();

                    if (entities.isEmpty()) {
                        return Mono.just(PageResult.empty(pageCallLogQO.getCurrentPage(), pageCallLogQO.getPageSize()));
                    }

                    String botCode = entities.get(0).getBotCode();
                    BotInfoQuery botInfoQuery = new BotInfoQuery();
                    botInfoQuery.setBotCode(botCode);

                    return agentApiClient.getBotInfo(botInfoQuery)
                            .defaultIfEmpty(new BotInfoResponse()) // Handle empty response
                            .onErrorResume(e -> {
                                log.error("Failed to get bot info for code {}: {}", botCode, e.getMessage());
                                return Mono.just(new BotInfoResponse());
                            })
                            .map(botInfo -> {
                                List<HdsCallLogVO> voList = entities.stream()
                                        .map(entity -> {
                                            HdsCallLogVO vo = new HdsCallLogVO();
                                            BeanUtils.copyProperties(entity, vo);
                                            vo.setCreatedAtTimeStamp(LocalDateTimeUtil.toEpochMilli(entity.getCreatedAt()));
                                            vo.setBotName(botInfo.getName()); // Set bot name from response
                                            return vo;
                                        })
                                        .collect(Collectors.toList());

                                return PageResult.create(totalCount, voList,
                                        pageCallLogQO.getCurrentPage(), pageCallLogQO.getPageSize());
                            });
                });
    }

    /**
     * 工单维度绑定calllog
     * @param entity
     * @return
     */

    public Mono<Void> ticketServiceBindCallLog(HdsServiceTicketEntity entity) {
        if (Objects.isNull(entity)) {
            log.info("ticketServiceBindCallLog: entity 为空");
            return Mono.empty();
        }
        log.info("CallLogService.ticketServiceBindCallLog: entity: {}", JacksonUtils.writeValueAsString(entity));
        Map<String, HdsServiceTicketEntity> ticketMap = Collections.singletonMap(entity.getClientReqId(), entity);
        return bindCallLogsToTickets(ticketMap)
                .onErrorResume(e -> {
                    log.error("ticketServiceBindCallLog: {}", e.getMessage());
                    return Mono.empty();
                })
                .then();
    }
    /**
     * 通话结束后绑定工单数据
     * @param rtcRoomId
     * @return
     */
    public Mono<Void> callLogBindServiceTicket(String rtcRoomId) {
        if (StringUtils.isBlank(rtcRoomId)) {
            log.info("callLogBindServiceTicket: rtcRoomId 为空");
            return Mono.empty();
        }
        log.info("CallLogService.callLogBindServiceTicket: rtcRoomId: {}", rtcRoomId);
        long startTime = System.currentTimeMillis();

        return serviceTicketRepository.findByRtcRoomId(rtcRoomId)
                .filter(ticket -> StringUtils.isNotBlank(ticket.getClientReqId()))
                .collectMap(HdsServiceTicketEntity::getClientReqId, Function.identity())
                .filter(ticketMap -> !ticketMap.isEmpty())
                .flatMap(this::bindCallLogsToTickets)
                .doFinally(signalType -> {
                    long elapsed = System.currentTimeMillis() - startTime;
                    log.info("callLogBindServiceTicket: 绑定耗时 {} ms", elapsed);
                })
                .onErrorResume(e -> {
                    log.error("callLogBindServiceTicket: {}", e.getMessage());
                    return Mono.empty();
                })
                .then();
    }

    private Mono<List<HdsCallLogEntity>> bindCallLogsToTickets(Map<String, HdsServiceTicketEntity> ticketMap) {
        return hdsCallLogDao.queryByClientReqIdList(CallMessageType.USER, ticketMap.keySet())
                .flatMap(callLog -> {
                    HdsServiceTicketEntity ticket = ticketMap.get(callLog.getClientReqId());
                    if (ticket == null) {
                        return Mono.empty();
                    }
                    ;
                    return hdsCallLogDao.updateCallLogWithTicketInfo(buildCallLogWithTicketInfo(callLog, ticket), ticket);
                })
                .collectList()
                .doOnNext(updatedLogs -> logBindingResults(updatedLogs, ticketMap.size()));
    }

    private HdsCallLogEntity buildCallLogWithTicketInfo(HdsCallLogEntity callLog, HdsServiceTicketEntity ticket) {
        // 设置日志绑定字段
        callLog.setServiceType(ticket.getServiceType());
        callLog.setServiceCategory(ticket.getServiceCategory());
        callLog.setServiceSubcategory(ticket.getServiceSubcategory());
        callLog.setServiceCategoryName(ticket.getServiceCategoryName());
        callLog.setServiceSubcategoryName(ticket.getServiceSubcategoryName());
        callLog.setRelatedTicketNo(ticket.getTicketNo());
        return callLog;
    }

    private void logBindingResults(List<HdsCallLogEntity> updatedLogs, int ticketCount) {
        if (updatedLogs.isEmpty()) {
            log.warn("callLogBindTicketService 未找到匹配的通话记录可绑定, 工单数: {}", ticketCount);
        } else {
            log.info("callLogBindTicketService 成功绑定: {}条通话记录/{}个工单", updatedLogs.size(), ticketCount);
        }
    }

    public Mono<String> getSimpleCallLogJson(SimpleCallLogReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getRtcRoomId()) || StringUtils.isNotBlank(req.getConversationId()), "会话id和rtcRoomId不能同时为空");
        if (StringUtils.isNotBlank(req.getRtcRoomId())) {
            GetCallLogQO getCallLogQO = GetCallLogQO
                    .builder()
                    .conversationId(req.getConversationId())
                    .rtcRoomId(req.getRtcRoomId()).build();
            return getCallLogSimpleJson(getCallLogQO).doOnNext(result -> log.info("req:{},getSimpleCallLogJson: {}", JacksonUtils.writeValueAsString(req),JacksonUtils.writeValueAsString(result)));
        } else {
            return hdsCallLogDao.findMaxIdWithTicket(req.getConversationId()).flatMap(maxId -> {
                GetCallLogQO getCallLogQO = GetCallLogQO
                        .builder()
                        .conversationId(req.getConversationId())
                        .maxId(maxId)
                        .rtcRoomIdIsNull(true).build();
                return getCallLogSimpleJson(getCallLogQO).doOnNext(result -> log.info("req:{},getCallLogQo:{},getSimpleCallLogJson: {}", JacksonUtils.writeValueAsString(req)
                        ,JacksonUtils.writeValueAsString(getCallLogQO),JacksonUtils.writeValueAsString(result)));
            });
        }
    }

    @NotNull
    private Mono<String> getCallLogSimpleJson(GetCallLogQO getCallLogQO) {
        return hdsCallLogDao.findList(getCallLogQO, null).map(hdsCallLogEntities -> {
            if (CollUtil.isEmpty(hdsCallLogEntities)) {
                return Strings.EMPTY;
            }

            List<HdsCallLogEntity> result = hdsCallLogEntities;
            if (Objects.equals(hdsCallLogEntities.get(0).getMessageType(), CallMessageType.ASSISTANT.getCode())) {
                result = hdsCallLogEntities.subList(1, hdsCallLogEntities.size());
            }

            List<HdsCallLogSimpleVO> collect = result.stream().map(entity -> {
                HdsCallLogSimpleVO hdsCallLogSimpleVO = new HdsCallLogSimpleVO();
                BeanUtils.copyProperties(entity, hdsCallLogSimpleVO);
                return hdsCallLogSimpleVO;
            }).collect(Collectors.toList());
            return JacksonUtils.writeValueAsString(collect);
        });
    }


    public Mono<Void> saveSubtitleEventCallLog(SubtitleEvent subtitleEvent) {
        if (Objects.isNull(subtitleEvent)) {
            log.info("saveSubtitleEventCallLog: subtitleEvent 为空");
            return Mono.empty();
        }
        log.info("CallLogService.saveSubtitleEventCallLog: subtitleEvent: {}", JacksonUtils.writeValueAsString(subtitleEvent));
        return Mono.defer(() -> {
                    HdsCallLogEntity entity = mapToCallLogEntity(subtitleEvent);
                    return r2dbcEntityTemplate.insert(entity);
                })
                .doOnError(e -> log.error("Failed to save subtitle event call log", e))
                .onErrorResume(e -> Mono.empty())
                .then();
    }

    private HdsCallLogEntity mapToCallLogEntity(SubtitleEvent subtitleEvent) {
        HdsCallLogEntity callLog = new HdsCallLogEntity();
        callLog.setCallType(CallTypeEnum.PERSONAL_CALL.getType());
        callLog.setContent(subtitleEvent.getText());
        callLog.setRtcRoomId(subtitleEvent.getRtcRoomId());
        callLog.setDeviceId(subtitleEvent.getDeviceId());
        callLog.setClientReqId(String.valueOf(subtitleEvent.getSequence()));
        callLog.setHotelCode(subtitleEvent.getHotelCode());
        callLog.setPositionCode(subtitleEvent.getPositionCode());
        callLog.setCreatedAt(LocalDateTimeUtil.now());
        callLog.setCreatedBy(subtitleEvent.getUserId());
        callLog.setCreatedByName(subtitleEvent.getUserId());
        return callLog;
    }

    /**
     * 处理AI消息反馈
     */
    public Mono<Boolean> feedback(CallLogFeedbackReq req) {
        Preconditions.checkArgument(StrUtil.isNotBlank(req.getMessageId()), "消息ID不能为空");
        Preconditions.checkArgument(req.getFeedbackStatus() != null, "反馈状态不能为空");
        Preconditions.checkArgument(Objects.equals(req.getFeedbackStatus(), 1) || Objects.equals(req.getFeedbackStatus(), 2), "反馈状态只能是1(点赞)或2(点踩)");

        // 如果是点踩，反馈类型不能为空
        if (Objects.equals(req.getFeedbackStatus(), 2)) {
            Preconditions.checkArgument(StrUtil.isNotBlank(req.getFeedbackTypes()), "点踩时反馈类型不能为空");
            // 根据反馈类型组装反馈内容
            req.setFeedbackContent(buildFeedbackContent(req.getFeedbackTypes()));
        }

        log.info("CallLogService.feedback: req: {}", JacksonUtils.writeValueAsString(req));

        return hdsCallLogDao.updateFeedbackByMessageId(
                req.getMessageId(),
                req.getFeedbackStatus(),
                req.getFeedbackTypes(),
                req.getFeedbackContent()
        ).flatMap(count -> {
            if (count > 0) {
                log.info("反馈更新成功: messageId={}, feedbackStatus={}, feedbackTypes={}", req.getMessageId(), req.getFeedbackStatus(), req.getFeedbackTypes());
                return Mono.just(true);
            } else {
                log.warn("反馈更新失败，未找到对应的消息: messageId={}", req.getMessageId());
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "反馈失败"));
            }
        });
    }

    private String buildFeedbackContent(String feedbackTypes) {
        return Arrays.stream(feedbackTypes.split(","))
                .map(String::trim)
                .map(Integer::valueOf)
                .map(FeedbackTypeEnum::getDescByType)
                .collect(Collectors.joining(","));
    }
}
