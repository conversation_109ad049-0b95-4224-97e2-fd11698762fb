package com.wormhole.hotelds.api.hotel.util;

import org.apache.commons.lang3.StringUtils;

public class ValidatorUtils {

    // 公共校验方法
    public static void requireNonNull(Object object, String errorMessage) {
        if (object == null) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    public static void assertNotBlank(String value, String errorMessage) {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException(errorMessage);
        }
    }
}
