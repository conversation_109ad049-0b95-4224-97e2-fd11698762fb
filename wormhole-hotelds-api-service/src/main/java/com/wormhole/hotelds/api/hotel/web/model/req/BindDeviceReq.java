package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BindDeviceReq {
    private String  deviceId;
    private String  deviceSn;
    private String  imei;
    private String  hotelCode;

    private String  positionCode;
    private String  deviceType;
}