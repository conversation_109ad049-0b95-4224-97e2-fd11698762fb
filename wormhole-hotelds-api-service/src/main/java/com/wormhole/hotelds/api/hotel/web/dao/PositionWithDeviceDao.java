package com.wormhole.hotelds.api.hotel.web.dao;

import com.google.common.collect.Lists;
import com.wormhole.hotelds.api.hotel.util.SqlUtils;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionFieldEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/14
 */
@Repository
public class PositionWithDeviceDao {
    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;


    public Mono<List<HdsDevicePositionEntity>> getHdsDevicePositions(String hotelCode, String positionKey, String deviceType) {
        if (StringUtils.isBlank(hotelCode) && StringUtils.isBlank(positionKey) && StringUtils.isBlank(deviceType)){
            return Mono.just(Lists.newArrayList());
        }
        Criteria room = Criteria.empty() ;
        if (StringUtils.isNotBlank(hotelCode)){
            room = room.and(HdsDevicePositionFieldEnum.hotel_code.name()).is(hotelCode);
        }
        if (StringUtils.isNotBlank(deviceType)){
            room = room.and(HdsDevicePositionFieldEnum.device_app_type.name()).is(deviceType);
        }
        if (StringUtils.isNotBlank(positionKey)){
            // 创建 OR 条件的嵌套组合
            Criteria orConditions = Criteria.empty().or(HdsDevicePositionFieldEnum.block.name()).like(SqlUtils.like(positionKey))
                    .or(HdsDevicePositionFieldEnum.area.name()).like(SqlUtils.like(positionKey))
                    .or(HdsDevicePositionFieldEnum.number.name()).like(SqlUtils.like(positionKey))
                    .or(HdsDevicePositionFieldEnum.position_name.name()).like(SqlUtils.like(positionKey));
            // 将 OR 条件组添加到主查询条件中（用 AND 连接）
            room = room.and(orConditions);
        }
        Query sort = Query.query(room)
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.app_type_sort_order.name()))
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.block_area_sort_order.name()))
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.position_sort_order.name()));
        return r2dbcEntityTemplate.select(sort, HdsDevicePositionEntity.class).collectList();
    }


    public Mono<HdsDevicePositionEntity> getOnePosition(String positionCode) {
        Criteria room = Criteria.where(HdsDevicePositionFieldEnum.position_code.name()).is(positionCode);
        Query sort = Query.query(room)
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.id.name()))
                .limit(1);
        return r2dbcEntityTemplate.selectOne(sort, HdsDevicePositionEntity.class);
    }

    public Mono<List<HdsDevicePositionEntity>> getPositionList(Collection<String> positionCodes) {
        Criteria room = Criteria.where(HdsDevicePositionFieldEnum.position_code.name()).in(positionCodes);
        Query sort = Query.query(room)
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.app_type_sort_order.name()))
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.block_area_sort_order.name()))
                .sort(Sort.by(Sort.Direction.ASC, HdsDevicePositionFieldEnum.position_sort_order.name()));
        return r2dbcEntityTemplate.select(sort, HdsDevicePositionEntity.class).collectList();
    }
}
