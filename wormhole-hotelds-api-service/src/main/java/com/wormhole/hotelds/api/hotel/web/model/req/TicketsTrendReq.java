package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import java.time.LocalDate;

/**
 * 近7天工单数量趋势请求体
 * 用于查询指定酒店在指定日期区间的工单数量趋势
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TicketsTrendReq {
    /** 酒店编码 */
    private String hotelCode;
    /** 查询开始日期（yyyy-MM-dd） */
    private LocalDate startDate;
    /** 查询结束日期（yyyy-MM-dd） */
    private LocalDate endDate;
} 