package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.agent.core.model.entity.UserConversationEntity;
import com.wormhole.agent.core.model.entity.UserConversationFieldEnum;
import com.wormhole.common.enums.SourcePlatform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 19:39
 */
@Service
@Slf4j
public class UserConversationService {

    @Value("${wormhole.ticket.cleanHour:14}")
    private Integer hourConfig;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;



    public Mono<List<UserConversationEntity>> findYesterdayHotelConversations(Long idx, int pageSize) {

        // 设置结束时间为当前时间
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(new Date());

        // 设置结束时间为今天的指定小时
        endTime.set(Calendar.HOUR_OF_DAY, hourConfig);
        endTime.set(Calendar.MINUTE, 0);
        endTime.set(Calendar.SECOND, 0);
        endTime.set(Calendar.MILLISECOND, 0);

        // 设置开始时间为昨天的同一时间点
        Calendar startTime = (Calendar) endTime.clone();
        startTime.add(Calendar.DAY_OF_MONTH, -1);

        // 转换为LocalDateTime，这是R2DBC更好支持的日期时间类型
        LocalDateTime startDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 构建时间范围查询条件
        Criteria criteria = Criteria
                .where(UserConversationFieldEnum.created_at.name()).greaterThanOrEquals(startDateTime)
                .and(UserConversationFieldEnum.created_at.name()).lessThan(endDateTime)
                .and(UserConversationFieldEnum.source.name()).is(SourcePlatform.HOTEL.getCode())
                .and(UserConversationFieldEnum.id.name()).greaterThan(idx);

        // 构建查询并执行
        return r2dbcEntityTemplate.select(
                        Query.query(criteria).sort(Sort.by(Sort.Direction.ASC, UserConversationFieldEnum.id.name())).limit(pageSize),
                        UserConversationEntity.class)
                .collectList();
    }

    /**
     * 将会话标记为无效
     */
    public Mono<Void> markConversationAsInvalid(UserConversationEntity entity) {
        entity.setRowStatus(0);
        return r2dbcEntityTemplate.update(entity)
                .doOnError(e -> log.error("更新会话状态异常, conversationId={}, error={}",
                        entity.getConversationId(), e.getMessage(), e))
                .then();
    }

}
