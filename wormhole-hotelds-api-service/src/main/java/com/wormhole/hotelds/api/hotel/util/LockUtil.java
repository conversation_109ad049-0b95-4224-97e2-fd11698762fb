package com.wormhole.hotelds.api.hotel.util;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLockReactive;
import org.redisson.api.RedissonReactiveClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Date 2024/11/6 11:13
 **/
@Slf4j
public class LockUtil {

    public static String lockName(String name) {
        return "wormhole-hotelds-api:" + name;
    }


    public static <T> Mono<T> withMonoLock(String lockName, long lockTimeMs, long waitTimeMs,
                                           RedissonReactiveClient redissonReactiveClient,
                                           Supplier<Mono<T>> processor) {
        RLockReactive lock = redissonReactiveClient.getLock(lockName);
        Supplier<Mono<T>> unlock = () -> lock.unlock().then(Mono.empty());
        return Mono.using(
                () -> lock.tryLock(waitTimeMs, lockTimeMs, TimeUnit.MILLISECONDS),
                l -> l.filter(locked -> locked)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.CONFLICT)))
                        .flatMap(locked -> processor.get()
                                .onErrorResume(err -> unlock.get()
                                        .onErrorResume(unlockError -> {
                                            err.addSuppressed(unlockError);
                                            return Mono.error(err);
                                        }).then(Mono.error(err)))),
                l -> l.filter(locked -> locked)
                        .doFinally(signalType -> unlock.get())
        );
    }

    public static <T> Flux<T> withFluxLock(String lockName, long waitTimeMs, long lockTimeMs,
                                           RedissonReactiveClient redissonReactiveClient,
                                           Supplier<Flux<T>> processor) {


        RLockReactive lock = redissonReactiveClient.getLock(lockName);
        Supplier<Mono<T>> unlock = () -> lock.unlock().then(Mono.empty());
        return Flux.using(
                () -> lock.tryLock(waitTimeMs, lockTimeMs, TimeUnit.MILLISECONDS),
                l -> l.filter(locked -> locked)
                        .switchIfEmpty(Mono.error(new BusinessException(ResultCode.CONFLICT)))
                        .flatMapMany(locked -> processor.get()
                                .onErrorResume(err -> unlock.get()
                                        .onErrorResume(unlockError -> {
                                            err.addSuppressed(unlockError);
                                            return Mono.error(err);
                                        }).then(Mono.error(err)))),
                l -> l.filter(locked -> locked)
                        .doFinally(signalType -> unlock.get())
        );
    }

}
