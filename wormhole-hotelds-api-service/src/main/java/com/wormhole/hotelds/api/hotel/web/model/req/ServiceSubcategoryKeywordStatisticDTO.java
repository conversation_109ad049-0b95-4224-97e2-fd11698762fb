package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceSubcategoryKeywordStatisticDTO implements Serializable {
    private String serviceSubcategory;
    private String serviceKeyword;
    private Integer totalCount;
}
