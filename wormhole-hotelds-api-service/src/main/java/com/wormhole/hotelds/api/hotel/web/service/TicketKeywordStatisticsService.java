package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.date.DateUtil;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsTicketKeyWordStatisticsDao;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsTicketQO;
import com.wormhole.hotelds.api.hotel.web.ticket.model.HdsTicketKeyWordStatistics;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class TicketKeywordStatisticsService {
    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;
    @Autowired
    private HdsTicketKeyWordStatisticsDao hdsTicketKeyWordStatisticsDao;


    public Mono<Void> generateTicketKeywordStatistics(List<String> hotelCodes, LocalDate businessDate) {
        log.info("开始生成酒店工单关键词统计数据, hotelCode: {}, businessDate: {}", hotelCodes, businessDate);
        LocalDate localDate = Objects.isNull(businessDate) ? LocalDate.now().minusDays(1L) : businessDate;

        return hdsServiceTicketDao.findList(getTicketQO(hotelCodes, localDate)).flatMap(entities -> {
            List<HdsTicketKeyWordStatistics> statistics = toExpandedDTO(entities)
                    .stream()
                    .collect(Collectors.groupingBy(this::generateKey)).entrySet().stream().map(entry -> buildHdsTicketKeyWordStatistics(localDate, entry))
                    .toList();
            Set<String> ticketHotelCodes = entities.stream().map(HdsServiceTicketEntity::getHotelCode).collect(Collectors.toSet());
            return hdsTicketKeyWordStatisticsDao.removeData(ticketHotelCodes, localDate).then(hdsTicketKeyWordStatisticsDao.saveAll(statistics));
        }).then(Mono.empty());
    }

    private static HdsTicketQO getTicketQO(List<String> hotelCodes, LocalDate businessDate) {
        HdsTicketQO hdsTicketQO = new HdsTicketQO();
        hdsTicketQO.setHotelCodes(hotelCodes);
        LocalDateTime startTime = DateUtil.date(businessDate.atStartOfDay()).toLocalDateTime();
        LocalDateTime endTime = DateUtil.endOfDay(DateUtil.date(businessDate.atStartOfDay())).toLocalDateTime();
        hdsTicketQO.setStart(startTime);
        hdsTicketQO.setEnd(endTime);
        return hdsTicketQO;
    }

    private static HdsTicketKeyWordStatistics buildHdsTicketKeyWordStatistics(LocalDate businessDate, Map.Entry<String, List<TicketExpandedDTO>> stringListEntry) {
        String key = stringListEntry.getKey();
        String[] split = key.split("\\|");
        HdsTicketKeyWordStatistics hdsTicketKeyWordStatistics = new HdsTicketKeyWordStatistics();
        hdsTicketKeyWordStatistics.setHotelCode(split[0]);
        hdsTicketKeyWordStatistics.setPositionCode(split[1]);
        hdsTicketKeyWordStatistics.setServiceType(split[2]);
        hdsTicketKeyWordStatistics.setServiceCategory(split[3]);
        hdsTicketKeyWordStatistics.setServiceSubcategory(split[4]);
        hdsTicketKeyWordStatistics.setServiceKeyword(split[5]);
        hdsTicketKeyWordStatistics.setBusinessDate(businessDate);
        hdsTicketKeyWordStatistics.setTicketCount(stringListEntry.getValue().size());
        return hdsTicketKeyWordStatistics;
    }

    private String generateKey(TicketExpandedDTO ticketExpandedDTO) {
        return String.join("|", ticketExpandedDTO.getHotelCode(), ticketExpandedDTO.getPositionCode(),
                ticketExpandedDTO.getServiceType(), ticketExpandedDTO.getServiceCategory(),
                ticketExpandedDTO.getServiceSubcategory(), ticketExpandedDTO.getServiceKeyword());
    }

    public List<TicketExpandedDTO> toExpandedDTO(List<HdsServiceTicketEntity> lists) {
        List<TicketExpandedDTO> result = new ArrayList<TicketExpandedDTO>();
        for (HdsServiceTicketEntity entity : lists) {
            if (StringUtils.isBlank(entity.getHotelCode()) || StringUtils.isBlank(entity.getPositionCode())
                    || StringUtils.isBlank(entity.getServiceType()) || StringUtils.isBlank(entity.getServiceCategory())
                    || StringUtils.isBlank(entity.getServiceSubcategory()) || StringUtils.isBlank(entity.getServiceKeywords())) {
                log.error("工单数据不完整, entity: {}", entity);
                continue;
            }
            for (String serviceKeyWord : entity.getServiceKeywords().split(",")) {
                result.add(buildTicketExpandedDTO(entity, serviceKeyWord));
            }
        }
        return result;
    }

    @NotNull
    private static TicketExpandedDTO buildTicketExpandedDTO(HdsServiceTicketEntity entity, String serviceKeyWord) {
        TicketExpandedDTO ticketExpandedDTO = new TicketExpandedDTO();
        ticketExpandedDTO.setHotelCode(entity.getHotelCode());
        ticketExpandedDTO.setPositionCode(entity.getPositionCode());
        ticketExpandedDTO.setServiceType(entity.getServiceType());
        ticketExpandedDTO.setServiceCategory(entity.getServiceCategory());
        ticketExpandedDTO.setServiceSubcategory(entity.getServiceSubcategory());
        ticketExpandedDTO.setServiceKeyword(serviceKeyWord);
        return ticketExpandedDTO;
    }


    @Data
    public static class TicketExpandedDTO {
        private String hotelCode;
        private String positionCode;
        private String serviceType;
        private String serviceCategory;
        private String serviceSubcategory;
        private String serviceKeyword;

    }

}
