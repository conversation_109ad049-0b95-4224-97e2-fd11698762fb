package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsDeviceQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 09:08
 */
@Service
@Slf4j
public class HdsDeviceService {
    @Autowired
    private HdsDeviceDao hdsDeviceDao;

    public Mono<Boolean> hasDeviceFor(String hotelCode, String positionCode) {
        // 参数校验
        if (StringUtils.isAnyBlank(hotelCode, positionCode)) {
            log.info("hasDeviceFor 酒店代码或位置代码为空，hotelCode={}, positionCode={}", hotelCode, positionCode);
            return Mono.just(false);
        }

        // 查询设备
        return hdsDeviceDao.findOne(HdsDeviceQO.builder()
                        .hotelCode(hotelCode)
                        .positionCodes(List.of(positionCode))
                        .build())
                .map(device -> {
                    log.info("找到设备：hotelCode={}, positionCode={}, deviceId={}",
                            hotelCode, positionCode, device.getId());
                    return true;
                })
                .defaultIfEmpty(false)
                .doOnError(e -> log.error("查询设备异常：hotelCode={}, positionCode={}, error={}",
                        hotelCode, positionCode, e.getMessage(), e))
                .onErrorReturn(false);
    }

}
