package com.wormhole.hotelds.api.hotel.constant;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.core.enums.TicketStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TicketShowStatusEnum {

    EXPIRED("expired","过期未完成",0, 1),
    WAIT_TO_HANDLE("wait_to_handle","待处理",0,0),
    COMPLETED("completed","已完成",1,null),
    ;
    private final String code;
    private final String description;
    private final Integer status;
    private final Integer expiredFlag;

    public static String getShowFlag(Integer expired,Integer completed) {
        if (ObjectUtil.equal(completed, TicketStatus.COMPLETED.getCode())) return COMPLETED.getDescription();
        return ObjectUtil.equal(expired,ExpiredFlagEnum.EXPIRED.getCode()) ? EXPIRED.getDescription() : WAIT_TO_HANDLE.getDescription();
    }

    public static TicketShowStatusEnum getByCode(String code) {
        for (TicketShowStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
