package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDevicePositionDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsTicketKeyWordStatisticsDao;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.model.res.MaintenanceReportRes;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.ServiceSubcategory;
import com.wormhole.hotelds.core.model.entity.HdsDevicePositionEntity;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Service
@Slf4j
public class TicketLargeScreenService {

    @Autowired
    private HdsTicketKeyWordStatisticsDao ticketKeyWordStatisticsDao;
    @Autowired
    private HdsDevicePositionDao hdsDevicePositionDao;

    public Mono<MaintenanceReportRes> maintenanceMonitor(MaintenanceMonitorReq req) {
        MaintenanceMonitorReq.WeekRange overallWeekRange = getOverallWeekRange(req.getWeekLimit());
        req.setWeekRange(overallWeekRange);
        log.info("TicketLargeScreenService,maintenanceMonitor: {}", req);

        return buildTop3MaintenanceRoom(req).doOnNext(res -> log.info("TicketLargeScreenService,maintenanceMonitor,res: {}", JacksonUtils.writeValueAsString(res)))
                .flatMap(maintenanceReportRes -> buildTop3MaintenanceSystem(maintenanceReportRes, req));
    }


    public Mono<List<KeywordStaticsDTO>> complaintTicketKeywords(ComplaintTicketKeywordReq req) {
        log.info("TicketLargeScreenService,complaintTicketKeywords: {}", req);

        if(Objects.isNull(req.getStartDate()) && Objects.isNull(req.getEndDate())) {
            req.setStartDate(LocalDate.now().minusDays(7));
            req.setEndDate(LocalDate.now().minusDays(1));
        }


        return ticketKeyWordStatisticsDao.findKeyWordStatics(req.getHotelCode(), req.getStartDate(), req.getEndDate(), ServiceCategory.COMPLAINT.getCode(), 5).switchIfEmpty(Mono.just(Collections.emptyList()));

    }


    public Mono<MaintenanceReportRes> buildTop3MaintenanceSystem(MaintenanceReportRes res, MaintenanceMonitorReq req) {
        MaintenanceMonitorReq.WeekRange weekRange = req.getWeekRange();
        return ticketKeyWordStatisticsDao.findTopServiceCategoryTicketCount(
                req.getHotelCode(),
                weekRange.getStartDate(),
                weekRange.getEndDate(),
                ServiceCategory.MAINTENANCE.getCode()
        ).flatMap(topServiceCategories ->
                {
                    if (CollUtil.isEmpty(topServiceCategories)) {
                        return Mono.just(res);
                    }
                    return getServiceSubcategoryKeyWordStatisticsMono(req, topServiceCategories, weekRange)
                            .switchIfEmpty(Mono.just(new HashMap<>()))
                            .map(serviceSubcategoryKeyWordStatistics -> {

                                topServiceCategories.forEach(topServiceCategory -> {
                                    res.addTop3MaintenanceSystem(buildTop3MaintenanceSystem(serviceSubcategoryKeyWordStatistics, topServiceCategory));
                                });

                                return res;
                            });
                }
        ).switchIfEmpty(Mono.just(res));

    }


    @NotNull
    private static MaintenanceReportRes.Top3MaintenanceSystem buildTop3MaintenanceSystem(Map<String, List<ServiceSubcategoryKeywordStatisticDTO>> serviceSubcategoryKeyWordStatistics, TopServiceCategoryStatisticDTO topServiceCategory) {
        MaintenanceReportRes.Top3MaintenanceSystem top3MaintenanceSystem = new MaintenanceReportRes.Top3MaintenanceSystem();
        top3MaintenanceSystem.setServiceCategory(topServiceCategory.getServiceSubcategory());
        top3MaintenanceSystem.setTotalCount(topServiceCategory.getTotalCount());

        ServiceSubcategory byCode = ServiceSubcategory.getByCode(topServiceCategory.getServiceSubcategory(), false);
        Optional.ofNullable(byCode).ifPresent(serviceSubcategory ->
                top3MaintenanceSystem.setServiceCategoryName(serviceSubcategory.getChineseName())
        );

        Optional.ofNullable(serviceSubcategoryKeyWordStatistics.get(topServiceCategory.getServiceSubcategory()))
                .ifPresent(serviceSubcategoryKeyWords -> {
                    List<MaintenanceReportRes.MaintenanceDetailInfo> maintenanceDetailInfos =
                            serviceSubcategoryKeyWords.stream()
                                    .map(serviceSubcategoryKeyWord -> {
                                        MaintenanceReportRes.MaintenanceDetailInfo maintenanceDetailInfo = new MaintenanceReportRes.MaintenanceDetailInfo();
                                        maintenanceDetailInfo.setServiceKeyword(serviceSubcategoryKeyWord.getServiceKeyword());
                                        maintenanceDetailInfo.setCount(serviceSubcategoryKeyWord.getTotalCount());
                                        return maintenanceDetailInfo;
                                    }).sorted(Comparator.comparingInt(MaintenanceReportRes.MaintenanceDetailInfo::getCount).reversed()
                                            .thenComparing(MaintenanceReportRes.MaintenanceDetailInfo::getServiceKeyword))
                                    .toList();
                    top3MaintenanceSystem.setMaintenanceDetailInfos(maintenanceDetailInfos);
                });
        return top3MaintenanceSystem;
    }

    @NotNull
    private Mono<Map<String, List<ServiceSubcategoryKeywordStatisticDTO>>> getServiceSubcategoryKeyWordStatisticsMono(MaintenanceMonitorReq req, List<TopServiceCategoryStatisticDTO> topServiceCategories
            , MaintenanceMonitorReq.WeekRange overallWeekRange) {
        return ticketKeyWordStatisticsDao.findServiceSubcategoryKeyWordTicketCount(
                req.getHotelCode(),
                overallWeekRange.getStartDate(),
                overallWeekRange.getEndDate(),
                ServiceCategory.MAINTENANCE.getCode(),
                topServiceCategories.stream()
                        .map(TopServiceCategoryStatisticDTO::getServiceSubcategory)
                        .toList()
        ).map(serviceSubcategoryKeyWordStatistics ->
                serviceSubcategoryKeyWordStatistics.stream()
                        .collect(Collectors.groupingBy(ServiceSubcategoryKeywordStatisticDTO::getServiceSubcategory))
        );
    }

    @NotNull
    private Mono<MaintenanceReportRes> buildTop3MaintenanceRoom(MaintenanceMonitorReq req) {
        MaintenanceMonitorReq.WeekRange overallWeekRange = req.getWeekRange();
        return ticketKeyWordStatisticsDao.findTopRoomsTicketCount(
                req.getHotelCode(),
                overallWeekRange.getStartDate(),
                overallWeekRange.getEndDate(),
                ServiceCategory.MAINTENANCE.getCode()
        ).flatMap(topRooms -> {
            if (CollUtil.isEmpty(topRooms)) {
                return Mono.just(new MaintenanceReportRes());
            }

            MaintenanceReportRes maintenanceReportRes = new MaintenanceReportRes();

            List<String> positionCodes = topRooms.stream()
                    .map(TopRoomStatisticDTO::getPositionCode)
                    .toList();
            return Mono.zip(
                    getpositionCode2PositionMono(buildPositionReq(req, positionCodes)).switchIfEmpty(Mono.just(new HashMap<>())),
                    getPositionCode2KeyWordStatisticsMono(req, overallWeekRange, positionCodes).switchIfEmpty(Mono.just(new HashMap<>()))
            ).map(tuple -> {
                Map<String, HdsDevicePositionEntity> devicePositionCode2Position = tuple.getT1();
                Map<String, List<RoomKeywordStatisticDTO>> devicePositionCode2KeyWordStatistics = tuple.getT2();

                topRooms.forEach(ticketStatisticDTO -> {
                    maintenanceReportRes.addTop3MaintenanceRoom(buildMaintenanceRoom(ticketStatisticDTO, devicePositionCode2Position, devicePositionCode2KeyWordStatistics));

                });
                maintenanceReportRes.getTop3MaintenanceRoom()
                        .sort(Comparator.comparing(MaintenanceReportRes.Top3MaintenanceRoom::getTotalCount, Comparator.reverseOrder())
                                .thenComparing(MaintenanceReportRes.Top3MaintenanceRoom::getRoomName, Comparator.nullsLast(Comparator.naturalOrder())));
                return maintenanceReportRes;
            });
        }).switchIfEmpty(Mono.just(new MaintenanceReportRes()));
    }

    private static HdsDevicePositionQO buildPositionReq(MaintenanceMonitorReq req, List<String> positionCodes) {
        return HdsDevicePositionQO.builder()
                .positionCodes(positionCodes)
                .hotelCode(req.getHotelCode())
                .build();
    }

    @NotNull
    private static MaintenanceReportRes.Top3MaintenanceRoom buildMaintenanceRoom(TopRoomStatisticDTO ticketStatisticDTO, Map<String, HdsDevicePositionEntity> devicePositionCode2Position
            , Map<String, List<RoomKeywordStatisticDTO>> devicePositionCode2KeyWordStatistics) {
        MaintenanceReportRes.Top3MaintenanceRoom top3MaintenanceRoom = new MaintenanceReportRes.Top3MaintenanceRoom();
        String positionCode = ticketStatisticDTO.getPositionCode();
        top3MaintenanceRoom.setRoomCode(positionCode);
        top3MaintenanceRoom.setTotalCount(ticketStatisticDTO.getTotalCount());

        Optional.ofNullable(devicePositionCode2Position.get(positionCode))
                .ifPresent(position -> top3MaintenanceRoom.setRoomName(position.getPositionName()));

        Optional.ofNullable(devicePositionCode2KeyWordStatistics.get(positionCode))
                .ifPresent(keyWordStatistics -> {
                    List<MaintenanceReportRes.MaintenanceDetailInfo> maintenanceDetailInfos = keyWordStatistics.stream()
                            .map(keyWordStatistic -> {
                                MaintenanceReportRes.MaintenanceDetailInfo maintenanceDetailInfo = new MaintenanceReportRes.MaintenanceDetailInfo();
                                maintenanceDetailInfo.setServiceKeyword(keyWordStatistic.getServiceKeyword());
                                maintenanceDetailInfo.setCount(keyWordStatistic.getTotalCount());
                                return maintenanceDetailInfo;
                            }).sorted(Comparator.comparingInt(MaintenanceReportRes.MaintenanceDetailInfo::getCount).reversed())
                            .toList();
                    top3MaintenanceRoom.setMaintenanceDetailInfos(maintenanceDetailInfos);
                });
        return top3MaintenanceRoom;
    }

    @NotNull
    private Mono<Map<String, List<RoomKeywordStatisticDTO>>> getPositionCode2KeyWordStatisticsMono
            (MaintenanceMonitorReq req, MaintenanceMonitorReq.WeekRange overallWeekRange, List<String> positionCodes) {
        return ticketKeyWordStatisticsDao.findRoomKeyWordTicketCount(req.getHotelCode(), overallWeekRange.getStartDate(), overallWeekRange.getEndDate(), ServiceCategory.MAINTENANCE.getCode(), positionCodes)
                .map(results -> results.stream().collect(Collectors.groupingBy(RoomKeywordStatisticDTO::getPositionCode)));
    }

    @NotNull
    private Mono<Map<String, HdsDevicePositionEntity>> getpositionCode2PositionMono(HdsDevicePositionQO build) {
        return hdsDevicePositionDao.findList(build).map(
                positions -> positions.stream().collect(Collectors.toMap(HdsDevicePositionEntity::getPositionCode, Function.identity())));
    }


    public static MaintenanceMonitorReq.WeekRange getOverallWeekRange(int weekLimit) {
        if (weekLimit <= 0) {
            throw new IllegalArgumentException("weekLimit must be greater than 0");
        }

        LocalDate today = LocalDate.now();
        LocalDate endDate = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));

        LocalDate startDate = endDate.minusDays(6).minusWeeks(weekLimit - 1);

        return new MaintenanceMonitorReq.WeekRange(startDate, endDate);
    }

}
