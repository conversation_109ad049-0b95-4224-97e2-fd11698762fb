package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServiceSatisfactionTrendReq {
    /** 酒店编码 */
    private String hotelCode;
    /** 星期 */
    private Integer weekLimit = 4;
} 