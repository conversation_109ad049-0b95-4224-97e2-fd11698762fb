package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;

/**
 * 每日工单统计
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DailyTicketsResp {

    /**
     * AI通话数量
     */
    private String aiCallCount = "0";
    /**
     * 回拨通话数量
     */
    private String returnCallCount = "0";
    /**
     * 总工单数量
     */
    private String ticketCount = "0";
    /**
     * 已完成工单数量
     */
    private String completedTicketCount = "0";
    /**
     * 总完成率（%）
     */
    private String completedTicketPercent;
    /**
     * 问询总数
     */
    private String inquiryCount = "0";
    /**
     * 问询已完成总数
     */
    private String inquiryCompletedCount = "0";
    /**
     * 问询完成率（%）
     */
    private String inquiryCompletedPercent;
    /**
     * 客需工单（除了投诉、其他的服务工单）总数
     */
    private String serviceNeedCount = "0";
    /**
     * 客需工单已完成总数
     */
    private String serviceNeedCompletedCount = "0";
    /**
     * 客需完成率（%）
     */
    private String serviceNeedCompletedPercent;
    /**
     * 客诉工单总数
     */
    private String complaintCount = "0";
    /**
     * 客诉工单已完成总数
     */
    private String complaintCompletedCount = "0";
    /**
     * 客诉完成率（%）
     */
    private String complaintCompletedPercent;
    /**
     * 紧急事项工单总数
     */
    private String emergencyCount = "0";
    /**
     * 紧急事项工单已完成总数
     */
    private String emergencyCompletedCount = "0";
    /**
     * 紧急完成率（%）
     */
    private String emergencyCompletedPercent;
}