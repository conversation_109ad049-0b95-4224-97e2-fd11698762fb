package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.hotelds.api.hotel.web.model.res.HotelDetailVO;
import com.wormhole.hotelds.api.hotel.web.service.HotelService;
import jakarta.annotation.*;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/23 11:42
 */
@RestController
@RequestMapping("/hotel")
public class HotelController {

    @Resource
    private HotelService hotelService;

    @PostMapping("/hasAccess")
    public Mono<Boolean> hasAccess(@RequestParam("hotel_code") String hotelCode,
                                   @RequestParam("source") String source) {
        return hotelService.checkHotelAccess(hotelCode, source);
    }

    /**
     * 获取酒店详细信息
     *
     * @param hotelCode 酒店编码
     * @return 酒店详细信息
     */
    @GetMapping("/info")
    public Mono<HotelDetailVO> getHotelInfo(@RequestParam("hotel_code") String hotelCode) {
        return hotelService.getHotelInfo(hotelCode);
    }
}
