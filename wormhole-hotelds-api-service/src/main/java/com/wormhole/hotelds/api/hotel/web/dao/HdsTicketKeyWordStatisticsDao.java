package com.wormhole.hotelds.api.hotel.web.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.api.hotel.web.dao.repository.HdsTicketKeyWordStatisticsRepository;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.ticket.model.HdsTicketKeyWordStatistics;
import com.wormhole.hotelds.api.hotel.web.ticket.model.HdsTicketKeyWordStatisticsFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Repository
@Slf4j
public class HdsTicketKeyWordStatisticsDao {

    @Autowired
    private HdsTicketKeyWordStatisticsRepository repository;
    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<List<TopRoomStatisticDTO>> findTopRoomsTicketCount(String hotelCode, LocalDate startDate, LocalDate endDate, String serviceCategory) {
        return repository.findTopRoomsTicketCount(hotelCode, startDate, endDate, serviceCategory).collectList();
    }


    public Mono<List<RoomKeywordStatisticDTO>> findRoomKeyWordTicketCount(String hotelCode, LocalDate startDate, LocalDate endDate, String serviceCategory, List<String> positionCodes) {
        return repository.findRoomKeyWordTicketCount(hotelCode, startDate, endDate, serviceCategory, positionCodes).collectList();
    }

    public Mono<List<TopServiceCategoryStatisticDTO>> findTopServiceCategoryTicketCount(String hotelCode, LocalDate startDate, LocalDate endDate, String serviceCategory) {
        return repository.findTopServiceCategoryTicketCount(hotelCode, startDate, endDate, serviceCategory).collectList();
    }

    public Mono<List<ServiceSubcategoryKeywordStatisticDTO>> findServiceSubcategoryKeyWordTicketCount(String hotelCode, LocalDate startDate, LocalDate endDate, String serviceCategory, List<String> serviceSubCategories) {
        return repository.findServiceSubcategoryKeyWordTicketCount(hotelCode, startDate, endDate, serviceCategory, serviceSubCategories).collectList();
    }

    public Mono<List<KeywordStaticsDTO>> findKeyWordStatics(String hotelCode, LocalDate startDate, LocalDate endDate, String serviceCategory, int limit) {
        return repository.findKeyWordStatics(hotelCode, startDate, endDate, serviceCategory, limit).collectList();
    }


    public Mono<List<HdsTicketKeyWordStatistics>> saveAll(List<HdsTicketKeyWordStatistics> statistics) {
        return repository.saveAll(statistics).collectList();
    }

    public Mono<Boolean> removeData(Collection<String> hotelCodes, LocalDate businessDate) {
        if (CollUtil.isEmpty(hotelCodes) || Objects.isNull(businessDate)) {
            log.info("HdsTicketKeyWordStatisticsDao,setInvalid: hotelCodes is empty or businessDate is null");
            return Mono.just(false);
        }

        Criteria criteria = Criteria.where(HdsTicketKeyWordStatisticsFieldEnum.hotel_code.name()).in(hotelCodes).and(HdsTicketKeyWordStatisticsFieldEnum.business_date.name()).is(businessDate);
        return r2dbcEntityTemplate.delete(Query.query(criteria), HdsTicketKeyWordStatistics.class).doOnNext(result -> log.info("HdsTicketKeyWordStatisticsDao,remove data: " + result)).thenReturn(true).onErrorReturn(false);

    }
}
