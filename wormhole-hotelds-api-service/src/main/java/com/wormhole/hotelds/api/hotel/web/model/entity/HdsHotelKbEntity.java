package com.wormhole.hotelds.api.hotel.web.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serial;
import java.io.Serializable;

/**
 * 酒店后台维护，酒店对应的知识库
 *
 * <AUTHOR>
 */
@Data
@Table("hds_hotel_kb")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsHotelKbEntity extends BaseEntity implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;


    /**
     * 酒店code
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 知识库编码
     */
    @Column("knowledge_code")
    private String knowledgeCode;


    /**
     * 空间编码
     */
    @Column("space_code")
    private String spaceCode;


}