package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.core.model.entity.HdsEmployeeTicketMappingEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Repository
public interface EmployeeTicketMappingRepository extends ReactiveCrudRepository<HdsEmployeeTicketMappingEntity, Long> {

    /**
     * 根据账号id和账号类型查询
     *
     * @param accountId   账号id
     * @return Mono<HdsAccountTicketMapping>
     */
    Mono<HdsEmployeeTicketMappingEntity> findByEmployeeIdAndHotelCode(String accountId, String hotelCode);
}
