package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelDetailVO {
    /**
     * 酒店编码
     */
    private String hotelCode;
    
    /**
     * 酒店名称
     */
    private String hotelName;
    
    /**
     * 酒店地址
     */
    private String address;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;
    
    /**
     * 纬度
     */
    private String latitude;
    
    /**
     * 经度
     */
    private String longitude;
    
    /**
     * 房间号列表
     */
    private String roomCodes;
} 