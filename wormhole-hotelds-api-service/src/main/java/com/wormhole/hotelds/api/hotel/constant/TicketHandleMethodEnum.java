package com.wormhole.hotelds.api.hotel.constant;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.TicketClosedLoopLevel;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import com.wormhole.hotelds.core.model.entity.HdsServiceTicketFieldEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.relational.core.query.Criteria;

@AllArgsConstructor
@Getter
public enum TicketHandleMethodEnum {

    AI_HANDLE(1, "AI处理",Criteria.where(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(TicketClosedLoopLevel.L1.getCode())),
    CALLBACK(2, "人工回电",Criteria.where(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(TicketClosedLoopLevel.L2.getCode()).and(HdsServiceTicketFieldEnum.service_category.name()).not(ServiceCategory.COMPLAINT.getCode()) ),
    COMPLAINT_CALLBACK(3, "客诉回电",Criteria.where(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(TicketClosedLoopLevel.L3.getCode()).or(HdsServiceTicketFieldEnum.service_category.name()).is(ServiceCategory.COMPLAINT.getCode())),
    SOS(4, "SOS直连", Criteria.where(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(TicketClosedLoopLevel.L4.getCode())),
    HUMAN_HANDLE(5, "人工执行",Criteria.where(HdsServiceTicketFieldEnum.closed_loop_level.name()).is(TicketClosedLoopLevel.L5.getCode())),
    ;
    private final int code;
    private final String description;

    private final Criteria criteria;

    public static Criteria getByCode(Integer code) {
        if (code == null) return null;
        for (TicketHandleMethodEnum value : values()) {
            if (value.code == code) {
                return value.getCriteria();
            }
        }
        return null;
    }

    public static String getDescByTicket(HdsServiceTicketEntity ticket){
        if (ticket == null) return null;
        if(ObjectUtil.equal(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L1.getCode())){
            return AI_HANDLE.getDescription();
        }
        if(ObjectUtil.equal(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L2.getCode()) && ObjectUtil.notEqual(ticket.getServiceCategory(), ServiceCategory.COMPLAINT.getCode())){
            return CALLBACK.getDescription();
        }
        if(ObjectUtil.equal(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L3.getCode()) || ObjectUtil.equal(ticket.getServiceCategory(), ServiceCategory.COMPLAINT.getCode())){
            return COMPLAINT_CALLBACK.getDescription();
        }
        if(ObjectUtil.equal(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L5.getCode())){
            return HUMAN_HANDLE.getDescription();
        }
        if (ObjectUtil.equal(ticket.getClosedLoopLevel(), TicketClosedLoopLevel.L4.getCode())){
            return SOS.getDescription();
        }
        return null;
    }
}
