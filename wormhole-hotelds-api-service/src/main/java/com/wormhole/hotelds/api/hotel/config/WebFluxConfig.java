package com.wormhole.hotelds.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.web.reactive.config.WebFluxConfigurer;

/**
 * <AUTHOR>
 * @date 2025/5/19 19:27
 */
@Configuration
public class WebFluxConfig implements WebFluxConfigurer {
    @Override
    public void configureHttpMessageCodecs(ServerCodecConfigurer configurer) {
        // 根据请求类型配置特定解码器
        Jackson2JsonDecoder decoder = new Jackson2JsonDecoder();
        decoder.setMaxInMemorySize(10 * 1024 * 1024);
        configurer.defaultCodecs().jackson2JsonDecoder(decoder);
    }
}
