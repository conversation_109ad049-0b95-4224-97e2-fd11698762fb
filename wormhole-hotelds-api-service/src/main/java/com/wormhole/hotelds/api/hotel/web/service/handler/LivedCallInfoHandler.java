package com.wormhole.hotelds.api.hotel.web.service.handler;

import cn.hutool.core.util.ObjectUtil;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.YesNoEnum;
import com.wormhole.hotelds.api.hotel.util.HotelApiRedisUtils;
import com.wormhole.hotelds.api.hotel.util.TicketUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsLivedCallInfoDao;
import com.wormhole.hotelds.api.hotel.web.service.DailyTicketStatService;
import com.wormhole.hotelds.core.model.entity.HdsLivedRtcCallInfo;
import com.wormhole.hotelds.core.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

@Component
@Slf4j
public class LivedCallInfoHandler implements RtcCallbackHandler {

    @Autowired
    private HdsLivedCallInfoDao hdsLivedCallInfoDao;

    @Autowired
    private HotelApiRedisUtils hotelApiRedisUtils;

    @Autowired
    private DailyTicketStatService dailyTicketStatService;

    @Override
    public boolean canHandle(CallInfoMessage request) {
        return true;
    }

    @Override
    public Mono<Void> handle(CallInfoMessage request) {
        log.info("LivedCallInfoHandler.handle,request:{}", JacksonUtils.writeValueAsString(request));
        // 加锁
        String redisKey = String.format("hotelApi:lock:livedCallInfo:%s", request.getRtcRoomId());
        return hotelApiRedisUtils.lock(redisKey, String.valueOf(Thread.currentThread().getId()),3)
                .flatMap(lock -> {
                    if (!lock){
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "获取锁失败"));
                    }
                    return livedCallInfoSave(request);
                })
                .flatMap(res-> hotelApiRedisUtils.deleteRedisKey(redisKey))
                .onErrorResume(e -> {
                    log.error("LivedCallInfoHandler.handle,error:{}", JacksonUtils.writeValueAsString(request),e);
                    return hotelApiRedisUtils.deleteRedisKey(redisKey);
                });
    }

    private Mono<Boolean> livedCallInfoSave(CallInfoMessage request) {
        HdsLivedRtcCallInfo message = buildLivedCallInfo(request);
        return hdsLivedCallInfoDao.selectByRtcRoomId(request.getRtcRoomId())
                .switchIfEmpty(Mono.just(new HdsLivedRtcCallInfo()))
                .flatMap(entity -> {
                    if (Objects.isNull(entity) || Objects.isNull(entity.getId())) {
                        return hdsLivedCallInfoDao.insert(message);
                    }
                    message.setId(entity.getId());
                    // old状态只有1,2才能修改
                    if((ObjectUtil.equal(entity.getCallStatus(), RtcCallStatusEnum.CALLING.getCode()) || ObjectUtil.equal(entity.getCallStatus(), RtcCallStatusEnum.ANSWERED.getCode()))
                            && entity.getCallStatus().compareTo(message.getCallStatus()) < 0 ){
                        return hdsLivedCallInfoDao.updateEntity(message);
                    }
                    log.info("LivedCallInfoHandler update ignore ,oldCallStatus:{},newCallStatus:{}", entity.getCallStatus(), message.getCallStatus());
                    return Mono.just(true);
                })
                .then(dailyTicketStatService.updateCallStat(message))
                .thenReturn(true);

    }

    private HdsLivedRtcCallInfo buildLivedCallInfo(CallInfoMessage request) {
        HdsLivedRtcCallInfo entity = new HdsLivedRtcCallInfo();
        BeanUtils.copyProperties(request,entity);
        entity.setHotelCode(DeviceInitRtcUtil.getHotelCodeFromRtcRoomId(request.getRtcRoomId()));
        entity.setHasAiParticipant(request.isHasAiParticipant() ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        entity.setRowStatus(RowStatusEnum.VALID.getId());
        return entity;
    }
}