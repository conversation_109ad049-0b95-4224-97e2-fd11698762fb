package com.wormhole.hotelds.api.hotel.util;

import com.google.common.base.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

import java.io.InputStream;

/**
 * 本地文件读取工具类
 *
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
public class ResourceFileUtils {

    public static String readFile(String fullFilePath) {
        try (InputStream resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(fullFilePath)) {
            return IOUtils.toString(resourceAsStream, Charsets.UTF_8.toString());
        } catch (Exception e) {
            throw new ContextedRuntimeException(e).addContextValue("filePath", fullFilePath);
        }
    }

    public static String readLocalFile(String localFilePath) {
        return readFile(String.format("local/%s", localFilePath));
    }

    public static void main(String[] args) {
        String s = readFile("data/default_app_bar_form_schema.json");
        System.out.println(s);
    }

}