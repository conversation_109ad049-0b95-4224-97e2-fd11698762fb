package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.services.bedrockruntime.endpoints.internal.Value;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MaintenanceMonitorReq implements Serializable {
    private String hotelCode;
    private Integer weekLimit = 4;


    /**
     * 以下字段服务端自用
     */

    private WeekRange weekRange;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WeekRange implements Serializable {
        private LocalDate startDate;  // 周一
        private LocalDate endDate;    // 周日
    }
}