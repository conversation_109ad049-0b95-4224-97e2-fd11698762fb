package com.wormhole.hotelds.api.hotel.job;

import com.wormhole.agent.core.model.entity.UserConversationEntity;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.web.service.TicketKeywordStatisticsService;
import com.xxl.job.core.context.XxlJobHelper;
import com.wormhole.hotelds.api.hotel.web.service.UserConversationService;
import com.wormhole.hotelds.api.hotel.web.service.DeviceBindService;
import com.wormhole.hotelds.api.hotel.web.service.HdsDeviceService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2025/4/24 19:36
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CleanWechatConservationJob {

    @Autowired
    private UserConversationService userConversationService;

    @Autowired
    private HdsDeviceService hdsDeviceService;

    @Autowired
    private DeviceBindService deviceBindService;

    @Value("${task.conversation.page-size:100}")
    private int pageSize;

    @Value("${task.conversation.concurrency:5}")
    private int concurrency;

    @Autowired
    private TicketKeywordStatisticsService ticketKeyWordStatisticsService;

    @XxlJob("cleanYesterdayWechatConservation")
    public void cleanYesterdayWechatConservation() {

        log.info("开始执行清理昨天无设备的酒店会话任务");
        AtomicLong lastId = new AtomicLong(0);
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger invalidatedCount = new AtomicInteger(0);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 使用Flux.defer延迟执行数据库查询，直到有订阅者
        Flux.defer(() -> fetchAndProcessBatch(lastId, totalCount, invalidatedCount))
                .repeat(() -> lastId.get() > 0)  // 只要lastId大于0就继续重复操作
                .onErrorResume(e -> {
                    log.error("任务执行过程中发生异常", e);
                    return Mono.empty();
                })
                .blockLast();  // 等待所有处理完成

        stopWatch.stop();
        log.info("清理任务完成，总记录数：{}，失效数：{}，耗时：{} ms",
                totalCount.get(), invalidatedCount.get(), stopWatch.getTotalTimeMillis());
    }


    private Flux<Void> fetchAndProcessBatch(AtomicLong lastId, AtomicInteger totalCount, AtomicInteger invalidatedCount) {
        return userConversationService.findYesterdayHotelConversations(lastId.get(), pageSize)
                .flatMapMany(conversations -> {
                    if (conversations.isEmpty()) {
                        lastId.set(0);  // 设置为0表示没有更多数据
                        return Flux.empty();
                    }

                    // 更新lastId为当前批次最后一条记录的ID
                    lastId.set(conversations.get(conversations.size() - 1).getId());
                    log.info("获取到一批次会话数据，数量: {}, 最后ID: {}", conversations.size(), lastId.get());

                    return Flux.fromIterable(conversations);
                })
                .flatMap(conversation -> processConversation(conversation, totalCount, invalidatedCount), concurrency);
    }

    private Mono<Void> processConversation(UserConversationEntity conversation,
                                           AtomicInteger totalCount,
                                           AtomicInteger invalidatedCount) {
        totalCount.incrementAndGet();
        String hotelCode = conversation.getHotelCode();
        String positionCode = conversation.getPositionCode();
        String conversationId = conversation.getConversationId();

        return userConversationService.markConversationAsInvalid(conversation)
                .then(deviceBindService.cleanDeviceTickets(conversationId, hotelCode, positionCode))
                .doOnSuccess(result -> {
                    invalidatedCount.incrementAndGet();
                    log.info("会话已置为失效 -> conversationId={}, hotelCode={}, positionCode={}",
                            conversationId, hotelCode, positionCode);
                })
                .onErrorResume(e -> {
                    log.error("处理会话数据异常 conversationId={}, hotelCode={}, positionCode={}, error=",
                            conversationId, hotelCode, positionCode,  e);
                    return Mono.empty();
                }).then();
    }

}
