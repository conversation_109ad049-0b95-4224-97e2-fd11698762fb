package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.agent.knowledge.model.entity.KnowledgeBaseEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/6/17
 * @Description:
 */
@Repository
public interface KnowledgeBaseRepository extends ReactiveCrudRepository<KnowledgeBaseEntity, Long> {

    Mono<KnowledgeBaseEntity> findByKnowledgeCode(String knowledgeCode);

}
