package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.core.model.entity.*;
import org.springframework.data.repository.reactive.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.Flux;

@Repository
public interface EmployeeHotelRepository extends ReactiveCrudRepository<HdsEmployeeHotelEntity, Integer> {

    Flux<HdsEmployeeHotelEntity> findByEmployeeIdAndStatus(Integer employeeId, Integer status);

    Flux<HdsEmployeeHotelEntity> findByHotelCodeAndStatus(String hotelCode, Integer status);
}
