package com.wormhole.hotelds.api.hotel.web.controller.toB.dashboard;

import com.wormhole.common.result.*;
import com.wormhole.hotelds.api.hotel.job.CleanWechatConservationJob;
import com.wormhole.hotelds.api.hotel.job.HotelAiDailyStatsJob;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.model.res.*;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceSatisfactionTrendVO;
import com.wormhole.hotelds.api.hotel.web.model.res.TicketsTrendVO;
import com.wormhole.hotelds.api.hotel.web.service.HotelDailyAiStatisticsService;
import com.wormhole.hotelds.api.hotel.web.service.TicketAdminService;
import com.wormhole.hotelds.api.hotel.web.service.TicketKeywordStatisticsService;
import com.wormhole.hotelds.api.hotel.web.service.TicketLargeScreenService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import com.wormhole.hotelds.api.hotel.web.model.req.TicketsTrendReq;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/hotel_stat")
public class FocusTicketController {

    @Autowired
    private TicketAdminService ticketAdminService;

    @Autowired
    private TicketLargeScreenService ticketLargeScreenService;
    @Autowired
    private TicketKeywordStatisticsService ticketKeywordStatisticsService;
    @Autowired
    private HotelDailyAiStatisticsService hotelDailyAiStatisticsService;

    @PostMapping("/focus_tickets_topn")
    public Mono<Result<List<FocusTicketResp>>> getTopNFocusTickets(@RequestBody GetTopNTicketReq req){
        return ticketAdminService.getTopNFocusTickets(req).flatMap(Result::success);
    }

    @PostMapping("/daily_tickets")
    public Mono<Result<DailyTicketsResp>> getDailyTickets(@RequestBody GetDailyTicketsReq req){
        return ticketAdminService.getDailyTickets(req).flatMap(Result::success);
    }

    @PostMapping("/maintenance_monitor")
    public Mono<Result<MaintenanceReportRes>> maintenanceMonitor(@RequestBody MaintenanceMonitorReq req) {
        return ticketLargeScreenService.maintenanceMonitor(req).flatMap(Result::success);
    }

    @PostMapping("/complaint_ticket_keywords")
    public Mono<Result<List<KeywordStaticsDTO>>> complaintTicketKeywords(@RequestBody ComplaintTicketKeywordReq req) {
        return ticketLargeScreenService.complaintTicketKeywords(req).flatMap(Result::success);
    }

    //public Mono<Void> generateTicketKeyWordStatistics(List<String> hotelCodes, LocalDate businessDate)

    @PostMapping("/generate_ticket_key_word_statistics")
    public Mono<Result<Object>> generateTicketKeyWordStatistics(@RequestBody HotelAiDailyStatsJob.GenerateTicketKeyWordStatisticsParam req) {
        return ticketKeywordStatisticsService.generateTicketKeywordStatistics(req.getHotelCodes(), req.getBusinessDate()).then(Result.success(null));
    }

    @PostMapping("/service_satisfaction_trend")
    public Mono<Result<List<ServiceSatisfactionTrendVO>>> getServiceSatisfactionTrend(@RequestBody ServiceSatisfactionTrendReq req) {
        return hotelDailyAiStatisticsService.getServiceSatisfactionTrend(req).collectList().flatMap(Result::success);
    }

    @PostMapping("/tickets_trend")
    public Mono<Result<List<TicketsTrendVO>>> getTicketsTrend(@RequestBody TicketsTrendReq req) {
        return hotelDailyAiStatisticsService.getTicketsTrend(req).collectList().flatMap(Result::success);
    }


}
