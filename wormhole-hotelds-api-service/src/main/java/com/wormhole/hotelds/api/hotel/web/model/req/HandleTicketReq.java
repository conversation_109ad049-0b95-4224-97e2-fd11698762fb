package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HandleTicketReq implements Serializable {
    private String ticketId;
    private String userName;
    private String userId;
    private String rtcRoomId;
    private boolean verifyPermission = true;

    /**
     *  ======== 以下字段为服务端自用参数 ========
     */

    //是否需要推送工单反馈
    private boolean isPush = true;
}
