package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import com.wormhole.hotelds.api.hotel.web.model.req.HotelPositionSimpleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025/6/12
 */

@Repository
@Slf4j
public class OrderRoomRedisDao {


    @Autowired
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


    public Mono<Boolean> save(String bdwHotelCode, String childOrderNo, HotelPositionSimpleDto hotelPositionSimpleDto) {
        String key = String.format(RedisConstant.ORDER_ROOM_CACHE_KEY, bdwHotelCode, childOrderNo);
        Duration days = Duration.ofDays(7);
        log.info("OrderRoomRedisDao key {}  to redis {}", key, hotelPositionSimpleDto);
        return reactiveRedisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(hotelPositionSimpleDto),days);
    }


    public Mono<HotelPositionSimpleDto> get(String bdwHotelCode, String childOrderNo) {
        String key = String.format(RedisConstant.ORDER_ROOM_CACHE_KEY, bdwHotelCode, childOrderNo);

        return reactiveRedisTemplate.opsForValue().get(key).flatMap(value -> {
            log.info("{} getOrderRoom from redis success {}", bdwHotelCode, value);
            if (StringUtils.isNotBlank(value)) {
                return Mono.just(JacksonUtils.readValue(value, HotelPositionSimpleDto.class));
            }
            return Mono.empty();
        });

    }


}
