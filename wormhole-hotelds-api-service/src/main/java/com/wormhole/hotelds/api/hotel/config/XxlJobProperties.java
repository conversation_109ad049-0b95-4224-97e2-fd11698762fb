package com.wormhole.hotelds.api.hotel.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * XxlJobProperties
 *
 * <AUTHOR>
 * @version 2025/1/3
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wormhole.xxl.job")
public class XxlJobProperties {

    private String addresses;

    private Integer port;

    private Integer logRetentionDays;

}