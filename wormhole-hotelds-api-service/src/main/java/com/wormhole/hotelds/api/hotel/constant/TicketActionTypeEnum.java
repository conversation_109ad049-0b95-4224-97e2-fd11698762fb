package com.wormhole.hotelds.api.hotel.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TicketActionTypeEnum {
    BEGIN_CONSERVATION("begin_conservation", "开始会话"),
    CREATE("create", "创建工单"),
    COMPLETE("complete", "处理工单"),
    EXPIRE("expire", "工单重置后过期"),
    ;
    private final String code;
    private final String description;

    public static String getDescByCode(String actionType) {
        for (TicketActionTypeEnum ele : TicketActionTypeEnum.values()) {
            if (ele.getCode().equals(actionType)) {
                return ele.getDescription();
            }
        }
        return null;
    }
}
