package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@Builder
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KbResultResp implements Serializable {

    private String output;

    private List<Content> outputs;

    private Integer size;

    @Data
    @Builder
    @Accessors(chain = true)
    public static class Content {

        private String content;

        private Double score;

    }

}
