package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.req.DeviceInitReq;
import com.wormhole.hotelds.api.hotel.req.TicketFeedbackMessage;
import com.wormhole.hotelds.api.hotel.web.model.req.BindDeviceReq;
import com.wormhole.hotelds.api.hotel.req.BindDeviceUserInfoReq;
import com.wormhole.hotelds.api.hotel.web.model.req.ChatInitReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetHotelPositionReq;
import com.wormhole.hotelds.api.hotel.web.model.req.GetRoomsReq;
import com.wormhole.hotelds.api.hotel.web.service.DeviceBindService;
import com.wormhole.hotelds.api.hotel.web.service.DeviceInRoomService;
import com.wormhole.hotelds.api.hotel.req.SearchDeviceReq;
import com.wormhole.hotelds.api.hotel.web.model.res.*;
import com.wormhole.hotelds.core.model.req.DeviceHeartReq;
import com.wormhole.hotelds.core.model.resp.DeviceInitResp;
import com.wormhole.hotelds.core.model.resp.DeviceRtcInfoResp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/device")
public class RoomDeviceController {

    @Autowired
    private DeviceBindService deviceBindService;

    @Autowired
    private DeviceInRoomService roomDeviceService;

    @PostMapping("/test/pushFeedback")
    public Mono<Result<Boolean>> pushFeedbackUT(@RequestBody CallInfoMessage req) {
        return roomDeviceService.pushTicketFeedbackNote(req).flatMap(Result::success);
    }

    @PostMapping("/get_hotel_info")
    public Mono<Result<HotelRoomListVo>> getHotelInfo(@RequestBody GetRoomsReq req) {
        return deviceBindService.getHotelInfo(req).flatMap(Result::success);
    }

    @PostMapping("/position/details")
    public Mono<Result<DevicePositionVo>> getPositionDetails(@RequestBody GetHotelPositionReq req) {
        return deviceBindService.getPositionDetails(req).flatMap(Result::success);
    }

    @PostMapping("/get_rooms_by_hotel")
    public Mono<Result<HotelRoomListVo>> getRoomsByHotel(@RequestBody GetRoomsReq req, @RequestHeader(value = "DeviceType") String deviceType) {
        return deviceBindService.getRoomsByHotel(req, deviceType).flatMap(Result::success);
    }

    @PostMapping("/pending/search_rooms")
    public Mono<Result<List<PositionSearchVo>>> searchRoomsByHotel(@RequestBody GetRoomsReq req, @RequestHeader(value = "DeviceType") String deviceType) {
        return deviceBindService.searchRoomsByHotel(req, deviceType).flatMap(Result::success);
    }

    @PostMapping("/bind")
    public Mono<Result<DeviceBindResp>> bindDevice(@RequestBody BindDeviceReq req, @RequestHeader(value = "DeviceType") String deviceType) {
        req.setDeviceType(deviceType);
        return deviceBindService.bindDevice(req).flatMap(Result::success);
    }


    @PostMapping("/init_device_info")
    public Mono<DeviceInitResp> getInitDeviceInfo(@RequestParam(value = "device_id") String deviceId) {
        return deviceBindService.initDeviceInfo(DeviceInitReq.builder().deviceId(deviceId).build());
    }

    @PostMapping("/init_app_device_info")
    public Mono<DeviceInitResp> initAppDeviceInfo(@RequestBody DeviceInitReq req) {
        return deviceBindService.initDeviceInfo(req);
    }

    @PostMapping("/get_rtc_device_info")
    public Mono<DeviceRtcInfoResp> getRtcDeviceInfo(@RequestParam(value = "device_id") String deviceId,
                                                    @RequestParam(value = "client_type", required = false) String clientType) {
        return deviceBindService.getRtcDeviceInfo(deviceId, clientType);
    }

    @PostMapping("/get_available_devices")
    public Mono<List<DeviceRtcInfoResp>> getAvailableDevices(@RequestBody SearchDeviceReq deviceQo) {
        return deviceBindService.getAvailableDevices(deviceQo);
    }

    @PostMapping("/clean_device_tickets")
    public Mono<Boolean> cleanDeviceTickets(@RequestParam(value = "hotel_code") String hotelCode,
                                            @RequestParam(value = "position_code") String positionCode) {
        return deviceBindService.cleanDeviceTickets(null, hotelCode, positionCode);
    }

    @PostMapping("/bind_device_user_info")
    public Mono<Boolean> bindDeviceUserInfo(@RequestBody BindDeviceUserInfoReq req) {
        return deviceBindService.bindDeviceUserInfo(req);
    }


    @PostMapping("/get_room_simple_vos")
    public Mono<Result<List<RoomSimpleVo>>> getRoomSimpleVos(@RequestBody GetRoomsReq getRoomsReq) {
        return deviceBindService.getRoomSimpleVos(getRoomsReq)
                .flatMap(Result::success);
    }

    @PostMapping("/get_room_code")
    public Mono<Result<String>> getRoomCode(@RequestBody GetRoomsReq getRoomsReq){
        return deviceBindService.getRoomCode(getRoomsReq)
                .flatMap(Result::success);
    }

    @PostMapping("/heart_alive")
    public Mono<Boolean> deviceHeartAlive(@RequestBody DeviceHeartReq deviceHeartReq) {
        return deviceBindService.deviceHeartAlive(deviceHeartReq);
    }

    @PostMapping("/ticket_service_feedback")
    public Mono<Boolean> feedbackInTicketService(@RequestBody TicketFeedbackMessage req) {
        return roomDeviceService.feedbackInTicketService(req);
    }

    @PostMapping("/log_out")
    public Mono<Boolean> deviceLogOut(@RequestParam(value = "device_id") String deviceId) {
        return deviceBindService.deviceLogOut(deviceId);
    }

    @PostMapping("/send/device/rtc")
    public Mono<Boolean> sendDeviceRtcMessage(@RequestParam(value = "device_id") String deviceId, @RequestParam(value = "is_enable") String isEnable) {
        return deviceBindService.sendDeviceRtcMessage(deviceId,isEnable);
    }

    @PostMapping("/chat/init")
    public Mono<Result<DeviceInitResp>> chatInit(@RequestBody ChatInitReq chatInitReq) {
        return deviceBindService.chatInit(chatInitReq).flatMap(Result::success);
    }

}
