package com.wormhole.hotelds.api.hotel.web.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Data
@Table("user_login_device_record")
public class UserLoginDeviceRecord extends BaseEntity implements Serializable {

    @Id
    private Long id;
    @Column("user_id")
    private String userId;
    @Column("hotel_code")
    private String hotelCode;
    @Column("position_code")
    private String positionCode;
    @Column("device_type")
    private String deviceType;
}
