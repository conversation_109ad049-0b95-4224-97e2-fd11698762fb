package com.wormhole.hotelds.api.hotel.constant;

import com.google.common.collect.*;

import java.util.*;

public enum EnviromentEnum {
    /**
     * 开发&日常
     */
    dev,
    /**
     * 开发&日常
     */
    local,
    /**
     * 测试
     */
    test,
    /**
     * 线上
     */
    prod;

    private static final Set<String> NAME_SET = Sets.newHashSet();

    static {
        Arrays.stream(EnviromentEnum.values()).forEach(item -> NAME_SET.add(item.name()));
    }

    public static boolean isValid(String name) {
        return NAME_SET.contains(name);
    }

}
