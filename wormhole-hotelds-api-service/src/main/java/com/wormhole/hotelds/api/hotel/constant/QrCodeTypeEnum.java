package com.wormhole.hotelds.api.hotel.constant;

import lombok.*;

@Getter
@AllArgsConstructor
public enum QrCodeTypeEnum {

    WECHAT("wechat", "微信二维码"),
    NORMAL("normal", "普通二维码");

    private final String code;
    private final String desc;

    public static QrCodeTypeEnum getByCode(String code) {
        for (QrCodeTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}