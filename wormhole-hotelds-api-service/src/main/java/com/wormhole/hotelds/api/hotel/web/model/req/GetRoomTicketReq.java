package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetRoomTicketReq implements Serializable {
    private String positionCode;
    private Integer currentPage;
    private Integer pageSize;
    private String hotelCode;
    private String positionName;

    // 限制位置
    private List<String> limitPositionCodes;
}
