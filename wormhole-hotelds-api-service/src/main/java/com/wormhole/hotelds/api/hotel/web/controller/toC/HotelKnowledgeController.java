package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.req.KbSearchReq;
import com.wormhole.hotelds.api.hotel.web.model.res.KbResultResp;
import com.wormhole.hotelds.api.hotel.web.service.HotelKnowledgeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@RestController
@RequestMapping("/hotel/kb")
@RequiredArgsConstructor
public class HotelKnowledgeController {

    private final HotelKnowledgeService hotelKnowledgeService;

    @PostMapping("/search")
    public Mono<Result<KbResultResp>> search(@RequestBody KbSearchReq kbSearchReq) {
        return hotelKnowledgeService.search(kbSearchReq)
                .flatMap(Result::success)
                .switchIfEmpty(Result.failed("KB-SEARCH-ERROR", "knowledge base search error"));
    }

}
