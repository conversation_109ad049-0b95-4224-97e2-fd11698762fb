package com.wormhole.hotelds.api.hotel.web.dao;

import com.wormhole.hotelds.core.model.entity.HdsHotelDailyAiStatisticsEntity;
import com.wormhole.hotelds.core.model.entity.HdsHotelDailyAiStatisticsFieldEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * 酒店每日AI统计数据访问层
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Repository
@Slf4j
public class HdsHotelDailyAiStatisticsDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * 保存统计数据
     */
    public Mono<HdsHotelDailyAiStatisticsEntity> save(HdsHotelDailyAiStatisticsEntity entity) {
        return r2dbcEntityTemplate.insert(entity);
    }

    /**
     * 根据酒店编码和日期查询统计数据
     */
    public Mono<HdsHotelDailyAiStatisticsEntity> findByHotelCodeAndDate(String hotelCode, LocalDate businessDate) {
        Criteria criteria = Criteria.where(HdsHotelDailyAiStatisticsFieldEnum.hotel_code.name()).is(hotelCode)
                .and(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).is(businessDate)
                .and(HdsHotelDailyAiStatisticsFieldEnum.row_status.name()).is(1);
        
        return r2dbcEntityTemplate.selectOne(Query.query(criteria), HdsHotelDailyAiStatisticsEntity.class);
    }

    /**
     * 更新统计数据
     */
    public Mono<HdsHotelDailyAiStatisticsEntity> update(HdsHotelDailyAiStatisticsEntity entity) {
        return r2dbcEntityTemplate.update(entity);
    }

    /**
     * 删除指定日期的统计数据
     */
    public Mono<Long> deleteByDate(LocalDate businessDate) {
        Criteria criteria = Criteria.where(HdsHotelDailyAiStatisticsFieldEnum.business_date.name()).is(businessDate)
                .and(HdsHotelDailyAiStatisticsFieldEnum.row_status.name()).is(1);
        
        Update update = Update.update(HdsHotelDailyAiStatisticsFieldEnum.row_status.name(), 0);
        return r2dbcEntityTemplate.update(Query.query(criteria), update, HdsHotelDailyAiStatisticsEntity.class);
    }
} 