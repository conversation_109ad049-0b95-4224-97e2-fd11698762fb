package com.wormhole.hotelds.api.hotel.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.*;

/**
 * <AUTHOR>
 * &#064;date  2025/4/9 13:50
 */
@Data
@ConfigurationProperties(TicketProperties.CONFIG_PREFIX)
@RefreshScope
public class TicketProperties {

    public static final String CONFIG_PREFIX = "wormhole.ticket";

    private int listRefreshSecond = 5;

    private int callFeedbackHour = 3;

    /**
     * DELIVERY,FOOD,LAUNDRY,CLEANING,TRANSPORT,MAINTENANCE
     */
    private List<String> receiveTicketList = new ArrayList<>();

    /**
     * NACOS 会乱码
     * 物品配送,餐饮服务,洗衣服务,清洁服务,出行服务,维修服务
     */
    private List<String> ticketDescList = Lists.newArrayList("物品配送", "餐饮服务", "洗衣服务", "清洁服务", "出行服务", "维修服务");


}
