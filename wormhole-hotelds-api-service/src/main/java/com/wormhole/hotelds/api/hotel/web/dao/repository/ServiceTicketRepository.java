package com.wormhole.hotelds.api.hotel.web.dao.repository;

import com.wormhole.hotelds.core.model.entity.HdsServiceTicketEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface ServiceTicketRepository extends ReactiveCrudRepository<HdsServiceTicketEntity, Long>{

    //通过rtcRoomid查询
    Flux<HdsServiceTicketEntity> findByRtcRoomId(String rtcRoomId);

    Mono<HdsServiceTicketEntity> findByTicketNo(String ticketNo);
}
