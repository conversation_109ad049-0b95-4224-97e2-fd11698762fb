package com.wormhole.hotelds.api.hotel.util;

import com.wormhole.common.enums.*;
import com.wormhole.hotelds.api.hotel.constant.*;

import java.util.*;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/15 13:46
 */
public class TokenKeyUtils {

    public static final String HDS_ADMIN = "hds-admin";

    public static String getTokenKey(String source, String token) {
        // 统一hds后台token，使用新变量存储修改后的source
        String finalSource = source;
        if (Arrays.asList(SourcePlatform.HDS_MC.getCode(), SourcePlatform.HDS_OP.getCode()).contains(source)) {
            finalSource = HDS_ADMIN;
        }
        return String.format(RedisConstant.USER_LOGIN_TOKEN_KEY, finalSource, token);
    }

    public static String getRefreshTokenKey(String source, String refreshToken) {
        // 统一hds后台token，使用新变量存储修改后的source
        String finalSource = source;
        if (Arrays.asList(SourcePlatform.HDS_MC.getCode(), SourcePlatform.HDS_OP.getCode()).contains(source)) {
            finalSource = HDS_ADMIN;
        }
        return String.format(RedisConstant.USER_LOGIN_REFRESH_TOKEN_KEY, finalSource, refreshToken);
    }

    /**
     * 获取用户token的key
     *
     * @param source 来源
     * @param userId 用户ID
     * @return 用户token的key
     */
    public static String getUserTokenKey(String source, String userId) {
        return String.format(RedisConstant.HDS_USER_TOKEN_KEY, source, userId);
    }
}
