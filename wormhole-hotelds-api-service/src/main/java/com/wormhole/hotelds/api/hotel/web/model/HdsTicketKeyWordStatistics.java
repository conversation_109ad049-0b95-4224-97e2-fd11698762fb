package com.wormhole.hotelds.api.hotel.web.ticket.model;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Data
@Table("hds_ticket_keyword_statistics")
public class HdsTicketKeyWordStatistics  extends BaseEntity implements Serializable {
    private Long id;
    private LocalDate businessDate;
    private String hotelCode;
    private String positionCode;
    private String serviceType;
    private String serviceCategory;
    private String serviceSubcategory;
    private String serviceKeyword;
    private Integer ticketCount;
}
