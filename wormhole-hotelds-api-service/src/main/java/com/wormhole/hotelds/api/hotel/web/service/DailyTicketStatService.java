package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.wormhole.channel.consts.enums.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.api.hotel.constant.*;
import com.wormhole.hotelds.core.enums.ServiceCategory;
import com.wormhole.hotelds.core.enums.ServiceType;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.core.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.*;
import java.time.format.*;
import java.util.*;

/**
 * 每日工单统计指标更新服务
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Component
@Slf4j
public class DailyTicketStatService {

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    /**
     * 更新通话创建统计指标
     */
    public Mono<Void> updateCallStat(HdsLivedRtcCallInfo message) {
        if (!Objects.equals(message.getCallStatus(), RtcCallStatusEnum.FINISHED.getCode())) {
            return Mono.empty();
        }
        log.info("更新通话统计指标 | message:{}", JacksonUtils.writeValueAsString(message));
        String hotelCode = DeviceInitRtcUtil.getHotelCodeFromRtcRoomId(message.getRtcRoomId());
        String bizDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        String cacheKey = String.format(RedisConstant.HOTEL_DAILY_TICKET_STAT_KEY, hotelCode, bizDate);

        boolean isAiCall = Objects.equals(CallOperatorTypeEnum.GUEST_ROOM.getCode(), message.getInitiatorType())
                && Objects.equals(CallOperatorTypeEnum.AI_AGENT.getCode(), message.getReceiverType());

        boolean isReturnCall = Objects.equals(CallOperatorTypeEnum.FRONT_DESK.getCode(), message.getInitiatorType())
                && Objects.equals(CallOperatorTypeEnum.GUEST_ROOM.getCode(), message.getReceiverType());

        return Mono.when(
                        isAiCall ? incrementField(cacheKey, RedisConstant.DailyTicketField.AI_CALL_COUNT) : Mono.empty(),
                        isReturnCall ? incrementField(cacheKey, RedisConstant.DailyTicketField.RETURN_CALL_COUNT) : Mono.empty()
                ).then(setExpire(cacheKey))
                .doOnSuccess(v -> log.info("更新通话统计指标成功 | hotelCode:{}, isAiCall:{}, isReturnCall:{}", hotelCode, isAiCall, isReturnCall))
                .doOnError(e -> log.error("更新通话统计指标失败 | hotelCode:{}, isAiCall:{}, isReturnCall:{}", hotelCode, isAiCall, isReturnCall, e));
    }

    /**
     * 更新工单创建统计指标
     * 
     * @param hotelCode 酒店代码
     * @param serviceCategory 服务分类
     * @param serviceType 服务类型
     */
    public Mono<Void> updateTicketCreateStat(String hotelCode, String serviceCategory, String serviceType) {
        String bizDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        String cacheKey = String.format(RedisConstant.HOTEL_DAILY_TICKET_STAT_KEY, hotelCode, bizDate);
        
        return Mono.when(
            // 更新总工单数量
            incrementField(cacheKey, RedisConstant.DailyTicketField.TICKET_COUNT),
            // 根据工单类型更新对应分类计数
            updateCategoryCount(cacheKey, serviceCategory, serviceType)
        ).then(setExpire(cacheKey))
        .doOnSuccess(v -> log.info("更新工单创建统计指标成功 | hotelCode:{}, serviceCategory:{}, serviceType:{}", hotelCode, serviceCategory, serviceType))
        .doOnError(e -> log.error("更新工单创建统计指标失败 | hotelCode:{}, serviceCategory:{}, serviceType:{}", hotelCode, serviceCategory, serviceType, e));
    }

    /**
     * 更新工单完成统计指标
     * 
     */
    public Mono<Void> updateTicketCompleteStat(HdsServiceTicketEntity entity) {
        String hotelCode = entity.getHotelCode();
        String serviceCategory = entity.getServiceCategory();
        String serviceType = entity.getServiceType();

        String bizDate = entity.getCreatedAt().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
        String cacheKey = String.format(RedisConstant.HOTEL_DAILY_TICKET_STAT_KEY, hotelCode, bizDate);
        
        return Mono.when(
            // 更新已完成工单数量
            incrementField(cacheKey, RedisConstant.DailyTicketField.COMPLETED_TICKET_COUNT),
            // 根据工单类型更新对应分类完成计数
            updateCategoryCompleteCount(cacheKey, serviceCategory, serviceType)
        ).then(setExpire(cacheKey))
        .doOnSuccess(v -> log.info("更新工单完成统计指标成功 | hotelCode:{}, serviceCategory:{}, serviceType:{}", hotelCode, serviceCategory, serviceType))
        .doOnError(e -> log.error("更新工单完成统计指标失败 | hotelCode:{}, serviceCategory:{}, serviceType:{}", hotelCode, serviceCategory, serviceType, e));
    }

    /**
     * 根据工单类型更新对应分类计数
     */
    private Mono<Void> updateCategoryCount(String cacheKey, String serviceCategory, String serviceType) {
        return Mono.defer(() -> {
            if (ServiceType.INQUIRY.name().equals(serviceType)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.INQUIRY_COUNT);
            } else if (ServiceType.EMERGENCY.name().equals(serviceType)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.EMERGENCY_COUNT);
            } else if (ServiceCategory.COMPLAINT.getCode().equals(serviceCategory)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.COMPLAINT_COUNT);
            } else {
                // 其他服务工单归类为客需
                return incrementField(cacheKey, RedisConstant.DailyTicketField.SERVICE_NEED_COUNT);
            }
        });
    }

    /**
     * 根据工单类型更新对应分类完成计数
     */
    private Mono<Void> updateCategoryCompleteCount(String cacheKey, String serviceCategory, String serviceType) {
        return Mono.defer(() -> {
            if (ServiceType.INQUIRY.name().equals(serviceType)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.INQUIRY_COMPLETED_COUNT);
            } else if (ServiceType.EMERGENCY.name().equals(serviceType)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.EMERGENCY_COMPLETED_COUNT);
            } else if (ServiceCategory.COMPLAINT.getCode().equals(serviceCategory)) {
                return incrementField(cacheKey, RedisConstant.DailyTicketField.COMPLAINT_COMPLETED_COUNT);
            } else {
                // 其他服务工单归类为客需
                return incrementField(cacheKey, RedisConstant.DailyTicketField.SERVICE_NEED_COMPLETED_COUNT);
            }
        });
    }

    /**
     * 递增Redis Hash字段值
     */
    private Mono<Void> incrementField(String cacheKey, String field) {
        return reactiveStringRedisTemplate.opsForHash().increment(cacheKey, field, 1)
                .then();
    }

    /**
     * 设置缓存过期时间（2天）
     */
    private Mono<Void> setExpire(String cacheKey) {
        return reactiveStringRedisTemplate.expire(cacheKey, Duration.ofDays(2))
                .then();
    }
} 