package com.wormhole.hotelds.api.hotel.web.model.req;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.annotation.*;
import lombok.*;
import lombok.experimental.*;

/**
 * @author: joker.liu
 * @date: 2025/2/20
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TokenPairDTO {

    private String accessToken;

    private String refreshToken;

    private Long id;

    private Long expireAt;

}
