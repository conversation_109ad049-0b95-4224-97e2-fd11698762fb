package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/7
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PositionInfoVO implements Serializable {

    /**
     * 位置编号（自定义编码）
     */
    private String positionCode;


    /**
     * 酒店编码
     */
    private String hotelCode;


    /**
     * 楼栋
     */
    private String block;


    /**
     * 楼层
     */
    private Integer floor;


    /**
     * 位置牌号
     */
    private String number;


    /**
     * 位置名称
     */
    private String positionName;


    /**
     * 设备归属类型 (front: ai智能座机; room: ai云电话pad版)
     */
    private String deviceType;


    /**
     * wifi ssid
     */
    private String wifiSsid;


    /**
     * wifi 密码
     */
    private String wifiPassword;
}
