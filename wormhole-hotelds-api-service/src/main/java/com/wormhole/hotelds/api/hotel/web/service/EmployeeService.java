package com.wormhole.hotelds.api.hotel.web.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.enums.*;
import com.wormhole.common.util.*;
import com.wormhole.hotelds.api.hotel.enums.BindHotelTypeEnum;
import com.wormhole.hotelds.api.hotel.resp.*;
import com.wormhole.hotelds.api.hotel.util.SqlUtils;
import com.wormhole.hotelds.api.hotel.web.dao.repository.EmployeeHotelRepository;
import com.wormhole.hotelds.api.hotel.web.dao.repository.EmployeeRepository;
import com.wormhole.hotelds.api.hotel.constant.RoleEnum;
import com.wormhole.hotelds.api.hotel.web.dao.HdsEmployeeTicketMappingDao;
import com.wormhole.hotelds.api.hotel.web.model.req.HdsEmployeeTicketMappingQO;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.*;
import org.springframework.data.r2dbc.core.*;
import org.springframework.data.relational.core.query.*;
import org.springframework.stereotype.*;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;

import javax.annotation.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Author: wj
 * @Date: 2025/4/16 19:55
 */
@Service
@Slf4j
public class EmployeeService {

    @Resource
    private EmployeeHotelRepository employeeHotelRepository;

    @Resource
    private HdsEmployeeTicketMappingDao hdsEmployeeTicketMappingDao;

    @Resource
    private HdsHotelInfoDao hdsHotelInfoDao;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private EmployeeRepository employeeRepository;

    public Mono<EmployeeHotelAccessResp> hasAccess(Integer employeeId, String hotelCode, String source) {
        EmployeeHotelAccessResp resp = new EmployeeHotelAccessResp();
        return employeeHotelRepository.findByEmployeeIdAndStatus(employeeId, EmployeeStatusEnum.ACTIVE.getCode())
                .collectList()
                .flatMap(entities -> {
                    // 酒店后台登录
                    if (StringUtils.isEmpty(hotelCode) && SourcePlatform.HDS_MC.getCode().equals(source)) {
                        boolean hasActiveEntity = entities.stream()
                                .anyMatch(entity -> Objects.equals(entity.getStatus(), 1));
                        if (!hasActiveEntity) {
                            resp.setHasAccess(false);
                            resp.setMessage("当前账号未绑定可用酒店，暂无法使用。");
                            return Mono.just(resp);
                        }

                        // 检查OTA_AGENT_ADMIN角色
                        boolean hasOtaAgentAdmin = entities.stream()
                                .anyMatch(entity -> {
                                    String roleCodes = entity.getRoleCode();
                                    if (StringUtils.isNotEmpty(roleCodes)) {
                                        List<String> roleCodeList = Arrays.asList(roleCodes.split(","));
                                        return roleCodeList.contains(RoleEnum.HOTEL_ADMIN.getCode())
                                                || roleCodeList.contains(RoleEnum.OTA_AGENT_ADMIN.getCode())
                                                || roleCodeList.contains(RoleEnum.OTA_AGENT_STAFF.getCode());
                                    }
                                    return false;
                                });

                        if (!hasOtaAgentAdmin) {
                            log.error("Access denied: Employee {} does not have HOTEL_ADMIN role for source {}", employeeId, source);
                            resp.setHasAccess(false);
                        }else {
                            resp.setHasAccess(true);
                        }
                        return Mono.just(resp);
                    }

                    return Mono.justOrEmpty(entities.stream()
                                    .filter(entity -> StringUtils.equals(hotelCode, entity.getHotelCode()))
                                    .findFirst())
                            .flatMap(entity -> {
                                String roleCodes = entity.getRoleCode();
                                List<String> roleCodeList = Arrays.asList(StringUtils.defaultString(roleCodes).split(","));
                                // source=HOTEL 且只包含 OTA_AGENT_ADMIN 或 OTA_AGENT_STAFF
                                if (SourcePlatform.HOTEL.getCode().equals(source)
                                        && !roleCodeList.isEmpty()
                                        && roleCodeList.stream().allMatch(code ->
                                        RoleEnum.OTA_AGENT_ADMIN.getCode().equals(code)
                                                || RoleEnum.OTA_AGENT_STAFF.getCode().equals(code))) {
                                    log.error("Access denied: Employee {} only has OTA_AGENT_ADMIN/STAFF role for source HOTEL", employeeId);
                                    resp.setHasAccess(false);
                                    resp.setMessage("账号权限受限，暂无法访问。");
                                }
                                // 非当前酒店管理员不允许登录
                                if (!checkAdminRole(entity.getRoleCode()) &&
                                        SourcePlatform.HDS_MC.getCode().equals(source)) {
                                    log.error("Access denied: Employee {} with role HOTEL_STAFF is not allowed for source {}", employeeId, source);
                                    resp.setHasAccess(false);
                                }else {
                                    resp.setHasAccess(true);
                                }
                                return Mono.just(resp);
                            })
                            .switchIfEmpty(Mono.fromCallable(() -> {
                                log.error("Access denied: No matching hotelCode {} found for employee {}", hotelCode, employeeId);
                                resp.setHasAccess(false);
                                return resp;
                            }));
                })
                .onErrorResume(e -> {
                    log.error("Error occurred while checking access for employee {}: {}", employeeId, e.getMessage());
                    resp.setHasAccess(false);
                    return Mono.just(resp);
                });
    }

    private boolean checkAdminRole(String roleCodes) {
        if (StringUtils.isEmpty(roleCodes)) {
            return false;
        }
        List<String> roleCodeList = Arrays.asList(roleCodes.split(","));
        return roleCodeList.contains(RoleEnum.HOTEL_ADMIN.getCode())
                || roleCodeList.contains(RoleEnum.OTA_AGENT_ADMIN.getCode());
    }

    public Mono<List<String>> getNotifyEmployees(String hotelCode) {
        String prefix = SourcePlatform.HDS_MC.getCode().concat("_");
        return employeeHotelRepository.findByHotelCodeAndStatus(hotelCode, EmployeeStatusEnum.ACTIVE.getCode())
                .filter(entity -> StringUtils.equals(entity.getRoleCode(), RoleEnum.HOTEL_ADMIN.getCode()))
                .map(entity -> prefix + entity.getEmployeeId())
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .doOnNext(ids -> {
                    if (CollectionUtils.isEmpty(ids)) {
                        log.warn("No HOTEL_ADMIN employees found for hotelCode: {}", hotelCode);
                    }
                })
                .onErrorResume(e -> {
                    log.error("Error occurred while fetching employee hotel list for hotelCode {}: {}", hotelCode, e.getMessage());
                    return Mono.just(Collections.emptyList());
                });
    }

    /**
     * 获取用户酒店信息
     *
     * @param employeeId 员工ID
     * @param type 员工类型，1为集团员工
     * @return 用户酒店信息
     */
    public Mono<UserHotelInfoResp> getUserHotelInfo(Integer employeeId, Integer type) {
        return getHotelsByEmployeeType(employeeId, type)
                .flatMap(hotels -> {
                    if (Objects.equals(type, 1)) {
                        // 集团员工：所有酒店默认有接单权限
                        return processGroupEmployeeHotels(hotels);
                    } else {
                        // 非集团员工：需要查询接单权限
                        return processNonGroupEmployeeHotels(employeeId, hotels);
                    }
                })
                .defaultIfEmpty(buildUserHotelInfoResp(Collections.emptyList()));
    }

    /**
     * 处理集团员工的酒店信息
     */
    private Mono<UserHotelInfoResp> processGroupEmployeeHotels(List<HotelInfo> hotels) {
        // 集团员工不需要进入接单修改页
        hotels.forEach(hotel -> hotel.setNeedTicketModify(0));
        return Mono.just(buildUserHotelInfoResp(hotels));
    }

    /**
     * 处理非集团员工的酒店信息
     */
    private Mono<UserHotelInfoResp> processNonGroupEmployeeHotels(Integer employeeId, List<HotelInfo> hotels) {
        if (hotels.isEmpty()) {
            return Mono.just(buildUserHotelInfoResp(Collections.emptyList()));
        }

        // 为每个酒店分别查询接单权限
        return Flux.fromIterable(hotels)
                .flatMap(hotel -> getEmployeeTicketMapping(employeeId, hotel.getHotelCode())
                        .map(ticketMapping -> {
                            // 判断是否需要进入接单修改页
                            Integer needTicketModify = shouldEnterTicketModifyPage(ticketMapping);
                            hotel.setNeedTicketModify(needTicketModify);
                            return hotel;
                        })
                        .defaultIfEmpty(hotel))
                .collectList()
                .map(this::buildUserHotelInfoResp);
    }

    /**
     * 判断是否需要进入接单修改页
     * 只有分机且ticketCategories或areaCodes为空时才需要进入
     */
    private Integer shouldEnterTicketModifyPage(HdsEmployeeTicketMappingEntity ticketMapping) {
        if (ticketMapping == null) {
            return 0;
        }

        // 只有分机才能设置接单范围
        if (!Objects.equals(ticketMapping.getEmployeeType(), AccountTypeEnum.EXTENSION.getCode())) {
            return 0;
        }

        // 判断ticketCategories或areaCodes是否为空
        boolean needModify = StringUtils.isBlank(ticketMapping.getTicketCategories())
                || StringUtils.isBlank(ticketMapping.getAreaCodes());

        return needModify ? 1 : 0;
    }

    /**
     * 构建用户酒店信息响应
     */
    private UserHotelInfoResp buildUserHotelInfoResp(List<HotelInfo> hotels) {
        return UserHotelInfoResp.builder()
                .bindHotelType(BindHotelTypeEnum.fromHotelCount(hotels.size()).getCode())
                .hotels(hotels)
                .build();
    }

    /**
     * 获取员工工单映射信息
     */
    private Mono<HdsEmployeeTicketMappingEntity> getEmployeeTicketMapping(Integer employeeId, String hotelCode) {
        HdsEmployeeTicketMappingQO qo = new HdsEmployeeTicketMappingQO();
        qo.setEmployId(employeeId.toString());
        qo.setHotelCode(hotelCode);
        return hdsEmployeeTicketMappingDao.findOne(qo);
    }

    /**
     * 根据员工类型获取酒店信息
     */
    private Mono<List<HotelInfo>> getHotelsByEmployeeType(Integer employeeId, Integer type) {
        if (Objects.equals(type, 1)) {
            // 集团员工
            return getGroupEmployeeHotels();
        } else {
            // 非集团员工
            return getNonGroupEmployeeHotels(employeeId);
        }
    }

    /**
     * 获取集团员工的酒店信息（有AI产品且有效的酒店）
     */
    private Mono<List<HotelInfo>> getGroupEmployeeHotels() {
        Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                .and(HdsHotelInfoFieldEnum.status.name()).is(RowStatusEnum.VALID.getId())
                .and(HdsHotelInfoFieldEnum.ai_product_types.name()).like(SqlUtils.like("AI_CUSTOMER"));

        return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class)
                .map(this::convertToHotelInfo)
                .collectList()
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 获取非集团员工的酒店信息（只返回有AI产品且有效的酒店）
     */
    private Mono<List<HotelInfo>> getNonGroupEmployeeHotels(Integer employeeId) {
        return employeeHotelRepository.findByEmployeeIdAndStatus(employeeId, EmployeeStatusEnum.ACTIVE.getCode())
                .map(HdsEmployeeHotelEntity::getHotelCode)
                .collectList()
                .flatMap(hotelCodes -> {
                    if (hotelCodes.isEmpty()) {
                        return Mono.just(Collections.<HotelInfo>emptyList());
                    }

                    // 批量查询酒店信息
                    Criteria criteria = Criteria.where(HdsHotelInfoFieldEnum.hotel_code.name()).in(hotelCodes)
                            .and(HdsHotelInfoFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId())
                            .and(HdsHotelInfoFieldEnum.status.name()).is(RowStatusEnum.VALID.getId())
                            .and(HdsHotelInfoFieldEnum.ai_product_types.name()).like(SqlUtils.like("AI_CUSTOMER"));

                    return r2dbcEntityTemplate.select(Query.query(criteria), HdsHotelInfoEntity.class)
                            .map(this::convertToHotelInfo)
                            .collectList();
                })
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * HdsHotelInfoEntity 转换为 HotelInfo
     */
    private HotelInfo convertToHotelInfo(HdsHotelInfoEntity hotelInfo) {
        return HotelInfo.builder()
                .hotelCode(hotelInfo.getHotelCode())
                .hotelName(hotelInfo.getHotelName())
                .build();
    }

    /**
     * 根据员工ID获取酒店列表
     *
     * @return 酒店列表
     */
    public Mono<List<HotelInfo>> getHotelsByEmployeeId() {
        // 先查询员工信息获取type
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo ->
                {
                    Integer employeeId = Integer.valueOf(headerInfo.getUserId());
                    return employeeRepository.findById(employeeId)
                            .flatMap(employee -> {
                                if (Objects.equals(employee.getType(), 1)) {
                                    // 集团员工：查询所有有效的酒店
                                    return getGroupEmployeeHotels();
                                } else {
                                    // 非集团员工：查询员工关联的酒店
                                    return getNonGroupEmployeeHotels(employeeId);
                                }
                            })
                            .defaultIfEmpty(Collections.emptyList());
                }
        );
    }

}
