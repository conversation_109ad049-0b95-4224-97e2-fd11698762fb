package com.wormhole.hotelds.api.hotel.web.dao;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.api.hotel.web.model.entity.UserLoginDeviceRecord;
import com.wormhole.hotelds.api.hotel.web.model.field.UserLoginDeviceRecordFieldEnum;
import com.wormhole.hotelds.api.hotel.web.model.req.UserLoginDeviceRecordQO;
import com.wormhole.hotelds.api.hotel.web.dao.repository.UserLoginDeviceRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
@Repository
public class UserLoginDeviceRecordDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Autowired
    private UserLoginDeviceRecordRepository userLoginDeviceRecordRepository;

    public Mono<UserLoginDeviceRecord> save(UserLoginDeviceRecord userLoginDeviceRecord) {
        return userLoginDeviceRecordRepository.save(userLoginDeviceRecord);
    }

    public Mono<UserLoginDeviceRecord> findOne(UserLoginDeviceRecordQO userLoginDeviceRecordQO){
        Criteria criteria = getCriteria(userLoginDeviceRecordQO);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).sort(Sort.by(Sort.Direction.DESC,UserLoginDeviceRecordFieldEnum.created_at.name()))
                .limit(1), UserLoginDeviceRecord.class);
    }

    public Criteria getCriteria(UserLoginDeviceRecordQO userLoginDeviceRecordQO){
        Criteria empty = Criteria.empty();
        if(StringUtils.isNotBlank(userLoginDeviceRecordQO.getUserId())) {
            empty = empty.and(UserLoginDeviceRecordFieldEnum.user_id.name()).is(userLoginDeviceRecordQO.getUserId());
        }
        if(StringUtils.isNotBlank(userLoginDeviceRecordQO.getDeviceType())) {
            empty = empty.and(UserLoginDeviceRecordFieldEnum.device_type.name()).is(userLoginDeviceRecordQO.getDeviceType());
        }
        empty = empty.and(UserLoginDeviceRecordFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
        return empty;
    }
}
