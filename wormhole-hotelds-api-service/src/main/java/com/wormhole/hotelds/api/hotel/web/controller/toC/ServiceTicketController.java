package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.req.RefreshCommandReq;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.service.handler.RtcCallbackService;
import com.wormhole.hotelds.api.hotel.web.model.res.RoomTicketVO;
import com.wormhole.hotelds.api.hotel.web.model.res.ServiceTicketVO;
import com.wormhole.hotelds.api.hotel.web.service.CallBackBizService;
import com.wormhole.hotelds.api.hotel.web.service.RtcService;
import com.wormhole.hotelds.api.hotel.web.service.ServiceTicketService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Collection;

/**
 * 服务工单管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tickets")
public class ServiceTicketController {

    private final ServiceTicketService serviceTicketService;
    private final RtcService rtcService;
    private final RtcCallbackService rtcCallbackService;
    private final CallBackBizService callBackBizService;


    @PostMapping("/create_ticket")
    public Mono<Result<String>> createTicket(@RequestBody CreateTicketReq req) {
        return serviceTicketService.createTicket(req).flatMap(Result::success);
    }

    @PostMapping("/get_tickets")
    public Mono<Result<PageResult<ServiceTicketVO>>> getTickets(@RequestBody GetTicketReq req) {
        return serviceTicketService.getTickets(req).flatMap(Result::success);
    }


    @PostMapping("/get_simple_tickets_json")
    public Mono<Result<String>> getSimpleTicketsJson(@RequestBody GetTicketReq req) {
        return serviceTicketService.getSimpleTicketsJson(req).flatMap(Result::success);
    }

    /**
     * 查询房间工单列表（酒店员工侧）
     * @param req
     * @return
     */
    @PostMapping("/list_by_stuff")
    public Mono<Result<PageResult<RoomTicketVO>>> listByStuff(@RequestBody GetRoomTicketReq req) {
        return  serviceTicketService.listByStuff(req).flatMap(Result::success);
    }

    /**
     * 处理订单
     * @param req
     * @return
     */
    @PostMapping("/handle")
    public Mono<Result<Boolean>> handle(@RequestBody HandleTicketReq req){
        return serviceTicketService.handle(req).flatMap(Result::success);
    }

    /**
     * 调用方：客户端。客户端直接打小程序电话时候，调用此接口，完成电话回拨逻辑。
     * @param req
     * @return
     */
    @PostMapping("/handle_front_desk_mini_program_callback")
    public Mono<Result<Boolean>> handleFrontDeskMiniProgramCallback(@RequestBody FrontDeskCallBackTicketReq req){
        return callBackBizService.handleFrontDeskMiniProgramCallback(req).flatMap(Result::success);
    }
    @PostMapping("/send_refresh_command")
    public Mono<Result<Boolean>> sendRefreshCommand(@RequestBody RefreshCommandReq req) {
        return serviceTicketService.sendRefreshCommand(req).flatMap(Result::success);
    }
//    @PostMapping("/create_sos_ticket")
//    public Mono<Result<Boolean>> createSosTicket(@RequestBody CreateTicketSimpleReq req) {
//        return serviceTicketService.createSosTicket(req).flatMap(Result::success);
//    }


    @PostMapping("/create_ticket_simple")
    public Mono<Result<String>> createTicketSimple(@RequestBody CreateTicketReq req) {
        return serviceTicketService.createTicketSimple(req).flatMap(Result::success);
    }

    @PostMapping("/after_all_create")
    public Mono<Result<Boolean>> afterAllCreate(@RequestBody AfterAllTicketCreateReq req){
        return serviceTicketService.afterAllCreate(req).flatMap(Result::success);
    }

    @PostMapping("/push_rtc_command")
    public Mono<Result<Void>> pushRtcCommand(@RequestBody PushRtcCommandReq req) {
        return rtcService.sendDevice(req.getUserIds(),req.getCallbackMessage()).flatMap(Result::success);
    }
    @PostMapping("/push_call_info_message")
    public Mono<Result<Void>> pushCallInfoMessage(@RequestBody CallInfoMessage message){
        return rtcCallbackService.handleRtcCallStatusCallback(message).flatMap(Result::success);
    }
    @Data
    public static class PushRtcCommandReq {
        private Collection<String> userIds;
        private CallbackMessage callbackMessage;

    }


}
