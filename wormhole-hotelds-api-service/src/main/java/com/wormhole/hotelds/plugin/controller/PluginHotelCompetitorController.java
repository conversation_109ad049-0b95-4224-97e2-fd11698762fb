package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.HotelCompetitorDTO;
import com.wormhole.hotelds.plugin.model.vo.HotelCompetitorVO;
import com.wormhole.hotelds.plugin.service.HotelCompetitorService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 16:26
 */
@RestController
@RequestMapping(value = "/plugin/hotel/competitor")
public class PluginHotelCompetitorController {

    @Resource
    private HotelCompetitorService hotelCompetitorService;


    @PostMapping("/save")
    public Mono<Result<HotelCompetitorVO>> addCompetitor(@RequestBody HotelCompetitorDTO hotelCompetitorDTO) {
        return hotelCompetitorService.addCompetitor(hotelCompetitorDTO).flatMap(Result::success);
    }

    @PostMapping("/batch_save")
    public Mono<Result<List<HotelCompetitorVO>>> batchAddCompetitor(@RequestBody List<HotelCompetitorDTO> hotelCompetitorDTOList) {
        return hotelCompetitorService.batchAddCompetitor(hotelCompetitorDTOList).flatMap(Result::success);
    }

    @GetMapping("/list")
    public Mono<Result<List<HotelCompetitorVO>>> listCompetitors(@RequestParam("hotel_code") String hotelCode,
                                                                 @RequestParam(value = "select_all", required = false) Integer selectAll) {
        return hotelCompetitorService.listCompetitors(hotelCode,selectAll).flatMap(Result::success);
    }

    @GetMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestParam("id") String id) {
        return hotelCompetitorService.delete(id).flatMap(Result::success);
    }

    @GetMapping("/cancel")
    public Mono<Result<Boolean>> cancel(@RequestParam("id") String id) {
        return hotelCompetitorService.cancel(id).flatMap(Result::success);
    }

}
