package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import com.wormhole.task.model.constant.PluginOutCallStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("plugin_call_record")
public class PluginCallRecord extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 数据ID（info表的id，唯一）
     */
    @Column("order_id")
    private String orderId;

    /**
     * 客户ID
     */
    @Column("customer_id")
    private Long customerId;

    /**
     * 呼叫结果 (1:拨通中， 2: 已接通,3: 未接通,)
     */
    @Column("call_status")
    private Integer callStatus;

    /**
     * 呼叫时间
     */
    @Column("call_date")
    private Date callDate;

    /**
     * 手机号
     */
    @Column("contact_number")
    private String contactNumber;

    /**
     * 唯一标识
     */
    @Column("unique_id")
    private String uniqueId;

    /**
     * 完整通话录音 URL
     */
    @Column("record_path")
    private String recordPath;

    /**
     * 通话时长（秒）
     */
    @Column("billsec")
    private BigDecimal billsec;

    /**
     * 评分，对应意向等级
     */
    @Column("scores")
    private Integer scores;


    public static PluginCallRecord init(String orderId, String contactNumber, Date callDate) {
        PluginCallRecord pluginCallRecord = new PluginCallRecord();
        pluginCallRecord.setOrderId(orderId);
        pluginCallRecord.setCallStatus(PluginOutCallStatusEnum.OUT_CALLING.getCode());
        pluginCallRecord.setCallDate(callDate);
        pluginCallRecord.setContactNumber(contactNumber);
        return pluginCallRecord;
    }
}

