package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
public class SavePreReplyDTO {
    private String hotelCode;
    private String content;
    /**
     * @see com.wormhole.hotelds.plugin.model.enums.PluginReplyStyleEnum
     * */
    private String replyStyle;
    private Integer onlyPositive;
}
