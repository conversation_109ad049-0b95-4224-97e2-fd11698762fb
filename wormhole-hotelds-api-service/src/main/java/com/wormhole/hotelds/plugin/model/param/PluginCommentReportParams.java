package com.wormhole.hotelds.plugin.model.param;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-20 15:12:19
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCommentReportParams extends PluginCommentSearchParams {

    private Integer year;

    private Integer month;

    private Integer week;

    private Integer reportIntervalType;

    public static PluginCommentReportParams createParamByTimeRange(String hotelCode, List<String> channels, LocalDateTime startDateTime, LocalDateTime endDateTime, PluginCommentStatisticsIntervalEnum reportIntervalType) {
        PluginCommentReportParams pluginCommentReportParams = new PluginCommentReportParams();
        pluginCommentReportParams.setYear(startDateTime.getYear());
        pluginCommentReportParams.setMonth(startDateTime.getMonthValue());

        pluginCommentReportParams.setWeek(startDateTime.get(WeekFields.ISO.weekOfWeekBasedYear()));

        pluginCommentReportParams.setReportIntervalType(reportIntervalType.getValue());
        pluginCommentReportParams.setStartDateTime(startDateTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        pluginCommentReportParams.setEndDateTime(endDateTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        pluginCommentReportParams.setHotelCode(hotelCode);
        pluginCommentReportParams.setChannels(channels);
        return pluginCommentReportParams;

    }

    public void initDateTimeRange() {
        if(Objects.equals(reportIntervalType,PluginCommentStatisticsIntervalEnum.MONTH.getValue())){
            initDateTimeRangeByMonth();
        }
        if(Objects.equals(reportIntervalType,PluginCommentStatisticsIntervalEnum.WEEK.getValue())){
            initDateTimeRangeByWeek();
        }
    }

    /**
     * 根据年份和月份初始化时间范围
     */
    public void initDateTimeRangeByMonth() {
        Pair<String, String> monthDateTimeRange = toMonthDateTimeRange(DatePattern.NORM_DATETIME_PATTERN);
        super.setStartDateTime(monthDateTimeRange.getFirst());
        super.setEndDateTime(monthDateTimeRange.getSecond());
    }

    public void initDateTimeRangeByWeek() {
        Pair<String, String> weekDateTimeRange = toWeekDateTimeRange(DatePattern.NORM_DATETIME_PATTERN);
        super.setStartDateTime(weekDateTimeRange.getFirst());
        super.setEndDateTime(weekDateTimeRange.getSecond());
    }


    public Pair<String, String> toMonthDateTimeRange(String datePattern) {
        Pair<DateTime, DateTime> monthDateTimeRange = toMonthDateTimeRange();
        return Pair.of(
                monthDateTimeRange.getFirst().toString(datePattern)
                , monthDateTimeRange.getSecond().toString(datePattern)
        );
    }

    public Pair<DateTime, DateTime> toMonthDateTimeRange() {
        DateTime dateTime = DateUtil.parse(this.year + "-" + this.month, "yyyy-MM");
        return Pair.of(DateUtil.beginOfMonth(dateTime), DateUtil.endOfMonth(dateTime));
    }

    public Pair<String, String> toWeekDateTimeRange(String datePattern) {
        Pair<DateTime, DateTime> weekDateTimeRange = toWeekDateTimeRange();

        return Pair.of(
                weekDateTimeRange.getFirst().toString(datePattern)
                , weekDateTimeRange.getSecond().toString(datePattern)
        );
    }

    public Pair<DateTime, DateTime> toWeekDateTimeRange() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.WEEK_OF_YEAR, week);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        DateTime dateTime = DateUtil.date(calendar);
        return Pair.of(DateUtil.beginOfWeek(dateTime, true)
                , DateUtil.endOfWeek(dateTime, true));
    }


}
