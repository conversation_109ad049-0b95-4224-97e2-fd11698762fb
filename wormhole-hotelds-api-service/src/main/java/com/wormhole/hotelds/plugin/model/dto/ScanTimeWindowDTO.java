package com.wormhole.hotelds.plugin.model.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.sql.SQLOutput;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Data
public class ScanTimeWindowDTO {

    /**
     * 左边界时间 - 当前扫描的最新时间点
     * 通常设置为当前时间，表示从这个时间点向过去扫描
     */
    private Date leftTime;

    /**
     * 右边界时间 - 实际扫描到的最旧时间点
     * 用于记录当前扫描进度
     */
    private Date rightTime;

    /**
     * 目标右边界时间 - 期望扫描到的时间点
     * 例如上个月的第一天，用于控制扫描深度
     */
    private Date shouldRight;

    /**
     * 采集完成时间
     */
    private Date finishTime;

    /**
     * 创建一个新的初始化窗口
     * @return 初始化的时间窗口对象
     */
    public static ScanTimeWindowDTO createInitialWindow(boolean ourHotel) {
        Date currentTime = new Date();

        // 获取上个月第一天
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, ourHotel ? -6 : -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date lastMonthFirstDay = calendar.getTime();

        ScanTimeWindowDTO dto = new ScanTimeWindowDTO();
        dto.setLeftTime(currentTime);
        dto.setRightTime(currentTime);
        dto.setShouldRight(lastMonthFirstDay);
        return dto;
    }

    /**
     * 判断是否需要保存当前页数据
     * @param pageFirstTime 页面最新数据的时间
     * @return 是否需要保存
     */
    public boolean shouldSavePage(Date pageFirstTime) {
        return pageFirstTime != null && pageFirstTime.compareTo(shouldRight) >= 0;
    }


    /**
     * 更新扫描边界并判断是否需要继续翻页
     * @param pageLastTime 页面最旧数据的时间
     * @return 是否需要继续翻页
     */
     public boolean updateAndCheckNextPage(Date pageLastTime,boolean lastPage) {
        if (pageLastTime == null) {
            return false;
        }

        this.rightTime = pageLastTime;

        // 判断是否已达到目标深度
        // 如果右边界早于目标时间，则已完成扫描
        if (this.rightTime.before(this.shouldRight) || lastPage) {
            this.shouldRight =  DateUtil.beginOfMonth(new Date());
            Date currentDate = new Date();
            this.rightTime = currentDate;
            this.leftTime = currentDate;
            this.finishTime = currentDate;
            return false; // 不需要继续翻页
        } else {
            return true; // 需要继续翻页
        }
    }
}
