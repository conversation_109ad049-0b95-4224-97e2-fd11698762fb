package com.wormhole.hotelds.plugin.repository;

import com.wormhole.task.model.entity.CommentEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface WpCommentRepository extends ReactiveCrudRepository<CommentEntity, Long> {
    Mono<Boolean> existsByBusinessCommentIdAndRowStatus(String businessCommentId, int id);

    Mono<CommentEntity> findByBusinessCommentIdAndRowStatus(String businessCommentId, int id);
}
