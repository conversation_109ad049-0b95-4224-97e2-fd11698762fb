package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginHotelAbbreviationDTO {
    /**
     * 外部酒店code
     */
    private String externalHotelId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 例如：携程、飞猪、去哪儿等
     */
    private String channel;

    /**
     * 简写
     */
    private String hotelAbbreviation;

    /**
     * 酒店全称
     */
    private String hotelName;
}
