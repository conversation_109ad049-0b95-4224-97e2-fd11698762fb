package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.hotelds.plugin.model.dto.SavePreReplyDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginCommentReplyPrefixContentEntity;
import com.wormhole.hotelds.plugin.model.field.PluginCommentReplyPrefixContentField;
import com.wormhole.task.model.entity.filed.CommentField;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class PluginCommentReplyPrefixContentService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * 根据id查询评论
     */
    public Mono<PluginCommentReplyPrefixContentEntity> getPreReply(String hotelCode) {
        Criteria criteria = Criteria.where(PluginCommentReplyPrefixContentField.HOTEL_CODE.getColumn())
                .is(hotelCode)
                .and(CommentField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginCommentReplyPrefixContentEntity.class)
                .next();
    }

    public Mono<PluginCommentReplyPrefixContentEntity> savePreReply(SavePreReplyDTO savePreReplyDTO) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> getPreReply(savePreReplyDTO.getHotelCode())
                .flatMap(entity -> {
                    // 如果存在，更新内容
                    entity.setReplyPrefixContent(savePreReplyDTO.getContent());
                    entity.setReplyStyle(savePreReplyDTO.getReplyStyle());
                    entity.setOnlyPositive(savePreReplyDTO.getOnlyPositive());
                    entity.setUpdatedAt(java.time.LocalDateTime.now());
                    entity.setUpdatedBy(StringUtils.isNotBlank(headerInfo.getUserId())? headerInfo.getUserId() : "system");
                    entity.setUpdatedByName(StringUtils.isNotBlank(headerInfo.getUsername())? headerInfo.getUsername() : "system");
                    return r2dbcEntityTemplate.update(entity);
                })
                .switchIfEmpty(Mono.defer(() -> {
                    // 如果不存在，则创建新实体
                    PluginCommentReplyPrefixContentEntity entity = new PluginCommentReplyPrefixContentEntity();
                    entity.setHotelCode(savePreReplyDTO.getHotelCode());
                    entity.setReplyPrefixContent(savePreReplyDTO.getContent());
                    entity.setReplyStyle(savePreReplyDTO.getReplyStyle());
                    entity.setOnlyPositive(savePreReplyDTO.getOnlyPositive());
                    entity.setRowStatus(1);
                    entity.setCreatedAt(java.time.LocalDateTime.now());
                    entity.setUpdatedAt(java.time.LocalDateTime.now());
                    entity.setCreatedBy(StringUtils.isNotBlank(headerInfo.getUserId())? headerInfo.getUserId() : "system");
                    entity.setCreatedByName(StringUtils.isNotBlank(headerInfo.getUsername())? headerInfo.getUsername() : "system");
                    entity.setUpdatedBy(StringUtils.isNotBlank(headerInfo.getUserId())? headerInfo.getUserId() : "system");
                    entity.setUpdatedByName(StringUtils.isNotBlank(headerInfo.getUsername())? headerInfo.getUsername() : "system");
                    return r2dbcEntityTemplate.insert(entity);
                })));
    }
}