package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.plugin.model.entity.HdsLowestPriceCollectRecord;
import com.wormhole.hotelds.plugin.model.entity.HdsLowestPriceCollectRecordField;
import com.wormhole.hotelds.plugin.model.qo.HdsLowestPriceCollectRecordQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Repository
public class HdsLowestPriceCollectRecordDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;


    public Mono<HdsLowestPriceCollectRecord> save(HdsLowestPriceCollectRecord hdsLowestPriceCollectRecord) {
        return r2dbcEntityTemplate.insert(hdsLowestPriceCollectRecord);
    }


    public Mono<List<HdsLowestPriceCollectRecord>> findList(HdsLowestPriceCollectRecordQO hdsLowestPriceCollectRecordQO) {
        Criteria criteria = getCriteria(hdsLowestPriceCollectRecordQO);
        return r2dbcEntityTemplate.select(Query.query(criteria), HdsLowestPriceCollectRecord.class).collectList();
    }

    public Criteria getCriteria(HdsLowestPriceCollectRecordQO hdsLowestPriceCollectRecordQO) {
        Criteria criteria = Criteria.empty();
        if (CollUtil.isNotEmpty(hdsLowestPriceCollectRecordQO.getExternalHotelIds())) {
            criteria = criteria.and(HdsLowestPriceCollectRecordField.externalHotelId.name()).in(hdsLowestPriceCollectRecordQO.getExternalHotelIds());
        }
        if(CollUtil.isNotEmpty(hdsLowestPriceCollectRecordQO.getPriceDates())) {
            criteria = criteria.and(HdsLowestPriceCollectRecordField.priceDate.name()).in(hdsLowestPriceCollectRecordQO.getPriceDates());
        }
        return criteria;
    }
}