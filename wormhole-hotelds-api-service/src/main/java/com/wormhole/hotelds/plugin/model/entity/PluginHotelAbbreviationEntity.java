package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.task.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@Table("plugin_hotel_abbreviation")
public class PluginHotelAbbreviationEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 外部酒店code
     */
    private String externalHotelId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 例如：携程、飞猪、去哪儿等
     */
    private String channel;

    /**
     * 简写
     */
    private String hotelAbbreviation;

    /**
     * 酒店全称
     */
    private String hotelName;
}


