package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.plugin.model.qo.HotelPriceDeleteQO;
import com.wormhole.hotelds.plugin.model.qo.HotelPriceQO;
import com.wormhole.task.model.entity.filed.HotelPriceIndexField;
import com.wormhole.task.model.entity.index.HotelPriceIndex;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Repository
@Slf4j
public class HotelPriceDao {

    @Autowired
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    public Mono<HotelPriceIndex> save(HotelPriceIndex hotelPriceIndex) {
        return reactiveElasticsearchTemplate.save(hotelPriceIndex);
    }

    public Mono<HotelPriceIndex> findOne(HotelPriceQO hotelPriceQO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(hotelPriceQO.getBizId())) {
            criteria.and(HotelPriceIndexField.BIZ_ID.getColumn()).is(hotelPriceQO.getBizId());
        }
        CriteriaQuery query = new CriteriaQuery(criteria);
        query.setMaxResults(1);
        return reactiveElasticsearchTemplate.search(query, HotelPriceIndex.class)
                .map(SearchHit::getContent)
                .next()
                .switchIfEmpty(Mono.empty());
    }

    public Mono<List<HotelPriceIndex>> saveAll(List<HotelPriceIndex> hotelPriceIndexList) {
        return reactiveElasticsearchTemplate.saveAll(hotelPriceIndexList, HotelPriceIndex.class).collectList();
    }

    public Mono<Boolean> delete(HotelPriceDeleteQO hotelPriceDeleteQO) {
        if (StringUtils.isBlank(hotelPriceDeleteQO.getHotelCode())
                || StringUtils.isBlank(hotelPriceDeleteQO.getSelfHotelCode())
                || CollUtil.isEmpty(hotelPriceDeleteQO.getBizIds())) {
            log.info("酒店编码为空,不删除");
            return Mono.just(false);
        }
        Criteria criteria = new Criteria();
        criteria = criteria.and(HotelPriceIndexField.HOTEL_CODE.getColumn()).is(hotelPriceDeleteQO.getHotelCode());
        criteria = criteria.and(HotelPriceIndexField.SELF_HOTEL_CODE.getColumn()).is(hotelPriceDeleteQO.getSelfHotelCode());
        criteria = criteria.and(HotelPriceIndexField.BIZ_ID.getColumn()).in(hotelPriceDeleteQO.getBizIds());
        Query query = new CriteriaQuery(criteria);

        return reactiveElasticsearchTemplate.delete(query, HotelPriceIndex.class).then(Mono.just(true));
    }

}
