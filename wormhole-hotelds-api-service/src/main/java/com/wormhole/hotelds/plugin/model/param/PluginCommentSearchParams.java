package com.wormhole.hotelds.plugin.model.param;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.wormhole.hotelds.plugin.model.dto.DateRange;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PluginCommentSearchParams {


    private String startDateTime;

    private String endDateTime;

    private String hotelCode;

    private List<String> channels;

    public DateRange toDateRange(String datePattern) {
        String start = DateUtil.parse(startDateTime).toString(datePattern);
        String end = DateUtil.parse(endDateTime).toString(datePattern);
        return new DateRange(start, end);
    }

    public Pair<LocalDateTime, LocalDateTime> toLocalDateTime() {
        LocalDateTime startDateTime = LocalDateTime.parse(this.getStartDateTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        LocalDateTime endDateTime = LocalDateTime.parse(this.getEndDateTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        return Pair.of(startDateTime, endDateTime);
    }

    public Pair<LocalDate,LocalDate> toLocalDate(){
        LocalDate startDateTime = LocalDate.parse(this.getStartDateTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        LocalDate endDateTime = LocalDate.parse(this.getEndDateTime(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        return Pair.of(startDateTime, endDateTime);
    }


    /**
     * 克隆当前对象，并将 startDateTime 调整为 n 个月前或 n 个周前，endDateTime 保持不变。
     *
     * @param n    时间间隔的数量
     * @param unit 时间单位 ("MONTH" 或 "WEEK")
     * @return 克隆后的对象
     */
    public PluginCommentSearchParams cloneWithOffsetStartDateTime(int n, DateField unit) {
        // 克隆当前对象
        PluginCommentSearchParams cloned = new PluginCommentSearchParams();
        cloned.setStartDateTime(this.getStartDateTime());
        cloned.setEndDateTime(this.getEndDateTime());
        cloned.setHotelCode(this.getHotelCode());
        cloned.setChannels(this.getChannels());

        // 计算新的 startDateTime
        DateTime originalStart = DateUtil.parse(this.getStartDateTime(), DatePattern.NORM_DATETIME_PATTERN);
        DateTime adjustedStart = DateUtil.offset(originalStart, unit, n);

        // 设置新的 startDateTime
        cloned.setStartDateTime(adjustedStart.toString(DatePattern.NORM_DATETIME_PATTERN));

        return cloned;
    }

    /**
     * 克隆当前对象，并将 开始和结束 时间调整n个时间单位
     *
     * @param n    时间间隔的数量
     * @param unit 时间单位 ("MONTH" 或 "WEEK")
     * @return 克隆后的对象
     */
    public PluginCommentSearchParams cloneWithOffsetDateTime(int n, DateField unit) {

        DateTime originalStartDateTime = DateUtil.parse(this.getStartDateTime(), DatePattern.NORM_DATETIME_PATTERN);
        DateTime originalEndDateTime = DateUtil.parse(this.getEndDateTime(), DatePattern.NORM_DATETIME_PATTERN);

        PluginCommentSearchParams cloned = new PluginCommentSearchParams();
        cloned.setStartDateTime(DateUtil.offset(originalStartDateTime, unit, n).toString(DatePattern.NORM_DATETIME_PATTERN));
        cloned.setEndDateTime(DateUtil.offset(originalEndDateTime, unit, n).toString(DatePattern.NORM_DATETIME_PATTERN));
        cloned.setHotelCode(this.getHotelCode());
        cloned.setChannels(this.getChannels());

        return cloned;

    }
}
