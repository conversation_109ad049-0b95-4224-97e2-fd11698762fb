package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderFormDateDTO;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderRecordDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginOrderRecordEntity;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderDealQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderPageByTabQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderTabListQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTimeVO;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderUpdateMoveStatusVo;
import com.wormhole.hotelds.plugin.service.PluginOrderIndexService;
import com.wormhole.hotelds.plugin.service.PluginOrderRecordService;
import com.wormhole.task.model.dto.PluginOrderJsonDTO;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import com.wormhole.task.model.vo.PluginOrderVO;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/plugin/order")
public class PluginOrderController {
    @Resource
    private PluginOrderIndexService pluginOrderIndexService;
    @Resource
    private PluginOrderRecordService pluginOrderRecordService;


    @PostMapping("/tab/list")
    public Mono<Result<List<PluginOrderTabVo>>> listOrderTab(@RequestBody PluginOrderTabListQo qo) {
        return pluginOrderIndexService.listOrderTab(qo).flatMap(Result::success);
    }


    @PostMapping("/query_list")
    public Mono<Result<PageResult<PluginOrderVO>>> queryList(@RequestBody PluginOrderPageByTabQo pluginOrderQo) {
        return pluginOrderIndexService.pagePluginOrder(pluginOrderQo).flatMap(Result::success);
    }

    @PostMapping("/move_order/not_deal")
    public Mono<Result<PluginOrderUpdateMoveStatusVo>> moveOrderNotDeal(@RequestBody PluginOrderDealQo qo) {
        return pluginOrderIndexService.moveOrderNotDeal(qo).flatMap(Result::success);
    }

    @PostMapping("/move_order/deal")
    public Mono<Result<PluginOrderUpdateMoveStatusVo>> dealMoveOrder(@RequestBody @Valid PluginOrderDealQo qo) {
        return pluginOrderIndexService.dealMoveOrder(qo).flatMap(Result::success);
    }

    @PostMapping("/get_from_date")
    public Mono<Result<PluginOrderTimeVO>> getFromDate(@RequestBody @Valid PluginOrderFormDateDTO formDateDTO) {
        // 1. 直接调用 record service 的新方法获取所有记录
        return pluginOrderRecordService.findRecordsByHotelAndPlatform(formDateDTO.getHotelCode(), formDateDTO.getPlatform())
                .collectList() // 2. 将所有记录收集到一个 List 中
                .map(records -> {
                    // 3. 将 PluginOrderRecordEntity 列表转换为 FormDateItem 列表
                    List<PluginOrderTimeVO.FormDateItem> formDateItems = records.stream()
                            .map(record -> new PluginOrderTimeVO.FormDateItem(record.getSourceType(), record.getFormDate()))
                            .toList();

                    // 4. 构建最终的 VO 对象
                    PluginOrderTimeVO resultVO = new PluginOrderTimeVO();
                    resultVO.setHotelCode(formDateDTO.getHotelCode());
                    resultVO.setFormDateList(formDateItems);
                    return resultVO;
                })
                .flatMap(vo -> Result.success(vo)) // 5. 包装成统一的成功结果
                .switchIfEmpty(Result.success(new PluginOrderTimeVO(formDateDTO.getHotelCode(), new ArrayList<>()))); // 6. 如果没有记录，返回一个包含空列表的成功结果
    }


    @PostMapping("/record")
    public Mono<Result<PluginOrderRecordEntity>> record(@RequestBody @Valid PluginOrderRecordDTO pluginOrderRecordDTO) {
        return pluginOrderRecordService.record(pluginOrderRecordDTO).flatMap(Result::success);
    }

    @PostMapping("/real_time/sync")
    public Mono<Result<PluginOrderIndex>> orderSync(@RequestBody PluginOrderJsonDTO pluginOrderJsonDTO) {
        return pluginOrderIndexService.orderSync(pluginOrderJsonDTO).flatMap(Result::success);
    }

}
