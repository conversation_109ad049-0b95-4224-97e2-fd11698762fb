package com.wormhole.hotelds.plugin.model.vo;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.dto.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 周度评论统计报告VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCommentStatisticsWeekReportVo {

    /**
     * 报告类型
     */
    private String type;

    /**
     * 时间范围
     */
    private DateRange dateRange;

    /**
     * 数据对比
     */
    private WeekDataComparison dataComparison = new WeekDataComparison();

    // todo 本周ai洞察
    private List<AiInsightVO> aiInsight;

    /**
     * 差评列表
     */
    private PluginReportBadCommentInfoDto badCommentInfo;

    /**
     * 设置时间范围
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void setDateRange(String startDate, String endDate) {
        this.dateRange = new DateRange(startDate, endDate);
    }

    public void setWeeklyScore(List<ScoreItem> monthlyScore) {
        this.getDataComparison().setCommentScore(new WeekCommentScore(monthlyScore));
    }


    public void setWeeklyCommentCountRange(List<ScoreItem> goodCommentCount, List<ScoreItem> badCommentCount) {
        this.getDataComparison().setCommentCountRange(new WeekCommentCountRange(goodCommentCount, badCommentCount));
    }

    /**
     * 周度数据对比
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class WeekDataComparison {
        /**
         * 评分
         */
        private WeekCommentScore commentScore;

        /**
         * 评价数分布
         */
        private WeekCommentCountRange commentCountRange;
    }
}