package com.wormhole.hotelds.plugin.model.enums;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.task.model.constant.PluginOrderMoveStatusEnum;
import com.wormhole.task.model.constant.PluginOrderStatusEnum;
import com.wormhole.task.model.entity.filed.PluginOrderField;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.domain.Sort;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-17 10:41:08
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginOrderTabEnum {


//    TODAY_NEW_ORDER(1, "今日新订", 100) {
//        @Override
//        public void assembleOrderConditionParam(PluginOrderQo pluginOrderQo) {
//            LocalDateTime now = LocalDateTimeUtil.now();
//
//            LocalDateTime start = LocalDateTimeUtil.beginOfDay(now);
//            String startDateTimeStr = LocalDateTimeUtil.format(start, DatePattern.NORM_DATETIME_PATTERN);
//
//            LocalDateTime end = LocalDateTimeUtil.endOfDay(now);
//            String endDateTimeStr = LocalDateTimeUtil.format(end, DatePattern.NORM_DATETIME_PATTERN);
//            pluginOrderQo.setStartDateTime(startDateTimeStr);
//            pluginOrderQo.setEndDateTime(endDateTimeStr);
//        }
//    },

    UNMOVED_ORDER(2, "未搬单", 99, true) {
        @Override
        public void assembleOrderConditionParam(PluginOrderQo pluginOrderQo) {
            List<Integer> moveOrderStatusList = CollUtil.toList(
                    PluginOrderMoveStatusEnum.UNMOVED_ORDER.getValue(),
                    PluginOrderMoveStatusEnum.NOT_DEAL.getValue()
            );
            pluginOrderQo.setMoveOrderStatusList(moveOrderStatusList);

            List<Integer> orderStatus = CollUtil.toList(
                    PluginOrderStatusEnum.WAIT_CONFIRMED.getValue(),
                    PluginOrderStatusEnum.CANCEL.getValue(),
                    PluginOrderStatusEnum.ALREADY_CONFIRMED.getValue()
            );
            pluginOrderQo.setOrderStatus(orderStatus);
        }
    },

    ALL_ORDER(5, "全部订单", 96, false),

    ;

    private final int value;

    private final String name;

    private final int weight;

    private final boolean tipsDisplay;


    private static final Map<Integer, PluginOrderTabEnum> MAP = Arrays.stream(values())
            .collect(Collectors.toMap(PluginOrderTabEnum::getValue, Function.identity()));

    public static PluginOrderTabEnum fromValue(int value) {
        return MAP.getOrDefault(value, PluginOrderTabEnum.ALL_ORDER);
    }

    public static List<PluginOrderTabEnum> listTabEnum() {
        return Arrays.stream(PluginOrderTabEnum.values())
                .sorted(Comparator.comparingInt(PluginOrderTabEnum::getWeight).reversed())
                .toList();
    }

    protected void assembleOrderConditionParam(PluginOrderQo pluginOrderQo) {

    }

    public PluginOrderQo buildOrderQo(String hotelCode, String platform) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setPlatform(platform);

        assembleOrderConditionParam(pluginOrderQo);

        Sort sort = buildSort();
        pluginOrderQo.setSort(sort);
        return pluginOrderQo;
    }

    public Sort buildSort() {
        return Sort.by(
                Sort.Order.asc(PluginOrderField.MOVE_ORDER_STATUS.getColumn())
                , Sort.Order.asc(PluginOrderField.CHECK_IN_DATE.getColumn())
                , Sort.Order.desc(PluginOrderField.UPDATED_AT.getColumn())
                , Sort.Order.desc(PluginOrderField.ID.getColumn())
        );
    }

    public List<Object> buildSearchAfter(PluginOrderIndex pluginOrderIndex) {
        if (Objects.isNull(pluginOrderIndex)) {
            return new ArrayList<>();
        }
        ZonedDateTime zonedDateTime = pluginOrderIndex
                .getUpdatedAt()
                .atZone(ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;


        return CollUtil.toList(
                pluginOrderIndex.getMoveOrderStatus(),
                pluginOrderIndex.getCheckInDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                formatter.format(zonedDateTime),
                pluginOrderIndex.getId()
        );
    }
}
