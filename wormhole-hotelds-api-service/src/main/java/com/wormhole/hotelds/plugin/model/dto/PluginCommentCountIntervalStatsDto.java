package com.wormhole.hotelds.plugin.model.dto;

import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-21 10:32:11
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginCommentCountIntervalStatsDto {

    private PluginCommentStatisticsIntervalEnum intervalEnum;

    private List<TimeIntervalStats> intervals;


    public static PluginCommentCountIntervalStatsDto createWithIntervals(PluginCommentStatisticsIntervalEnum intervalEnum, List<TimeIntervalStats> intervals) {
        PluginCommentCountIntervalStatsDto dto = new PluginCommentCountIntervalStatsDto();
        dto.setIntervalEnum(intervalEnum);
        dto.setIntervals(intervals);

        return dto;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimeIntervalStats {

        private String intervalStart;

        private Long negativeCommentCount;

        private Long positiveCommentCount;

    }

}
