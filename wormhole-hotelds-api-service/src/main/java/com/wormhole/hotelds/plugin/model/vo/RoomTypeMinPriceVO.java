package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/22 19:24
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoomTypeMinPriceVO {

    private String roomTypeName;

    private Long minPrice;

    private String productCode;

    private String roomCode;

    private Integer breakfastCount;

    private Integer cancelType;

    private String cancelInfo;

    private String createdAt;

    private String hotelName;

    private String priceDate;

}
