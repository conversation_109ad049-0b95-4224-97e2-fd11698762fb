package com.wormhole.hotelds.plugin.dao;

import com.wormhole.hotelds.plugin.model.entity.PluginCallRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-14 10:57:36
 * @Description:
 */
@Repository
public class PluginCallRecordDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<Boolean> save(PluginCallRecord pluginCallRecord){
        return r2dbcEntityTemplate.insert(pluginCallRecord)
                .map(Objects::nonNull);
    }


}
