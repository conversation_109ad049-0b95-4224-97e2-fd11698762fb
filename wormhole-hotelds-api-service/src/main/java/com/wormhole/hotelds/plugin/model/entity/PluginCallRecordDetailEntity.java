package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.util.Date;

@Data
@Table("plugin_call_record_detail")
public class PluginCallRecordDetailEntity extends BaseEntity {

    /** 主键ID */
    @Id
    @Column("id")
    private Long id;

    /** 关联唯一标识，与call_info表的unique_id关联 */
    @Column("unique_id")
    private String uniqueId;

    /** 聊天内容 */
    @Column("msg")
    private String msg;

    /** 聊天录音路径（相对路径） */
    @Column("record")
    private String record;

    /** 聊天录音短URL路径 */
    @Column("path")
    private String path;

    /** 发言者 (0: 机器人, 1: 客户) */
    @Column("user")
    private Byte user;

    /** 聊天时间 */
    @Column("create_time")
    private Date createTime;
}