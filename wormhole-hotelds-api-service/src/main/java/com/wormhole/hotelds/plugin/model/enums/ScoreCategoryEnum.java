package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.checkerframework.common.value.qual.ArrayLen;

/**
 * 评分分类枚举
 */
@AllArgsConstructor
@Getter
public enum ScoreCategoryEnum {

    ENVIRONMENT("ENVIRONMENT", "环境", "环境评分"),
    HYGIENE("HYGIENE", "卫生", "卫生评分"),
    SERVICE("SERVICE", "服务", "服务评分"),
    FACILITIES("FACILITIES", "设施", "设施评分");

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String description;

    /**
     * 根据名称获取枚举
     */
    public static ScoreCategoryEnum getByName(String name) {
        for (ScoreCategoryEnum category : values()) {
            if (category.getName().equals(name)) {
                return category;
            }
        }
        return null;
    }
}