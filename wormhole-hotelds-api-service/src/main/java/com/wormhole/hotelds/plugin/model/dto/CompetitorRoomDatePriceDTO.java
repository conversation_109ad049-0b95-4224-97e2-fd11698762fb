package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/14
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompetitorRoomDatePriceDTO implements Serializable {
    private String competitorHotelId;
    private String competitorHotelName;
    private String date;
    private Long currentPrice;
    private Long previousPrice;
    private LocalDateTime lastCollectionDatetime;
}
