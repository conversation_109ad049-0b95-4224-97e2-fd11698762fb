package com.wormhole.hotelds.plugin.dao;

import com.wormhole.hotelds.plugin.model.entity.HdsCollectDataRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Repository
public class HdsCollectDataRecordDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<HdsCollectDataRecord> save(HdsCollectDataRecord hdsCollectDataRecord) {
        return r2dbcEntityTemplate.insert(hdsCollectDataRecord);
    }
}
