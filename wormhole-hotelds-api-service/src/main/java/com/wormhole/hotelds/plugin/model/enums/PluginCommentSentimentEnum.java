package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-14 10:55:22
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginCommentSentimentEnum {

    POSITIVE("positive", "正面"),
    NEGATIVE("negative", "负面"),
    NEUTRAL("neutral", "中性"),

    ;


    private final String code;

    private final String desc;


}
