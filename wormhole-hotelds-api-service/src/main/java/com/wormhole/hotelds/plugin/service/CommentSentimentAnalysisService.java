package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.task.model.entity.CommentLabelAnalysisEntity;
import com.wormhole.task.model.entity.CommentSentimentAnalysisEntity;
import com.wormhole.task.model.entity.filed.CommentField;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Service
public class CommentSentimentAnalysisService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    public Mono<CommentSentimentAnalysisEntity> getByCommentId(String commentId) {
        Criteria criteria = Criteria.where("comment_id")
                .is(commentId)
                .and(CommentField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), CommentSentimentAnalysisEntity.class)
                .next();
    }
}
