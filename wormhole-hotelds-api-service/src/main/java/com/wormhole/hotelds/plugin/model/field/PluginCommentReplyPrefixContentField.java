package com.wormhole.hotelds.plugin.model.field;

import lombok.Getter;

@Getter
public enum PluginCommentReplyPrefixContentField {
    ID("id", "主键ID"),
    ROW_STATUS("row_status", "记录状态：0-删除，1-有效"),
    CREATED_BY("created_by", "创建人ID"),
    CREATED_BY_NAME("created_by_name", "创建人名称"),
    UPDATED_BY("updated_by", "修改人ID"),
    UPDATED_BY_NAME("updated_by_name", "修改人名称"),
    CREATED_AT("created_at", "创建时间"),
    UPDATED_AT("updated_at", "更新时间"),
    HOTEL_CODE("hotel_code", "酒店code，关联酒店的标识"),
    REPLY_PREFIX_CONTENT("reply_prefix_content", "酒店固定回复前缀内容");

    private final String column;
    private final String desc;

    PluginCommentReplyPrefixContentField(String column, String desc) {
        this.column = column;
        this.desc = desc;
    }
}
