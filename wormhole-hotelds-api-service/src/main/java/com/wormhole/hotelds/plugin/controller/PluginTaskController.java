package com.wormhole.hotelds.plugin.controller;

import com.wormhole.agent.knowledge.model.dto.PluginHotelDTO;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.HotelCommentDTO;
import com.wormhole.hotelds.plugin.model.dto.HotelCommentReviewScoreDTO;
import com.wormhole.hotelds.plugin.model.dto.TaskDTO;
import com.wormhole.hotelds.plugin.model.vo.HotelCommentVO;
import com.wormhole.hotelds.plugin.model.vo.TaskVO;
import com.wormhole.hotelds.plugin.service.PluginTaskService;
import com.wormhole.task.model.dto.*;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping(value = "/plugin/task")
public class PluginTaskController {
    @Resource
    private PluginTaskService pluginTaskService;
    @PostMapping("/getLastTask")
    public Mono<Result<List<TaskVO>>> getLastTask(@RequestBody TaskDTO taskDTO) {
        return pluginTaskService.getLastTask(taskDTO).collectList().flatMap(Result::success);
    }

    @PostMapping("/hotel_info/sync")
    public Mono<Result<TaskScheduleDetailVO>> hotelInfoSync(@RequestBody PluginHotelDTO hotelDTO) {
        return pluginTaskService.hotelInfoSync(hotelDTO).flatMap(Result::success);
    }

    @PostMapping("/comment/batchSync")
    public Mono<Result<TaskScheduleDetailVO>> commentBatchSync(@RequestBody EbkBatchCommentDTO ebkBatchCommentDTO) {
        return pluginTaskService.commentBatchSync(ebkBatchCommentDTO).flatMap(Result::success);
    }

    @PostMapping("/comment/singleSync")
    public Mono<Result<TaskScheduleDetailVO>> commentSingleSync(@RequestBody EbkSingleCommentDTO ebkSingleCommentDTO) {
        return pluginTaskService.commentSingleSync(ebkSingleCommentDTO).flatMap(Result::success);
    }

    @PostMapping("/hotel_price/sync")
    public Mono<Result<TaskScheduleDetailVO>> hotelPriceSync(@RequestBody HotelPriceDTO hotelPriceDTO) {
        return pluginTaskService.hotelPriceSync(hotelPriceDTO).flatMap(Result::success);
    }


    @PostMapping("/hotel_comment/sync")
    public Mono<Result<HotelCommentVO>> hotelCommentSync(@RequestBody HotelCommentDTO hotelCommentDTO) {
        return pluginTaskService.hotelCommentSync(hotelCommentDTO).flatMap(Result::success);
    }
    @PostMapping("/hotel_comment_review_score/sync")
    public Mono<Result<Boolean>> hotelCommentReviewScoreSync(@RequestBody HotelCommentReviewScoreDTO hotelCommentReviewScoreDTO){
        return pluginTaskService.hotelCommentReviewScoreSync(hotelCommentReviewScoreDTO).flatMap(Result::success);
    }

    @PostMapping("/order/sync")
    public Mono<Result<TaskScheduleDetailVO>> orderSync(@RequestBody PluginOrderJsonDTO pluginOrderJsonDTO) {
        return pluginTaskService.orderSync(pluginOrderJsonDTO).flatMap(Result::success);
    }
}
