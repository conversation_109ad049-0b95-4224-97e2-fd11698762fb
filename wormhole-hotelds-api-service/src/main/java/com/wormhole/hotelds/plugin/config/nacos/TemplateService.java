package com.wormhole.hotelds.plugin.config.nacos;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.config.listener.AbstractListener;
import com.alibaba.nacos.api.exception.NacosException;
import com.wormhole.common.util.LocalResourceUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TemplateListener
 *
 * <AUTHOR>
 * @version 2025/1/1
 */
@Slf4j
@Component
public class TemplateService implements EnvironmentAware {

    private final Map<TemplateEnum, Object> templateMap = new ConcurrentHashMap<>();

    @Resource
    private NacosConfigManager nacosConfigManager;

    private static List<String> activeProfiles;

    @PostConstruct
    public void init() {
        Arrays.stream(TemplateEnum.values()).forEach(this::addConfigListener);
    }

    @Override
    public void setEnvironment(Environment environment) {
        activeProfiles = Arrays.asList(environment.getActiveProfiles());
    }

    private void addConfigListener(TemplateEnum templateEnum) {
        try {
            AbstractListener listener = createListener(templateEnum);
            nacosConfigManager.getConfigService().addListener(templateEnum.getDataId(), templateEnum.getGroup(), listener);
            String config = nacosConfigManager.getConfigService().getConfig(templateEnum.getDataId(), templateEnum.getGroup(), NacosConstant.TIMEOUT_MS);
            if (config != null) {
                listener.receiveConfigInfo(config);
            }
        } catch (NacosException e) {
            log.error("Failed to process config - dataId: {}", templateEnum.getDataId(), e);
            throw new ContextedRuntimeException(e).addContextValue("dataId", templateEnum.getDataId());
        }
    }

    private AbstractListener createListener(TemplateEnum templateEnum) {
        return new AbstractListener() {
            @Override
            public void receiveConfigInfo(String content) {
                Object parsedContent = templateEnum.parseContent(content);
                templateMap.put(templateEnum, parsedContent);
                log.info("Config updated - dataId: {}, group: {}", templateEnum.getDataId(), templateEnum.getGroup());
            }
        };
    }

    @SuppressWarnings("unchecked")
    public <T> T getTemplate(TemplateEnum templateEnum) {
        // 开发环境，或者template明确指定了使用本地的提示词
        Optional<String> local = activeProfiles.stream().filter(profile -> StringUtils.equalsIgnoreCase(profile, "local")).findFirst();
        if (templateEnum.isUseLocal() || local.isPresent()) {
            String dataId = templateEnum.getDataId();
            try {
                String template = LocalResourceUtils.readLocalFile("ftl/" + dataId + ".ftl");
                return (T) template;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return (T) templateMap.get(templateEnum);
    }
}