package com.wormhole.hotelds.plugin.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wormhole.task.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Table("plugin_order_record")
public class PluginOrderRecordEntity  extends BaseEntity {
    /** 主键ID */
    @Id
    @Column("id")
    private Long id;

    /** 酒店code，自家酒店的hotel_code */
    private String hotelCode;

    /** 平台 */
    private String platform;

    /** Ebooking/CorporateTravel */
    private String sourceType;


    private Boolean isEnd; // 是否结束

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime formDate;
}
