package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.dto.PluginReportParams;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsMonthReportVo;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsReportVo;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsWeekReportVo;
import com.wormhole.hotelds.plugin.service.PluginCommentStatisticsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-13 14:06:28
 * @Description:
 */
@RestController
@RequestMapping(value = "/plugin/statistics/comment")
public class PluginCommentStatisticsController {

    @Resource
    private PluginCommentStatisticsService pluginCommentStatisticsService;

    @PostMapping("/report/month")
    public Mono<Result<PluginCommentStatisticsMonthReportVo>> monthReport(@RequestBody PluginCommentReportParams params) {
        return pluginCommentStatisticsService.searchMonthReportData(params).flatMap(Result::success);
    }

    @PostMapping("/report/week")
    public Mono<Result<PluginCommentStatisticsWeekReportVo>> weeklyReport(@RequestBody PluginCommentReportParams pluginReportParams) {
        return pluginCommentStatisticsService.searchWeekReportData(pluginReportParams).flatMap(Result::success);
    }

    @PostMapping("/report/create")
    public Mono<Result<Boolean>> createReportTask(@RequestBody PluginCommentReportParams params) {
        return pluginCommentStatisticsService.createReportTask(params).flatMap(Result::success);
    }

    @PostMapping("/report/info")
    public Mono<Result<PluginCommentStatisticsReportVo>> getReportInfo(@RequestBody PluginCommentReportParams params) {
        return pluginCommentStatisticsService.getReportInfo(params).flatMap(Result::success);
    }

    @PostMapping("/report/month/initInfo")
    public Mono<Result<PluginCommentStatisticsReportVo>> monthReportInitInfo(@RequestBody PluginCommentReportParams params) {
        params.setReportIntervalType(PluginCommentStatisticsIntervalEnum.MONTH.getValue());
        return pluginCommentStatisticsService.monthReportInitInfo(params).flatMap(Result::success);
    }

    @PostMapping("/report/month/reportDate")
    public Mono<Result<PluginCommentStatisticsMonthReportVo>> getMonthReportDate(@RequestBody PluginCommentReportParams params) {
        return pluginCommentStatisticsService.buildMonthReportData(params).flatMap(Result::success);
    }
}
