package com.wormhole.hotelds.plugin.model.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/14
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompetitorRoomDatePriceReq implements Serializable {
    private String hotelCode;
    private String competitorHotelId;
    private String date;
}
