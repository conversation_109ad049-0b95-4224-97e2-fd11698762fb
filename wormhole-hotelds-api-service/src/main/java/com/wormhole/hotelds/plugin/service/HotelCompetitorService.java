package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.util.LockUtils;
import com.wormhole.hotelds.plugin.dao.WpHotelCompetitorDao;
import com.wormhole.hotelds.plugin.model.dto.HotelCompetitorDTO;
import com.wormhole.hotelds.plugin.model.dto.PluginHotelAbbreviationDTO;
import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import com.wormhole.hotelds.plugin.model.entity.PluginHotelAbbreviationEntity;
import com.wormhole.hotelds.plugin.model.qo.HotelCompetitorQO;
import com.wormhole.hotelds.plugin.model.vo.HotelCompetitorMultichannelVO;
import com.wormhole.hotelds.plugin.model.vo.HotelCompetitorVO;
import com.wormhole.hotelds.plugin.repository.WpHotelCompetitorRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 酒店竞品管理服务
 *
 * <AUTHOR>
 * @date 2025/5/19 16:40
 */
@Service
@Slf4j
public class HotelCompetitorService {

    @Resource
    private WpHotelCompetitorRepository wpHotelCompetitorRepository;

    @Resource
    private TransactionalOperator transactionalOperator;

    @Autowired
    private WpHotelCompetitorDao wpHotelCompetitorDao;

    @Autowired
    private PluginHotelAbbreviationService pluginHotelAbbreviationService;

    @Autowired
    private LockUtils lockUtils;

    private static final int MAX_COMPETITORS = 5;

    private static final String COMPETITOR_ADD_LOCK_PREFIX = "hotel:competitor:add:";
    private static final String COMPETITOR_BATCH_ADD_LOCK_PREFIX = "hotel:competitor:batch_add:";

    /**
     * 添加竞品酒店
     * 校验规则：
     * 1. 参数有效性校验
     * 2. 竞品酒店数量限制（最多5个）
     * 3. 重复数据校验（hotelCode + competitorHotelId + channel）
     */
    public Mono<HotelCompetitorVO> addCompetitor(HotelCompetitorDTO dto) {
        log.info("开始添加竞品酒店，参数: {}", JacksonUtils.writeValueAsString(dto));

        return validateAddCompetitorParams(dto)
                .flatMap(validDto -> HeaderUtils.getHeaderInfo()
                        .flatMap(headerInfo -> executeAddCompetitorWithLock(validDto, headerInfo))
                )
                .doOnSuccess(result -> log.info("竞品酒店添加成功，ID: {}", result.getId()))
                .doOnError(error -> log.error("竞品酒店添加失败，参数: {}, 错误: {}", dto, error.getMessage()));
    }

    /**
     * 使用分布式锁执行添加竞品酒店操作
     */
    private Mono<HotelCompetitorVO> executeAddCompetitorWithLock(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
        String lockKey = buildLockKey(headerInfo.getHotelCode(), dto.getCompetitorHotelId(), dto.getChannel());

        return lockUtils.lock(lockKey, 1000)
                .flatMap(lockAcquired -> {
                    if (!lockAcquired) {
                        return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "系统繁忙，请稍后重试"));
                    }
                    return executeAddCompetitor(dto, headerInfo)
                            .doFinally(signalType -> {
                                // 确保锁被释放
                                lockUtils.unLock(lockKey);
                                log.info("释放分布式锁: {}", lockKey);
                            });
                });
    }

    /**
     * 构建分布式锁的key
     */
    private String buildLockKey(String hotelCode, String competitorHotelId, String channel) {
        return COMPETITOR_ADD_LOCK_PREFIX + hotelCode + ":" + competitorHotelId + ":" + channel;
    }

    private String buildBatchLockKey(String hotelCode, String channel) {
        // 建议使用一个新的前缀，以和旧的锁进行区分
        return COMPETITOR_BATCH_ADD_LOCK_PREFIX + hotelCode + ":" + channel;
    }

    /**
     * 参数校验
     */
    private Mono<HotelCompetitorDTO> validateAddCompetitorParams(HotelCompetitorDTO dto) {
        if (dto == null) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "竞品酒店信息不能为空"));
        }

        if (StringUtils.isBlank(dto.getHotelCode())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店代码不能为空"));
        }

        if (StringUtils.isBlank(dto.getCompetitorHotelId())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "竞品酒店ID不能为空"));
        }

        if (StringUtils.isBlank(dto.getChannel())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "渠道信息不能为空"));
        }

        return Mono.just(dto);
    }

    /**
     * 执行添加竞品酒店的业务逻辑
     */
    private Mono<HotelCompetitorVO> executeAddCompetitor(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
        String hotelCode = dto.getHotelCode();
        String competitorHotelId = dto.getCompetitorHotelId();
        String channel = dto.getChannel();

        return checkCompetitorLimit(hotelCode)
                .then(findCompetitorByUniqueKeys(hotelCode, competitorHotelId, channel)
                        .flatMap(existing -> {
                            if (existing.getSelectedStatus() == 1) {
                                return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "该竞品酒店已存在"));
                            } else {
                                existing.setSelectedStatus(1);
                                if (dto.getViewRate() != null) {
                                    existing.setViewRate(dto.getViewRate());
                                }
                                return updateCompetitor(existing)
                                        .flatMap(updated -> getShortName(updated)
                                                .map(shortName -> convertToVO(updated, shortName)));
                            }
                        })
                        .switchIfEmpty(saveCompetitor(dto, headerInfo)
                                .flatMap(saved -> getShortName(saved)
                                        .map(shortName -> convertToVO(saved, shortName)))
                        )
                );
    }


    /**
     * 检查竞品酒店数量限制
     */
    private Mono<Void> checkCompetitorLimit(String hotelCode) {
        return wpHotelCompetitorRepository.countByHotelCodeAndAndSelectedStatusAndRowStatus(hotelCode,1, RowStatusEnum.VALID.getId())
                .flatMap(count -> {
                    if (count >= MAX_COMPETITORS) {
                        return Mono.error(new BusinessException(
                                "HOTELDS-PLUGIN-BUSINESS_ERROR",
                                String.format("最多只能添加%d个竞品酒店，当前已有%d个", MAX_COMPETITORS, count)
                        ));
                    }
                    return Mono.empty();
                });
    }

    /**
     * 检查重复的竞品酒店（按照 hotelCode + competitorHotelId + channel）
     */
    private Mono<Void> checkDuplicateCompetitor(String hotelCode, String competitorHotelId, String channel) {
        return wpHotelCompetitorRepository.existsByHotelCodeAndCompetitorHotelIdAndChannelAndRowStatus(
                        hotelCode, competitorHotelId, channel, RowStatusEnum.VALID.getId())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new BusinessException(
                                "HOTELDS-PLUGIN-BUSINESS_ERROR",
                                String.format("该竞品酒店在指定渠道已存在：酒店代码[%s]，竞品酒店ID[%s]，渠道[%s]",
                                        hotelCode, competitorHotelId, channel)
                        ));
                    }
                    return Mono.empty();
                });
    }

    /**
     * 保存竞品酒店
     */
    private Mono<HotelCompetitorEntity> saveCompetitor(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
        HotelCompetitorEntity competitor = buildHotelCompetitorEntity(headerInfo, dto);
        return wpHotelCompetitorRepository.save(competitor);
    }

    /**
     * 构建竞品酒店实体对象
     */
    private HotelCompetitorEntity buildHotelCompetitorEntity(HeaderUtils.HeaderInfo headerInfo, HotelCompetitorDTO dto) {
        HotelCompetitorEntity competitor = new HotelCompetitorEntity();

        // 设置业务字段
        competitor.setHotelCode(dto.getHotelCode());
        competitor.setCompetitorHotelId(dto.getCompetitorHotelId());
        competitor.setCompetitorHotelName(dto.getCompetitorHotelName());
        competitor.setCompetitorPageUrl(dto.getCompetitorPageUrl());
        competitor.setChannel(dto.getChannel());
        competitor.setPlatform(dto.getPlatform());

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        competitor.setCreatedBy(headerInfo.getUserId());
        competitor.setCreatedByName(headerInfo.getUsername());
        competitor.setCreatedAt(now);
        competitor.setUpdatedBy(headerInfo.getUserId());
        competitor.setUpdatedByName(headerInfo.getUsername());
        competitor.setUpdatedAt(now);
        competitor.setSelectedStatus(dto.getSelectedStatus());

        // 设置状态字段
        competitor.setRowStatus(RowStatusEnum.VALID.getId());
        competitor.setViewRate(dto.getViewRate() != null ? dto.getViewRate() : 0.0);

        return competitor;
    }

    // 转换方法
    private HotelCompetitorVO convertToVO(HotelCompetitorEntity entity, String abbreviation) {
        HotelCompetitorVO vo = new HotelCompetitorVO();
        vo.setId(entity.getId().toString());
        vo.setHotelCode(entity.getHotelCode());
        vo.setCompetitorHotelId(entity.getCompetitorHotelId());
        vo.setCompetitorHotelName(entity.getCompetitorHotelName());
        vo.setCompetitorPageUrl(entity.getCompetitorPageUrl());
        vo.setChannel(entity.getChannel());
        vo.setPlatform(entity.getPlatform());
        if (StringUtils.isNotBlank(abbreviation)) {
            vo.setCompetitorHotelAbbreviation(abbreviation);
        }
        vo.setViewRate(entity.getViewRate());
        vo.setSelectedStatus(entity.getSelectedStatus());
        return vo;
    }

//    public Mono<List<HotelCompetitorVO>> listCompetitors(String hotelCode, Integer selectAll) {
//        if (StringUtils.isBlank(hotelCode)) {
//            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店代码不能为空"));
//        }
//
//        Flux<HotelCompetitorEntity> competitorEntities;
//        if (selectAll == null || selectAll == 0) {
//            competitorEntities = wpHotelCompetitorRepository.findByHotelCodeAndSelectedStatusAndRowStatus(hotelCode, 1, RowStatusEnum.VALID.getId());
//        } else {
//            competitorEntities = wpHotelCompetitorRepository.findByHotelCodeAndRowStatus(hotelCode, RowStatusEnum.VALID.getId());
//        }
//
//        return competitorEntities.collectList()
//                .flatMap(entities -> {
//                    if (entities.isEmpty()) {
//                        return Mono.just(Collections.emptyList());
//                    }
//
//                    // 根据channel分组
//                    Map<String, List<String>> channelToExternalIdsMap = new HashMap<>();
//                    for (HotelCompetitorEntity entity : entities) {
//                        channelToExternalIdsMap.computeIfAbsent(entity.getChannel(), k -> new ArrayList<>())
//                                .add(entity.getCompetitorHotelId());
//                    }
//
//                    // 为每个channel查询简称
//                    List<Mono<List<PluginHotelAbbreviationEntity>>> abbreviationMonos = new ArrayList<>();
//                    for (Map.Entry<String, List<String>> entry : channelToExternalIdsMap.entrySet()) {
//                        String channel = entry.getKey();
//                        List<String> externalHotelIds = entry.getValue();
//                        abbreviationMonos.add(pluginHotelAbbreviationService.findByExternalHotelIdAndChannel(externalHotelIds, channel));
//                    }
//
//                    // 将所有查询结果合并
//                    return Mono.zip(abbreviationMonos, results -> {
//                        // 将简称结果转换为 Map，以便后续快速查找
//                        Map<String, String> abbreviationMap = new HashMap<>();
//                        for (Object result : results) {
//                            List<PluginHotelAbbreviationEntity> abbreviationList = (List<PluginHotelAbbreviationEntity>) result;
//                            for (PluginHotelAbbreviationEntity abbreviationEntity : abbreviationList) {
//                                abbreviationMap.put(abbreviationEntity.getExternalHotelId(), abbreviationEntity.getHotelAbbreviation());
//                            }
//                        }
//                        return entities.stream()
//                                .map(entity -> convertToVO(entity, abbreviationMap.get(entity.getCompetitorHotelId())))
//                                .collect(Collectors.toList());
//                    });
//                });
//    }

    public Mono<List<HotelCompetitorVO>> listCompetitors(String hotelCode, Integer selectAll) {
        if (StringUtils.isBlank(hotelCode)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店代码不能为空"));
        }

        Flux<HotelCompetitorEntity> competitorEntities;
        if (selectAll == null || selectAll == 0) {
            competitorEntities = wpHotelCompetitorRepository.findByHotelCodeAndSelectedStatusAndRowStatus(hotelCode, 1, RowStatusEnum.VALID.getId());
        } else {
            competitorEntities = wpHotelCompetitorRepository.findByHotelCodeAndRowStatus(hotelCode, RowStatusEnum.VALID.getId());
        }

        return competitorEntities.collectList()
                .flatMap(entities -> {
                    if (entities.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    // 根据channel分组
                    Map<String, List<String>> channelToExternalIdsMap = new HashMap<>();
                    for (HotelCompetitorEntity entity : entities) {
                        channelToExternalIdsMap.computeIfAbsent(entity.getChannel(), k -> new ArrayList<>())
                                .add(entity.getCompetitorHotelId());
                    }

                    // 为每个channel查询简称
                    List<Mono<List<PluginHotelAbbreviationEntity>>> abbreviationMonos = new ArrayList<>();
                    for (Map.Entry<String, List<String>> entry : channelToExternalIdsMap.entrySet()) {
                        String channel = entry.getKey();
                        List<String> externalHotelIds = entry.getValue();
                        abbreviationMonos.add(pluginHotelAbbreviationService.findByExternalHotelIdAndChannel(externalHotelIds, channel));
                    }

                    // 将所有查询结果合并
                    return Mono.zip(abbreviationMonos, results -> {
                        // 将简称结果转换为 Map，以便后续快速查找
                        Map<String, String> abbreviationMap = new HashMap<>();
                        for (Object result : results) {
                            List<PluginHotelAbbreviationEntity> abbreviationList = (List<PluginHotelAbbreviationEntity>) result;
                            for (PluginHotelAbbreviationEntity abbreviationEntity : abbreviationList) {
                                abbreviationMap.put(abbreviationEntity.getExternalHotelId(), abbreviationEntity.getHotelAbbreviation());
                            }
                        }

                        // --- 修改开始 ---

                        // 定义排序规则:
                        // 1. selectedStatus 降序 (1 在前, 0 在后)
                        // 2. 在 selectedStatus 相同的情况下，按 viewRate 降序 (大的在前)，并安全处理 null 值
                        Comparator<HotelCompetitorEntity> customComparator = Comparator
                                .comparing(HotelCompetitorEntity::getSelectedStatus, Comparator.reverseOrder())
                                .thenComparing(
                                        HotelCompetitorEntity::getViewRate,
                                        Comparator.nullsLast(Comparator.reverseOrder())
                                );

                        // 在映射为 VO 之前，使用上面定义的规则对列表进行排序
                        return entities.stream()
                                .sorted(customComparator) // 应用排序
                                .map(entity -> convertToVO(entity, abbreviationMap.get(entity.getCompetitorHotelId())))
                                .collect(Collectors.toList());

                        // --- 修改结束 ---
                    });
                });
    }

    public Mono<List<HotelCompetitorMultichannelVO>> listCompetitorsByChannel(String hotelCode, List<String> channels) {
        HotelCompetitorQO build = HotelCompetitorQO.builder().hotelCode(hotelCode).channels(channels).build();
        return wpHotelCompetitorDao.findList(build)
                .map(entities -> {
                    Map<String, List<HotelCompetitorEntity>> grouped = entities.stream()
                            .collect(Collectors.groupingBy(HotelCompetitorEntity::getCompetitorHotelId));

                    return grouped.values().stream()
                            .map(this::mergeCompetitorData)
                            .collect(Collectors.toList());
                });
    }

    private HotelCompetitorMultichannelVO mergeCompetitorData(List<HotelCompetitorEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return null;
        }

        HotelCompetitorEntity first = entities.get(0);
        HotelCompetitorMultichannelVO vo = new HotelCompetitorMultichannelVO();
        vo.setHotelCode(first.getHotelCode());
        vo.setCompetitorHotelId(first.getCompetitorHotelId());
        vo.setCompetitorHotelName(first.getCompetitorHotelName());

        List<String> allChannels = entities.stream()
                .map(HotelCompetitorEntity::getChannel)
                .distinct()
                .collect(Collectors.toList());
        vo.setChannels(allChannels);

        return vo;
    }

    /**
     * 删除竞品酒店（逻辑删除）
     */
    public Mono<Boolean> delete(String id) {
        log.info("开始删除竞品酒店，ID: {}", id);

        return validateDeleteParams(id)
                .flatMap(competitorId -> executeDelete(competitorId))
                .doOnSuccess(result -> log.info("竞品酒店删除成功，ID: {}", id))
                .doOnError(error -> log.error("竞品酒店删除失败，ID: {}, 错误: {}", id, error.getMessage()));
    }

    /**
     * 删除参数校验
     */
    private Mono<Long> validateDeleteParams(String id) {
        if (StringUtils.isBlank(id)) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "竞品酒店ID不能为空"));
        }

        try {
            return Mono.just(Long.valueOf(id));
        } catch (NumberFormatException e) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "竞品酒店ID格式不正确"));
        }
    }

    /**
     * 执行删除操作
     */
    private Mono<Boolean> executeDelete(Long competitorId) {
        return wpHotelCompetitorRepository.findById(competitorId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                        String.format("竞品酒店不存在，ID: %d", competitorId))))
                .flatMap(entity -> {
                    if (Objects.equals(RowStatusEnum.DELETE.getId(), entity.getRowStatus())) {
                        return Mono.error(new BusinessException("HOTELDS-PLUGIN-BUSINESS_ERROR",
                                String.format("竞品酒店已被删除，ID: %d", competitorId)));
                    }

                    entity.setRowStatus(RowStatusEnum.DELETE.getId());
                    entity.setUpdatedAt(LocalDateTime.now());

                    return wpHotelCompetitorRepository.save(entity);
                })
                .thenReturn(true);
    }

    /**
     * 执行取消操作
     */
    private Mono<Boolean> executeCancel(Long competitorId) {
        return wpHotelCompetitorRepository.findById(competitorId)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,
                        String.format("竞品酒店不存在，ID: %d", competitorId))))
                .flatMap(entity -> {
                    if (Objects.equals(0, entity.getSelectedStatus())) {
                        return Mono.just(true);
                    }
                    entity.setSelectedStatus(0);
                    entity.setUpdatedAt(LocalDateTime.now());

                    return wpHotelCompetitorRepository.save(entity);
                })
                .thenReturn(true);
    }


//    public Mono<List<HotelCompetitorVO>> batchAddCompetitor(List<HotelCompetitorDTO> hotelCompetitorDTOList) {
//        log.info("开始批量添加竞品酒店，参数: {}", JacksonUtils.writeValueAsString(hotelCompetitorDTOList));
//        return HeaderUtils.getHeaderInfo()
//                .flatMap(headerInfo -> Flux.fromIterable(hotelCompetitorDTOList)
//                        .flatMap(dto -> {
//                            String lockKey = buildLockKey(headerInfo.getHotelCode(), dto.getCompetitorHotelId(), dto.getChannel());
//                            return lockUtils.lock(lockKey, 1000)
//                                    .flatMap(lockAcquired -> {
//                                        if (!lockAcquired) {
//                                            return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "系统繁忙，请稍后重试"));
//                                        }
//                                        return processBatchCompetitor(dto, headerInfo)
//                                                .doFinally(signal -> {
//                                                    lockUtils.unLock(lockKey);
//                                                    log.info("释放批量添加分布式锁: {}", lockKey);
//                                                });
//                                    });
//                        })
//                        .collectList() // 将 Flux<HotelCompetitorVO> 转为 Mono<List<HotelCompetitorVO>>
//                )
//                .doOnSuccess(result -> log.info("批量添加竞品酒店成功，共计: {}", result.size()))
//                .doOnError(error -> log.error("批量添加竞品酒店失败，错误: {}", error.getMessage()));
//    }

    public Mono<List<HotelCompetitorVO>> batchAddCompetitor(List<HotelCompetitorDTO> hotelCompetitorDTOList) {
        log.info("开始批量添加竞品酒店，参数: {}", JacksonUtils.writeValueAsString(hotelCompetitorDTOList));
        if (hotelCompetitorDTOList == null || hotelCompetitorDTOList.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 假设一个批次内的所有 DTO 都有相同的 hotelCode 和 channel
        String hotelCode = hotelCompetitorDTOList.get(0).getHotelCode();
        String channel = hotelCompetitorDTOList.get(0).getChannel();

        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 为整个批量操作获取一个锁，而不是为单个 DTO
                    String batchLockKey = buildBatchLockKey(hotelCode, channel); // 需要一个新的方法来构建批处理锁的 key
                    return lockUtils.lock(batchLockKey, 5000) // 为批量操作预留更长的锁定时间
                            .flatMap(lockAcquired -> {
                                if (!lockAcquired) {
                                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "系统繁忙，请稍后重试"));
                                }

                                // 1. 在所有操作开始前，只执行一次删除
                                return deleteByHotelCode(hotelCode)
                                        // 2. 删除完成后，再处理并保存所有新的竞品
                                        .thenMany(Flux.fromIterable(hotelCompetitorDTOList)
                                                .concatMap(dto -> saveAndConvert(dto, headerInfo)) // 使用 concatMap 保证顺序处理
                                        )
                                        .collectList()
                                        .doFinally(signal -> {
                                            lockUtils.unLock(batchLockKey);
                                            log.info("释放批量添加分布式锁: {}", batchLockKey);
                                        });
                            });
                })
                .doOnSuccess(result -> log.info("批量添加竞品酒店成功，共计: {}", result.size()))
                .doOnError(error -> log.error("批量添加竞品酒店失败，错误: {}", error.getMessage()));
    }

    // 辅助方法，封装保存和转换逻辑
    private Mono<HotelCompetitorVO> saveAndConvert(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
        return saveCompetitor(dto, headerInfo)
                .flatMap(savedEntity -> getShortName(savedEntity)
                        .map(shortName -> convertToVO(savedEntity, shortName)));
    }

// Repository 层需要提供一个返回 Mono<Void> 或 Mono<Long> (删除的数量) 的删除方法
// private Mono<Void> deleteByHotelCode(String hotelCode);

    private Mono<HotelCompetitorVO> processBatchCompetitor(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
        //直接清空数据库数据
        //批量保存
        return deleteByHotelCode(dto.getHotelCode()).flatMap(deleted -> {
            if (!deleted) {
                return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除竞品酒店失败"));
            }
            return saveCompetitor(dto, headerInfo)
                    .flatMap(saved -> getShortName(saved)
                            .map(shortName -> convertToVO(saved, shortName)));
        });
    }

    private Mono<Boolean> deleteByHotelCode(String hotelCode) {
        return wpHotelCompetitorDao.deleteByHotelCode(hotelCode);
    }

//    private Mono<HotelCompetitorVO> processBatchCompetitor(HotelCompetitorDTO dto, HeaderUtils.HeaderInfo headerInfo) {
//        String hotelCode = dto.getHotelCode();
//        String competitorHotelId = dto.getCompetitorHotelId();
//        String channel = dto.getChannel();
//        Integer incomingStatus = dto.getSelectedStatus();
//
//        return findCompetitorByUniqueKeys(hotelCode, competitorHotelId, channel)
//                .flatMap(existing -> {
//                    if (existing.getSelectedStatus() == 0 && incomingStatus == 1) {
//                        existing.setSelectedStatus(1);
//                        return updateCompetitor(existing)
//                                .flatMap(updated -> getShortName(updated)
//                                        .map(shortName -> convertToVO(updated, shortName)));
//                    } else {
//                        // 返回现有记录的简称
//                        return getShortName(existing)
//                                .map(shortName -> convertToVO(existing, shortName));
//                    }
//                })
//                .switchIfEmpty(saveCompetitor(dto, headerInfo)
//                        .flatMap(saved -> getShortName(saved)
//                                .map(shortName -> convertToVO(saved, shortName)))
//                );
//    }

    private Mono<String> getShortName(HotelCompetitorEntity competitor) {
        PluginHotelAbbreviationDTO dto = new PluginHotelAbbreviationDTO();
        dto.setExternalHotelId(competitor.getCompetitorHotelId());
        dto.setPlatform(competitor.getPlatform());
        dto.setChannel(competitor.getChannel());
        dto.setHotelName(competitor.getCompetitorHotelName());

        return pluginHotelAbbreviationService.generateHotelAbbreviationName(dto);
    }



    private Mono<HotelCompetitorEntity> findCompetitorByUniqueKeys(String hotelCode, String competitorHotelId, String channel) {
        return wpHotelCompetitorRepository.findByHotelCodeAndCompetitorHotelIdAndChannelAndRowStatus(hotelCode, competitorHotelId, channel,RowStatusEnum.VALID.getId());
    }

    private Mono<HotelCompetitorEntity> updateCompetitor(HotelCompetitorEntity entity) {
        entity.setUpdatedAt(LocalDateTime.now());
        return wpHotelCompetitorRepository.save(entity);
    }


    public Mono<Boolean> cancel(String id) {
        log.info("开始取消勾选竞品酒店，ID: {}", id);

        return validateDeleteParams(id)
                .flatMap(competitorId -> executeCancel(competitorId))
                .doOnSuccess(result -> log.info("竞品酒店取消勾选成功，ID: {}", id))
                .doOnError(error -> log.error("竞品酒店取消勾选失败，ID: {}, 错误: {}", id, error.getMessage()));
    }
}
