package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelPriceDeleteQO implements Serializable {
    private String hotelCode;
    private String selfHotelCode;
    private List<String> bizIds;
}