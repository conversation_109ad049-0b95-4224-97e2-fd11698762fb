package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import com.wormhole.hotelds.plugin.model.enums.HotelCompetitorEntityFieldEnum;
import com.wormhole.hotelds.plugin.model.qo.HotelCompetitorQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/27
 */
@Service
@Slf4j
public class WpHotelCompetitorDao {

    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;



    public Mono<HotelCompetitorEntity> findOne(HotelCompetitorQO hotelCompetitorQO){
        Criteria criteria = getCriteria(hotelCompetitorQO);
        return r2dbcEntityTemplate.selectOne(Query.query(criteria).limit(1),HotelCompetitorEntity.class);
    }

    public Mono<List<HotelCompetitorEntity>> findList(HotelCompetitorQO hotelCompetitorQO){
        Criteria criteria = getCriteria(hotelCompetitorQO);
        return r2dbcEntityTemplate.select(Query.query(criteria),HotelCompetitorEntity.class).collectList();
    }

    public Criteria getCriteria(HotelCompetitorQO hotelCompetitorQO){
        Criteria criteria = Criteria.empty();
        if (Objects.nonNull(hotelCompetitorQO.getHotelCode())) {
            criteria = criteria.and(Criteria.where(HotelCompetitorEntityFieldEnum.hotel_code.name()).is(hotelCompetitorQO.getHotelCode()));
        }
        if(CollUtil.isNotEmpty(hotelCompetitorQO.getChannels())){
            criteria = criteria.and(Criteria.where(HotelCompetitorEntityFieldEnum.channel.name()).in(hotelCompetitorQO.getChannels()));
        }
        if(StringUtils.isNotBlank(hotelCompetitorQO.getCompetitorHotelId())) {
            criteria = criteria.and(HotelCompetitorEntityFieldEnum.competitor_hotel_id.name()).in(hotelCompetitorQO.getCompetitorHotelId());
        }
        if(Objects.nonNull(hotelCompetitorQO.getSelectedStatus())) {
            criteria = criteria.and(HotelCompetitorEntityFieldEnum.selected_status.name()).is(hotelCompetitorQO.getSelectedStatus());
        }


        criteria  = criteria.and(HotelCompetitorEntityFieldEnum.row_status.name()).is(RowStatusEnum.VALID.getId());
        return criteria;
    }

    public Mono<Boolean> deleteByHotelCode(String hotelCode){
        Criteria criteria = Criteria.where(HotelCompetitorEntityFieldEnum.hotel_code.name()).is(hotelCode);
        Query query = Query.query(criteria);
        return r2dbcEntityTemplate.delete(query, HotelCompetitorEntity.class)
                .thenReturn(Boolean.TRUE);
    }
}
