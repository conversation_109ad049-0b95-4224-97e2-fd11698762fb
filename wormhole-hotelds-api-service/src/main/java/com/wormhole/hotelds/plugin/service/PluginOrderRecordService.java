package com.wormhole.hotelds.plugin.service;


import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderFormDateDTO;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderRecordDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginOrderRecordEntity;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTimeVO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PluginOrderRecordService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private PluginOrderIndexService pluginOrderIndexService;

//    public Mono<PluginOrderRecordEntity> record(PluginOrderRecordDTO pluginOrderRecordDTO) {
//        // 构建查询条件
//        Criteria criteria = Criteria.where("hotel_code").is(pluginOrderRecordDTO.getHotelCode())
//                .and("platform").is(pluginOrderRecordDTO.getPlatform())
//                .and("source_type").is(pluginOrderRecordDTO.getSourceType());
//        // 查询数据库
//        return r2dbcEntityTemplate.select(Query.query(criteria), PluginOrderRecordEntity.class).next()
//                .flatMap(entity -> updateRecord(entity,pluginOrderRecordDTO)) // 如果存在, 更新逻辑
//                .switchIfEmpty(createNewRecord(pluginOrderRecordDTO)); // 如果不存在, 创建新记录
//    }
//
//    private Mono<PluginOrderRecordEntity> updateRecord(PluginOrderRecordEntity entity,PluginOrderRecordDTO pluginOrderRecordDTO) {
//        if (pluginOrderRecordDTO.getIsEnd() != null  && pluginOrderRecordDTO.getIsEnd()) {
//            PluginOrderFormDateDTO formDateDTO = new PluginOrderFormDateDTO();
//            formDateDTO.setHotelCode(pluginOrderRecordDTO.getHotelCode());
//            formDateDTO.setPlatform(pluginOrderRecordDTO.getPlatform());//如果 isEnd 为 true, 则更新状态，以及fromDate
//            return pluginOrderIndexService.getFromDate(formDateDTO)
//                    .flatMap(
//                            pluginOrderTimeVO -> {
//                                pluginOrderTimeVO.getFormDateList().stream().filter(
//                                        formDate -> formDate.getSourceType().equals(pluginOrderRecordDTO.getSourceType())
//                                ).findFirst().ifPresent(
//                                        formDate -> entity.setFormDate(formDate.getFormDate())
//                                );
//                            }
//                    ).flatMap(a -> {
//                        entity.setIsEnd(pluginOrderRecordDTO.getIsEnd());
//                        r2dbcEntityTemplate.update(entity);
//                    }).thenReturn(entity); // 更新记录并返回
//
//        }
//    }
//
//    // 处理插入新记录的逻辑
//    private Mono<PluginOrderRecordEntity> createNewRecord(PluginOrderRecordDTO pluginOrderRecordDTO) {
//        PluginOrderRecordEntity newEntity = new PluginOrderRecordEntity();
//        newEntity.setSourceType(pluginOrderRecordDTO.getSourceType());
//        newEntity.setHotelCode(pluginOrderRecordDTO.getHotelCode());
//        newEntity.setPlatform(pluginOrderRecordDTO.getPlatform());
//        newEntity.setIsEnd(pluginOrderRecordDTO.getIsEnd());
//        PluginOrderFormDateDTO formDateDTO = new PluginOrderFormDateDTO();
//        formDateDTO.setHotelCode(pluginOrderRecordDTO.getHotelCode());
//        formDateDTO.setPlatform(pluginOrderRecordDTO.getPlatform());
//        return pluginOrderIndexService.getFromDate(formDateDTO)
//                .flatMap(
//                pluginOrderTimeVO -> {
//                    pluginOrderTimeVO.getFormDateList().stream().filter(
//                            formDate -> formDate.getSourceType().equals(pluginOrderRecordDTO.getSourceType())
//                    ).findFirst().ifPresent(
//                            formDate -> newEntity.setFormDate(formDate.getFormDate())
//                    );
//                }).flatMap(
//                        unused -> {
//                            // 插入新记录
//                            r2dbcEntityTemplate.insert(newEntity);
//                        }
//                );
//
//
//    }

    /**
     * 记录或更新插件订单记录。
     * - 如果记录已存在，则根据条件更新它。
     * - 如果记录不存在，则创建一条新记录。
     * - 无论是更新还是创建，当 isEnd=true 或为新记录时，都会重新计算并设置 formDate。
     *
     * @param pluginOrderRecordDTO 包含订单记录信息的 DTO
     * @return 操作完成后的实体 Mono
     */

    // 在 PluginOrderRecordService.java 中

    /**
     * 根据酒店和平台查询所有插件订单记录
     *
     * @param hotelCode 酒店编码
     * @param platform 平台
     * @return 包含相关记录的 Flux
     */
    public Flux<PluginOrderRecordEntity> findRecordsByHotelAndPlatform(String hotelCode, String platform) {
        // 校验输入参数
        if (StringUtils.isBlank(hotelCode) || StringUtils.isBlank(platform)) {
            return Flux.error(new BusinessException(ResultCode.INVALID_PARAMETER, "酒店编码和平台不能为空"));
        }

        // 构建查询条件
        Criteria criteria = Criteria.where("hotel_code").is(hotelCode).and("platform").is(platform);

        // 查询并返回所有匹配的记录流
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginOrderRecordEntity.class);
    }
    public Mono<PluginOrderRecordEntity> record(PluginOrderRecordDTO pluginOrderRecordDTO) {
        // 1. 构建查询条件，定位唯一的记录
        Criteria criteria = Criteria.where("hotel_code").is(pluginOrderRecordDTO.getHotelCode())
                .and("platform").is(pluginOrderRecordDTO.getPlatform())
                .and("source_type").is(pluginOrderRecordDTO.getSourceType());

        // 2. 查找现有记录，如果找不到则准备一个新实例
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginOrderRecordEntity.class)
                .next()
                .switchIfEmpty(Mono.defer(() -> {
                    // 如果数据库中没有记录，则在内存中创建一个新的实体对象
                    PluginOrderRecordEntity newEntity = new PluginOrderRecordEntity();
                    newEntity.setSourceType(pluginOrderRecordDTO.getSourceType());
                    newEntity.setHotelCode(pluginOrderRecordDTO.getHotelCode());
                    newEntity.setPlatform(pluginOrderRecordDTO.getPlatform());
                    return Mono.just(newEntity);
                }))
                .flatMap(entity -> {
                    // 3. 更新 isEnd 状态
                    entity.setIsEnd(pluginOrderRecordDTO.getIsEnd());

                    // 4. 判断是否需要更新 formDate
                    boolean needsDateUpdate = (pluginOrderRecordDTO.getIsEnd() != null && pluginOrderRecordDTO.getIsEnd()) || entity.getId() == null;

                    if (needsDateUpdate) {
                        // 如果需要更新日期，则调用索引服务
                        PluginOrderQo params = new PluginOrderQo();
                        params.setHotelCode(pluginOrderRecordDTO.getHotelCode());
                        params.setPlatform(pluginOrderRecordDTO.getPlatform());

                        return pluginOrderIndexService.fetchLatestOrderStatsBySourceType(params)
                                .map(latestOrderDTO -> {
                                    // 从服务返回的结果中查找匹配的项
                                    latestOrderDTO.getItems().stream()
                                            .filter(item -> item.getSourceType().equals(pluginOrderRecordDTO.getSourceType()))
                                            .findFirst()
                                            .ifPresent(item -> {
                                                // 如果找到，就更新实体上的日期
                                                entity.setFormDate(item.getFormDate());
                                            });
                                    // 关键：返回被修改后的 entity，以继续流的执行
                                    return entity;
                                })
                                // 添加一个回退机制：如果服务返回的是一个空的 Mono，则使用原始的 entity 继续
                                .defaultIfEmpty(entity);
                    }

                    // 如果不需要更新日期，直接将实体传递到下一步
                    return Mono.just(entity);
                })
                .flatMap(entity -> {
                    // 5. 持久化到数据库
                    // 如果实体没有 ID，说明是新创建的，执行插入操作
                    if (entity.getId() == null) {
                        return r2dbcEntityTemplate.insert(entity);
                    } else {
                        // 如果实体有 ID，说明是已存在的，执行更新操作
                        return r2dbcEntityTemplate.update(entity).thenReturn(entity);
                    }
                });
    }

    public Mono<Map<String, String>> getRecord(PluginOrderFormDateDTO formDateDTO) {
        // 构建查询条件
        Criteria criteria = Criteria.where("hotel_code").is(formDateDTO.getHotelCode())
                .and("platform").is(formDateDTO.getPlatform());

        // 查询数据库
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginOrderRecordEntity.class)
                .collectList() // Collect results into a list
                .flatMap(entities -> {
                    if (entities.isEmpty()) {
                        return Mono.empty(); // 如果没有记录, 返回空
                    }
                    // Collect source types into a map
                    Map<String, String> resultMap = entities.stream()
                            .collect(Collectors.toMap(PluginOrderRecordEntity::getSourceType, PluginOrderRecordEntity::getSourceType));

                    return Mono.just(resultMap); // Return the map wrapped in a Mono
                });
    }
}
