package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.util.NamedValue;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.hotelds.plugin.model.param.PluginSimpleCommentSearchParams;
import com.wormhole.hotelds.plugin.model.qo.SentimentLabelsQO;
import com.wormhole.task.model.entity.filed.PluginSimpleCommentIndexField;
import com.wormhole.task.model.entity.index.PluginCommentIndex;
import com.wormhole.task.model.entity.index.PluginSimpleCommentIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.*;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.data.elasticsearch.core.SearchHit;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.*;

@Service
@Slf4j
public class PluginSimpleCommentIndexService {
    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;




    public Mono<List<Pair<String, Long>>> getLabelsWithSentiment(SentimentLabelsQO sentimentLabelsQO, String sentiment) {
        Map<String, Aggregation> aggregationsMap = new HashMap<>();

        // 创建嵌套路径聚合 - label_analysis
        Aggregation nestedLabelAnalysisAgg = Aggregation.of(nestedBuilder -> nestedBuilder
                .nested(n -> n.path("label_analysis"))
                .aggregations("sentiment_filter", filterAgg -> filterAgg
                        .filter(f -> f.term(t -> t.field("label_analysis.sentiment").value(sentiment)))
                        .aggregations("nested_tags", nestedTagsAgg -> nestedTagsAgg
                                .nested(n -> n.path("label_analysis.labels"))
                                .aggregations("level_2_filter", levelFilterAgg -> levelFilterAgg
                                        .filter(f -> f.term(t -> t.field("label_analysis.labels.level").value(4)))
                                        .aggregations("tag_names", tagNamesAgg -> tagNamesAgg
                                                .terms(t -> t
                                                        .field("label_analysis.labels.name")
                                                        .order(List.of(NamedValue.of("_count", SortOrder.Desc)))
                                                )
                                        )
                                )
                        )
                )
        );

        // 添加聚合到主Map
        aggregationsMap.put("level_2_tags", nestedLabelAnalysisAgg);

        BoolQuery.Builder conditionBoolQueryBuilder = new BoolQuery.Builder();
        buildContentQuery(conditionBoolQueryBuilder, sentimentLabelsQO);
        Query conditionQuery = conditionBoolQueryBuilder.build()._toQuery();
        // 构建查询
        NativeQueryBuilder queryBuilder = NativeQuery.builder()
                .withQuery(conditionQuery)
                .withMaxResults(0); // size: 100

        // 添加所有聚合
        for (Map.Entry<String, Aggregation> entry : aggregationsMap.entrySet()) {
            queryBuilder.withAggregation(entry.getKey(), entry.getValue());
        }

        NativeQuery nativeQuery = queryBuilder.build();

        // 执行搜索
        return reactiveElasticsearchOperations.searchForHits(
                        nativeQuery,
                        PluginSimpleCommentIndex.class)
                .map(searchHits -> {
                    AggregationsContainer<?> aggregations = searchHits.getAggregations();
                    return getLevel3TagsDistribution(aggregations);
                });
    }

    private List<Pair<String, Long>> getLevel3TagsDistribution(AggregationsContainer<?> aggregations) {
        List<Pair<String, Long>> resultList = new ArrayList<>();

        if (aggregations != null) {
            try {
                // 获取level_2_tags嵌套聚合
                ElasticsearchAggregation level2TagsAgg = ((ElasticsearchAggregations) aggregations).get("level_2_tags");
                if (level2TagsAgg != null) {
                    // 获取level_2_tags嵌套聚合内的子聚合
                    Map<String, Aggregate> level2TagsAggregates = level2TagsAgg.aggregation()
                            .getAggregate().nested().aggregations();

                    // 获取sentiment_filter聚合
                    Aggregate sentimentFilterAgg = level2TagsAggregates.get("sentiment_filter");
                    if (sentimentFilterAgg != null) {
                        // 获取sentiment_filter聚合内的子聚合
                        Map<String, Aggregate> sentimentFilterAggregates = sentimentFilterAgg.filter().aggregations();

                        // 获取nested_tags嵌套聚合
                        Aggregate nestedTagsAgg = sentimentFilterAggregates.get("nested_tags");
                        if (nestedTagsAgg != null) {
                            // 获取nested_tags嵌套聚合内的子聚合
                            Map<String, Aggregate> nestedTagsAggregates = nestedTagsAgg.nested().aggregations();

                            // 获取level_2_filter过滤聚合
                            Aggregate level2FilterAgg = nestedTagsAggregates.get("level_2_filter");
                            if (level2FilterAgg != null) {
                                // 获取level_2_filter过滤聚合内的子聚合
                                Map<String, Aggregate> level2FilterAggregates = level2FilterAgg.filter().aggregations();

                                // 获取tag_names词条聚合
                                Aggregate tagNamesAgg = level2FilterAggregates.get("tag_names");
                                if (tagNamesAgg != null) {
                                    // 处理tag_names词条聚合结果
                                    var buckets = tagNamesAgg.sterms().buckets().array();
                                    for (var bucket : buckets) {
                                        resultList.add(Pair.of(bucket.key().stringValue(), bucket.docCount()));
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理level3标签聚合结果时出错: {}", e.getMessage());
            }
        }

        return resultList;
    }

    private void buildContentQuery(BoolQuery.Builder boolQueryBuilder, SentimentLabelsQO sentimentLabelsQO) {
        if (Objects.nonNull(sentimentLabelsQO.getStartDateTime()) && Objects.nonNull(sentimentLabelsQO.getEndDateTime())) {
            boolQueryBuilder.filter(Querys.range("comment_date", sentimentLabelsQO.getStartDateTime(), sentimentLabelsQO.getEndDateTime()));
        }

        if(Objects.nonNull(sentimentLabelsQO.getChannels())) {
            boolQueryBuilder.filter(Querys.terms("comment_channel", sentimentLabelsQO.getChannels()));
        }

        if (StrUtil.isNotEmpty(sentimentLabelsQO.getHotelCode())) {
            boolQueryBuilder.filter(Querys.term("hotel_code", sentimentLabelsQO.getHotelCode()));
        }
    }
}
