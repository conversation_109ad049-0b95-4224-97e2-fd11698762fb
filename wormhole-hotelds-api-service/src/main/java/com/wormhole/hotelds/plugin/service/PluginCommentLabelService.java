package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.repository.PluginCommentTagRepository;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class PluginCommentLabelService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private PluginCommentTagRepository pluginCommentLabelRepository;
    public Mono<PluginCommentLabelEntity> saveLabel(PluginCommentLabelEntity entity) {
        return pluginCommentLabelRepository.save(entity);
    }
}
