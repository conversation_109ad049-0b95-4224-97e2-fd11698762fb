package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.wormhole.agent.knowledge.model.dto.PluginHotelDTO;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.util.LockUtils;
import com.wormhole.hotelds.plugin.config.CommonProperties;
import com.wormhole.hotelds.plugin.dao.HdsHotelMappingDao;
import com.wormhole.hotelds.plugin.dao.HotelCommentRedisDao;
import com.wormhole.hotelds.plugin.dao.HotelCommentScoreDao;
import com.wormhole.hotelds.plugin.dao.HotelSimpleCommentDao;
import com.wormhole.hotelds.plugin.model.dto.HotelCommentDTO;
import com.wormhole.hotelds.plugin.model.dto.HotelCommentReviewScoreDTO;
import com.wormhole.hotelds.plugin.model.dto.ScanTimeWindowDTO;
import com.wormhole.hotelds.plugin.model.dto.TaskDTO;
import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import com.wormhole.hotelds.plugin.model.entity.HotelCommentScore;
import com.wormhole.hotelds.plugin.model.enums.ScoreCategoryEnum;
import com.wormhole.hotelds.plugin.model.vo.HotelCommentVO;
import com.wormhole.hotelds.plugin.model.vo.HotelCompetitorVO;
import com.wormhole.hotelds.plugin.model.vo.TaskVO;
import com.wormhole.hotelds.plugin.model.qo.HotelCommentScoreQO;
import com.wormhole.hotelds.plugin.model.qo.SimpleCommentQO;
import com.wormhole.hotelds.plugin.repository.TaskScheduleDetailRepository;
import com.wormhole.hotelds.plugin.repository.WpHotelCompetitorRepository;
import com.wormhole.task.model.constant.HotelDataSourceEnum;
import com.wormhole.task.model.constant.TaskScheduleJobStatusEnum;
import com.wormhole.task.model.constant.TaskScheduleJobTypeEnum;
import com.wormhole.task.model.dto.*;
import com.wormhole.task.model.entity.SimpleCommentEntity;
import com.wormhole.task.model.entity.TaskScheduleDetailEntity;
import com.wormhole.task.model.entity.filed.TaskScheduleDetailField;
import com.wormhole.task.model.entity.filed.*;
import com.wormhole.task.model.util.StringConvertUtils;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import org.springframework.data.domain.Sort;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMATTER;
import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMATTER;
import static com.wormhole.hotelds.api.hotel.constant.RedisConstant.HOTEL_COMMENT_SYNC_LOCK;

@Service
@Slf4j
public class PluginTaskService {

    @Resource
    private TaskScheduleDetailRepository taskScheduleDetailRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private HotelCommentRedisDao hotelCommentRedisDao;

    @Resource
    private HotelSimpleCommentDao hotelSimpleCommentDao;

    @Resource
    private WpHotelCompetitorRepository hotelCompetitorRepository;

    @Autowired
    private LockUtils lockUtils;


    @Autowired
    private HotelCommentScoreDao hotelCommentScoreDao;
    @Autowired
    private HotelCompetitorService hotelCompetitorService;
    @Autowired
    private HdsHotelMappingDao hotelMappingDao;

    public Flux<TaskVO> getLastTask(TaskDTO taskDTO) {
        return Flux.defer(() -> {
            List<String> jobTypes = getJobTypes(taskDTO);
            String hotelCode = taskDTO.getHotelCode();

            return buildBizIdHotelCodeMap(hotelCode)
                    .flatMapMany(bizIdToHotelCodeMap -> {
                        Query query = buildQuery(bizIdToHotelCodeMap.keySet(), jobTypes);
                        Query resultQuery = query.columns(TaskScheduleDetailField.id.name(), TaskScheduleDetailField.job_type.name(),
                                TaskScheduleDetailField.job_status.name(), TaskScheduleDetailField.updated_at.name(),
                                TaskScheduleDetailField.biz_id.name());
                        return r2dbcEntityTemplate.select(resultQuery, TaskScheduleDetailEntity.class)
                                .collectSortedList(Comparator.comparing(TaskScheduleDetailEntity::getId).reversed())
                                .flatMapMany(sortedList -> groupAndMapToTaskVO(sortedList, bizIdToHotelCodeMap));
                    });
        });
    }

    private List<String> getJobTypes(TaskDTO taskDTO) {
        return CollectionUtils.isEmpty(taskDTO.getJobType()) ?
                List.of(TaskScheduleJobTypeEnum.ctrip_hotel_fetch_save.getJobType(),
                        TaskScheduleJobTypeEnum.ebk_comment_batch_save.getJobType())
                : taskDTO.getJobType();
    }

    private Mono<Map<Long, String>> buildBizIdHotelCodeMap(String hotelCode) {
        Long selfBizId = StringConvertUtils.stringToLongHash(hotelCode, null);

        return hotelCompetitorRepository.findByHotelCodeAndRowStatus(hotelCode, RowStatusEnum.VALID.getId())
                .map(HotelCompetitorEntity::getCompetitorHotelId)
                .collectMap(
                        competitorId -> StringConvertUtils.stringToLongHash(competitorId, null),
                        Function.identity()
                )
                .map(competitorMap -> {
                    competitorMap.put(selfBizId, hotelCode);
                    return competitorMap;
                });
    }

    public static void main(String[] args) {
        System.out.println(StringConvertUtils.stringToLongHash("1649455", null));
        System.out.println(StringConvertUtils.stringToLongHash("6469336", null));
        System.out.println(StringConvertUtils.stringToLongHash("70988469", null));
        System.out.println(StringConvertUtils.stringToLongHash("661081", null));
        System.out.println(StringConvertUtils.stringToLongHash("602653", null));
    }

    private Query buildQuery(Set<Long> bizIds, List<String> jobTypes) {
        Criteria criteria = Criteria.empty()
                .and(TaskScheduleDetailField.job_type.name()).in(jobTypes)
                .and(TaskScheduleDetailField.biz_id.name()).in(bizIds);

        return Query.query(criteria)
                .sort(Sort.by(Sort.Order.desc(TaskScheduleDetailField.id.name())));
    }

    private Flux<TaskVO> groupAndMapToTaskVO(List<TaskScheduleDetailEntity> list, Map<Long, String> bizIdToHotelCodeMap) {
        Map<String, TaskScheduleDetailEntity> groupedMap = new LinkedHashMap<>();
        for (TaskScheduleDetailEntity task : list) {
            String key = task.getBizId() + "_" + task.getJobType();
            groupedMap.putIfAbsent(key, task);
        }

        return Flux.fromIterable(groupedMap.values())
                .map(entity -> {
                    TaskVO vo = new TaskVO();
                    vo.setTaskId(String.valueOf(entity.getId()));
                    vo.setJobType(entity.getJobType());
                    vo.setJobStatus(entity.getJobStatus());
                    vo.setUpdateTime(entity.getUpdatedAt());
                    vo.setRemind(remind(entity.getUpdatedAt()));
                    vo.setBizId(String.valueOf(entity.getBizId()));
                    vo.setJobName(entity.getJobName());
                    vo.setHotelCode(bizIdToHotelCodeMap.get(entity.getBizId()));
                    return vo;
                });
    }

    private Integer remind(LocalDateTime updatedAt) {
        LocalDateTime remindTime = updatedAt.plusDays(commonProperties.getTimeInterval());
        if (remindTime.isBefore(LocalDateTime.now())) {
            return 1;
        } else {
            return 0;
        }
    }

    private <T> Mono<TaskScheduleDetailVO> syncTask(HeaderUtils.HeaderInfo headerInfo, T dto, Function<T, Long> bizIdGenerator, Function<T, String> jobTypeGenerator) {
        long bizId = bizIdGenerator.apply(dto);
        String jobType = jobTypeGenerator.apply(dto);

        return this.findByBizCode(bizId, jobType)
                .flatMap(taskScheduleDetailVO -> {
                    if (DigestUtils.md5Hex(JacksonUtils.writeValueAsString(dto)).equals(DigestUtils.md5Hex(taskScheduleDetailVO.getJobInput()))) {
                        //把更新时间更新为当前时间
                        return updateUpdateTime(taskScheduleDetailVO, headerInfo);
                    }
                    if (TaskScheduleJobStatusEnum.running.getJobStatus().equals(taskScheduleDetailVO.getJobStatus())) {
                        return Mono.just(taskScheduleDetailVO);
                    }
                    return updateExistingTask(taskScheduleDetailVO.getId(), dto, headerInfo);
                })
                .switchIfEmpty(createNewTask(dto, headerInfo));
    }

    private Mono<TaskScheduleDetailVO> updateUpdateTime(TaskScheduleDetailVO taskScheduleDetailVO, HeaderUtils.HeaderInfo headerInfo) {
        TaskScheduleDetailEntity taskScheduleDetailEntity = new TaskScheduleDetailEntity();
        BeanUtils.copyProperties(taskScheduleDetailVO, taskScheduleDetailEntity);
        taskScheduleDetailEntity.setUpdatedAt(LocalDateTime.now());
        taskScheduleDetailEntity.setUpdatedBy(headerInfo.getUserId());
        taskScheduleDetailEntity.setUpdatedByName(headerInfo.getUsername());
        return taskScheduleDetailRepository.save(taskScheduleDetailEntity).map(this::toVo);

    }

    public Mono<TaskScheduleDetailVO> hotelInfoSync(PluginHotelDTO hotelDTO) {
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> syncTask(
                headerInfo,
                hotelDTO,
                dto -> StringConvertUtils.stringToLongHash(dto.getHotelCode(), dto.getHotelName()),
                dto -> buildJobType(dto.getDataSource())
        ));
    }

    public Mono<TaskScheduleDetailVO> commentBatchSync(EbkBatchCommentDTO ebkBatchCommentDTO) {
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> syncTask(
                headerInfo,
                ebkBatchCommentDTO,
                dto -> StringConvertUtils.stringToLongHash(dto.getHotelCode(), dto.getHotelName()),
                dto -> TaskScheduleJobTypeEnum.ebk_comment_batch_save.getJobType()
        ));
    }

    public Mono<TaskScheduleDetailVO> commentSingleSync(EbkSingleCommentDTO ebkSingleCommentDTO) {
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo -> syncTask(
                headerInfo,
                ebkSingleCommentDTO,
                dto -> StringConvertUtils.stringToLongHash(dto.getHotelCode(), null),
                dto -> TaskScheduleJobTypeEnum.ebk_comment_single_save.getJobType()
        ));
    }

    public Mono<TaskScheduleDetailVO> hotelPriceSync(HotelPriceDTO hotelPriceDTO) {
        if (StringUtils.isBlank(hotelPriceDTO.getHotelCode())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "hotel_code cannot be null"));
        }
        return HeaderUtils.getHeaderInfo().flatMap(headerInfo ->
                syncTask(headerInfo,
                        hotelPriceDTO,
                        dto -> StringConvertUtils.stringToLongHash(dto.getHotelCode(), dto.getHotelName()),
                        dto -> TaskScheduleJobTypeEnum.ctrip_hotel_price_fetch_save.getJobType()));
    }


    public Mono<HotelCommentVO> hotelCommentSync(HotelCommentDTO hotelCommentDTO) {
        if (hotelCommentDTO == null || StringUtils.isBlank(hotelCommentDTO.getCompetitorHotelId())
                || CollUtil.isEmpty(hotelCommentDTO.getCommentInfoV1s())  && CollUtil.isEmpty(hotelCommentDTO.getCommentInfoV2s())) {
            log.info("Invalid hotelCommentDTO: {}", JacksonUtils.writeValueAsString(hotelCommentDTO));
            return Mono.just(HotelCommentVO.builder()
                    .searchNextPage(false)
                    .build());
        }

        String lockKey = String.format(HOTEL_COMMENT_SYNC_LOCK, hotelCommentDTO.getCompetitorHotelId());

        return lockUtils.lock(lockKey, 500).flatMap(lock -> {
            if (!lock) {
                log.info("Failed to acquire lock for hotel comment sync, hotelCommentDTO: {}", JacksonUtils.writeValueAsString(hotelCommentDTO));
                return Mono.just(HotelCommentVO.builder()
                        .searchNextPage(false)
                        .build());
            }
            return handleCommentSync(hotelCommentDTO).doFinally(signalType -> lockUtils.unLock(lockKey).subscribe());
        });
    }

    public Mono<Boolean> hotelCommentReviewScoreSync(HotelCommentReviewScoreDTO hotelCommentReviewScoreDTO) {
        log.info("hotelCommentReviewScoreSync: {}", JacksonUtils.writeValueAsString(hotelCommentReviewScoreDTO));
        if (Objects.isNull(hotelCommentReviewScoreDTO.getHotelCommentReviewScoreInfo())
                || CollUtil.isEmpty(hotelCommentReviewScoreDTO.getHotelCommentReviewScoreInfo().getCategoryScore())) {
            log.info("Invalid hotelCommentReviewScoreDTO: {}", JacksonUtils.writeValueAsString(hotelCommentReviewScoreDTO));
            return Mono.just(false);
        }

        HotelCommentScore hotelCommentScore = new HotelCommentScore();
        hotelCommentScore.setCommentChannel(hotelCommentReviewScoreDTO.getChannel());
        hotelCommentScore.setHotelCode(hotelCommentReviewScoreDTO.getHotelCode());
        hotelCommentScore.setCommentPlatform(hotelCommentReviewScoreDTO.getPlatform());
        hotelCommentScore.setScoreCollectionTime(LocalDateTime.now());
        hotelCommentScore.setCreatedBy(hotelCommentReviewScoreDTO.getOperatorInfo().getOperatorId());
        hotelCommentScore.setCreatedByName(hotelCommentReviewScoreDTO.getOperatorInfo().getOperatorName());
        hotelCommentScore.setUpdatedBy(hotelCommentReviewScoreDTO.getOperatorInfo().getOperatorId());
        hotelCommentScore.setUpdatedByName(hotelCommentReviewScoreDTO.getOperatorInfo().getOperatorName());
        hotelCommentScore.setRowStatus(RowStatusEnum.VALID.getId());

        HotelCommentReviewScoreDTO.HotelCommentReviewScoreInfo hotelCommentReviewScoreInfo = hotelCommentReviewScoreDTO.getHotelCommentReviewScoreInfo();
        hotelCommentScore.setAverageScore(new BigDecimal(hotelCommentReviewScoreInfo.getScore()));
        hotelCommentScore.setTotalReviewsCount(hotelCommentReviewScoreInfo.getTotalReviews().longValue());
        hotelCommentScore.setScoreMax(hotelCommentReviewScoreInfo.getScoreMax());

        hotelCommentReviewScoreInfo.getCategoryScore().forEach(e -> {
            ScoreCategoryEnum byName = ScoreCategoryEnum.getByName(e.getScoreName());
            if (Objects.equals(byName, ScoreCategoryEnum.SERVICE)) {
                hotelCommentScore.setServiceScore(new BigDecimal(e.getItemScore()));
            } else if (Objects.equals(byName, ScoreCategoryEnum.HYGIENE)) {
                hotelCommentScore.setHygieneScore(new BigDecimal(e.getItemScore()));
            } else if (Objects.equals(byName, ScoreCategoryEnum.ENVIRONMENT)) {
                hotelCommentScore.setEnvironmentScore(new BigDecimal(e.getItemScore()));
            } else if (Objects.equals(byName, ScoreCategoryEnum.FACILITIES)) {
                hotelCommentScore.setFacilitiesScore(new BigDecimal(e.getItemScore()));
            }
        });
        HotelCommentScoreQO hotelCommentScoreQO = HotelCommentScoreQO
                .builder()
                .hotelCode(hotelCommentReviewScoreDTO.getHotelCode())
                .channel(hotelCommentReviewScoreDTO.getChannel())
                .platform(hotelCommentReviewScoreDTO.getPlatform())
                .build();

        return hotelCommentScoreDao.setInValid(hotelCommentScoreQO)
                .then(hotelCommentScoreDao.save(hotelCommentScore)).map(Objects::nonNull);
    }

    @NotNull
    private Mono<HotelCommentVO> handleCommentSync(HotelCommentDTO hotelCommentDTO) {
        log.info("handleCommentSync: {}", JacksonUtils.writeValueAsString(hotelCommentDTO));

        List<SimpleCommentEntity> simpleCommentEntity = toSimpleCommentEntity(hotelCommentDTO);
        log.info("simpleCommentEntity: {}", JacksonUtils.writeValueAsString(simpleCommentEntity));

        String hotelId = hotelCommentDTO.getCompetitorHotelId();
        log.info("开始处理酒店评论同步, hotelId: {}, 评论数量: {}, 是否最后一页: {}",
                hotelId, simpleCommentEntity.size(), hotelCommentDTO.isLastPage());



        return hotelCommentRedisDao.getScanTimeWindowDTO(hotelId)
                .defaultIfEmpty(ScanTimeWindowDTO.createInitialWindow(hotelCommentDTO.isInnerHotel()))
                .flatMap(scanTimeWindow -> {


                    Date pageFirstTime = DateUtil.parse(simpleCommentEntity.get(0).getCheckInDate(), DatePattern.NORM_DATE_PATTERN);
                    Date pageLastTime = DateUtil.parse(simpleCommentEntity.get(simpleCommentEntity.size() - 1).getCheckInDate(), DatePattern.NORM_DATE_PATTERN);

                    // 判断是否需要保存数据
                    boolean shouldSave = scanTimeWindow.shouldSavePage(pageFirstTime);
                    log.info("当前页评论时间范围, hotelId: {}, 首条评论时间: {}, 末条评论时间: {}, 扫描窗口: {},shouldSave: {}",
                            hotelId, DateUtil.formatDateTime(pageFirstTime), DateUtil.formatDateTime(pageLastTime), JacksonUtils.writeValueAsString(scanTimeWindow), shouldSave);

                    if (shouldSave) {
                        // 批量保存评论数据
                        List<String> commentIds = buildBussinessComentIds(simpleCommentEntity);
                        SimpleCommentQO simpleCommentQO = SimpleCommentQO.builder().businessIds(commentIds).externalHotelId(hotelCommentDTO.getCompetitorHotelId()).build();
                        return hotelSimpleCommentDao.findList(simpleCommentQO).flatMap(entities -> {
                            List<String> existComments = entities.stream().map(SimpleCommentEntity::getBusinessCommentId).toList();

                            List<SimpleCommentEntity> newEntities = simpleCommentEntity
                                    .stream()
                                    .filter(commentInfo -> !existComments.contains(commentInfo.getBusinessCommentId()))
                                    .toList();
                            // 如果没有新评论需要保存，则直接返回结果
                            if (newEntities.isEmpty()) {
                                log.info("无新评论需要保存，直接检查是否需要翻页, hotelId: {}", hotelId);
                                return checkSearchPageAndUpdateTimeWindows(scanTimeWindow, pageLastTime, hotelId, hotelCommentDTO.isLastPage());
                            }

                            return Flux.fromIterable(newEntities)
                                    .flatMap(hotelSimpleCommentDao::insert)
                                    .collectList().flatMap(savedEntities -> checkSearchPageAndUpdateTimeWindows(scanTimeWindow, pageLastTime, hotelId, hotelCommentDTO.isLastPage()));
                        });

                    } else {
                        log.info("当前页数据不需要保存，停止翻页, hotelId: {}, 原因: 首条评论时间[{}]不满足扫描窗口条件",
                                hotelId, DateUtil.formatDate(pageFirstTime));
                        return checkSearchPageAndUpdateTimeWindows(scanTimeWindow, pageLastTime, hotelId, hotelCommentDTO.isLastPage())
                                .then(Mono.just(HotelCommentVO.builder().searchNextPage(false).build()));
                    }
                })
                .doOnSuccess(result -> log.info("评论同步处理完成, hotelId: {}, 是否需要翻下一页: {}", hotelId, result.getSearchNextPage()))
                .doOnError(error -> log.error("评论同步处理失败, hotelId: {}, error: {}", hotelId, error.getMessage(), error));
    }


    private List<SimpleCommentEntity> toSimpleCommentEntity(HotelCommentDTO hotelCommentDTO) {
        if (CollUtil.isNotEmpty(hotelCommentDTO.getCommentInfoV1s())) {
            return hotelCommentDTO.getCommentInfoV1s().stream()
                    .map(commentInfoV1 -> commentV1ConvertToSimpleCommentEntity(commentInfoV1, hotelCommentDTO))
                    .toList();
        } else {
            return hotelCommentDTO.getCommentInfoV2s().stream()
                    .map(commentInfoV2 -> commentV2ConvertToSimpleCommentEntity(commentInfoV2, hotelCommentDTO))
                    .toList();
        }
    }

    public SimpleCommentEntity commentV2ConvertToSimpleCommentEntity(HotelCommentDTO.CommentInfoV2 commentInfoV2, HotelCommentDTO hotelCommentDTO) {
        SimpleCommentEntity simpleCommentEntity = new SimpleCommentEntity();
        simpleCommentEntity.setBusinessCommentId(String.valueOf(commentInfoV2.getId()));
        simpleCommentEntity.setExternalHotelId(hotelCommentDTO.getCompetitorHotelId());
        HotelCommentDTO.UserInfo userInfo = commentInfoV2.getUserInfo();
        if(Objects.nonNull(userInfo)) {
            simpleCommentEntity.setUsername(userInfo.getNickName());
        }


        simpleCommentEntity.setCheckInDate(LocalDate.parse(commentInfoV2.getCheckInDate(), NORM_DATETIME_FORMATTER).toString());
        simpleCommentEntity.setCommentDate(LocalDate.parse(commentInfoV2.getCreateDate(), NORM_DATETIME_FORMATTER));
        simpleCommentEntity.setTravelType(commentInfoV2.getTravelTypeText());
        simpleCommentEntity.setRoomType(commentInfoV2.getRoomTypeName());
        HotelCommentDTO.RatingInfo ratingInfo = commentInfoV2.getRatingInfo();
        simpleCommentEntity.setCommentScore(Objects.nonNull(ratingInfo) && Objects.nonNull(ratingInfo.getRatingAll()) ?
                new BigDecimal(ratingInfo.getRatingAll()) : BigDecimal.ZERO);
        simpleCommentEntity.setCommentContent(commentInfoV2.getContent());
        simpleCommentEntity.setRowStatus(1);
        simpleCommentEntity.setAnalysisStatus(0);
        simpleCommentEntity.setCommentChannel(hotelCommentDTO.getChannel());
        simpleCommentEntity.setCommentPlatform(hotelCommentDTO.getPlatform());
        if(CollUtil.isNotEmpty(commentInfoV2.getFeedbackList())) {
            HotelCommentDTO.FeedbackInfo feedbackInfo = commentInfoV2.getFeedbackList().get(0);
            simpleCommentEntity.setHotelReplyContent(feedbackInfo.getContent());
            simpleCommentEntity.setReplyDate(LocalDate.parse(feedbackInfo.getCreateDate(), NORM_DATETIME_FORMATTER));
        }

        if(Objects.nonNull(hotelCommentDTO.getOperatorInfo())) {
            simpleCommentEntity.setCreatedBy(hotelCommentDTO.getOperatorInfo().getOperatorId());
            simpleCommentEntity.setCreatedByName(hotelCommentDTO.getOperatorInfo().getOperatorName());
            simpleCommentEntity.setUpdatedBy(hotelCommentDTO.getOperatorInfo().getOperatorId());
            simpleCommentEntity.setUpdatedByName(hotelCommentDTO.getOperatorInfo().getOperatorName());
        }

        return simpleCommentEntity;

    }






    public Mono<Long> getHotelLastCollectionTime(String hotelCode) {
        return getHotelAndCompetitorHotelIds(hotelCode)
                .flatMap(hotelIds -> {
                    if (CollUtil.isEmpty(hotelIds)) {
                        return Mono.just(System.currentTimeMillis());
                    }

                    // 正常处理流程
                    return Flux.fromIterable(hotelIds)
                            .flatMap(hotelId ->
                                    hotelCommentRedisDao.getScanTimeWindowDTO(hotelId)
                                            .map(Optional::ofNullable)
                            )
                            .collectList()
                            .flatMap(optionalList -> {
                                if (optionalList.isEmpty() ||
                                        optionalList.stream().anyMatch(opt -> opt.isEmpty() || opt.get().getFinishTime() == null)) {
                                    return Mono.just(0L);
                                }

                                Optional<Long> minTimestamp = optionalList.stream()
                                        .filter(Optional::isPresent)
                                        .map(Optional::get)
                                        .map(ScanTimeWindowDTO::getFinishTime)
                                        .map(Date::getTime)
                                        .min(Long::compareTo);

                                return Mono.just(minTimestamp.orElse(0L));
                            });
                })
                .switchIfEmpty(Mono.just(System.currentTimeMillis()))
                .defaultIfEmpty(0L);
    }

    public Mono<List<String>> getHotelAndCompetitorHotelIds(String hotelCode) {


        return hotelCompetitorService.listCompetitors(hotelCode, null).map(results -> {

            if (CollUtil.isEmpty(results)) {
                return Collections.<String>emptyList();
            }
            return results.stream().map(HotelCompetitorVO::getCompetitorHotelId).collect(Collectors.toList());

        });

    }

    @NotNull
    private static List<String> buildBussinessComentIds(List<SimpleCommentEntity> simpleCommentEntity) {
       return simpleCommentEntity.stream().map(SimpleCommentEntity::getBusinessCommentId).collect(Collectors.toList());
    }

    @NotNull
    private Mono<HotelCommentVO> checkSearchPageAndUpdateTimeWindows(ScanTimeWindowDTO scanTimeWindow, Date pageLastTime, String hotelId, boolean lastPage) {
        // 更新扫描窗口并检查是否需要继续翻页
        boolean needNextPage = scanTimeWindow.updateAndCheckNextPage(pageLastTime, lastPage);

        // 保存更新后的扫描窗口
        return hotelCommentRedisDao.saveInfo(hotelId, scanTimeWindow)
                .thenReturn(HotelCommentVO.builder()
                        .searchNextPage(needNextPage)
                        .build());
    }

    private static void sortByCommentReleaseDate(List<HotelCommentDTO.CommentInfoV1> commentInfoV1s) {
        commentInfoV1s.sort((c1, c2) -> {
            if (StringUtils.isBlank(c1.getReviewDetails().getReleaseDate())
                    || StringUtils.isBlank(c2.getReviewDetails().getReleaseDate())) {
                return 0;
            }
            Date time1 = DateUtil.parse(c1.getReviewDetails().getReleaseDate(), DatePattern.NORM_DATE_PATTERN);
            Date time2 = DateUtil.parse(c2.getReviewDetails().getReleaseDate(), DatePattern.NORM_DATE_PATTERN);
            return time2.compareTo(time1);
        });
    }

    /**
     * 将评论信息转换为SimpleCommentEntity
     *
     * @param commentInfoV1     评论信息
     * @param hotelCommentDTO 酒店评论DTO
     * @return SimpleCommentEntity
     */
    private SimpleCommentEntity commentV1ConvertToSimpleCommentEntity(HotelCommentDTO.CommentInfoV1 commentInfoV1, HotelCommentDTO hotelCommentDTO) {
        SimpleCommentEntity entity = new SimpleCommentEntity();

        entity.setBusinessCommentId(commentInfoV1.getReviewId());
        entity.setExternalHotelId(hotelCommentDTO.getCompetitorHotelId());
        if (commentInfoV1.getUserProfile() != null) {
            entity.setUsername(commentInfoV1.getUserProfile().getUserName());
        }
        if (commentInfoV1.getReviewDetails() != null) {
            HotelCommentDTO.ReviewDetails details = commentInfoV1.getReviewDetails();

            entity.setCheckInDate(details.getCheckInDate());
            entity.setCommentDate(LocalDate.parse(details.getReleaseDate(), NORM_DATE_FORMATTER));
            entity.setTravelType(details.getTravelType());
            entity.setRoomType(details.getRoomType());

            entity.setCommentContent(details.getReviewContent());

            if (details.getReviewScore() != null && details.getReviewScore().getScore() != null) {
                try {
                    entity.setCommentScore(new BigDecimal(details.getReviewScore().getScore()));
                } catch (NumberFormatException e) {
                    entity.setCommentScore(BigDecimal.ZERO);
                }
            }
            if (CollUtil.isNotEmpty(details.getFeedbackList())) {
                HotelCommentDTO.FeedbackItem feedbackItem = details.getFeedbackList().get(0);
                entity.setHotelReplyContent(feedbackItem.getContent());
                entity.setReplyDate(LocalDate.parse(feedbackItem.getCreateDate(), NORM_DATE_FORMATTER));
            }
        }

        entity.setCommentChannel(hotelCommentDTO.getChannel());
        entity.setCommentPlatform(hotelCommentDTO.getPlatform());

        entity.setRowStatus(1);
        entity.setAnalysisStatus(0);

        if (hotelCommentDTO.getOperatorInfo() != null) {
            entity.setCreatedBy(hotelCommentDTO.getOperatorInfo().getOperatorId());
            entity.setCreatedByName(hotelCommentDTO.getOperatorInfo().getOperatorName());
            entity.setUpdatedBy(hotelCommentDTO.getOperatorInfo().getOperatorId());
            entity.setUpdatedByName(hotelCommentDTO.getOperatorInfo().getOperatorName());
        }

        return entity;
    }

    private TaskScheduleDetailVO toVo(TaskScheduleDetailEntity entity) {
        return TaskScheduleDetailVO.builder()
                .id(entity.getId())
                .rowStatus(entity.getRowStatus())
                .jobType(entity.getJobType())
                .bizId(entity.getBizId())
                .jobInput(entity.getJobInput())
                .jobStatus(entity.getJobStatus())
                .bizId(entity.getBizId())
                .jobCode(entity.getJobCode())
                .jobName(entity.getJobName())
                .jobOutput(entity.getJobOutput())
                .jobInstanceId(entity.getJobInstanceId())
                .errorType(entity.getErrorType())
                .errorMsg(entity.getErrorMsg())
                .sourceType(entity.getSourceType())
                .createdAt(entity.getCreatedAt())
                .createdBy(entity.getCreatedBy())
                .createdByName(entity.getCreatedByName())
                .updatedAt(entity.getUpdatedAt())
                .updatedBy(entity.getUpdatedBy())
                .updatedByName(entity.getUpdatedByName())
                .build();

    }

    private String buildJobType(String dataSource) {
        if (HotelDataSourceEnum.BDW.name().equals(dataSource)) {
            return TaskScheduleJobTypeEnum.bdw_hotel_sync_save.getJobType();
        }
        return TaskScheduleJobTypeEnum.ctrip_hotel_fetch_save.getJobType();
    }

    private <T> Mono<TaskScheduleDetailVO> updateExistingTask(Long taskId, T baseDTO, HeaderUtils.HeaderInfo headerInfo) {
        //先查询到记录
        return taskScheduleDetailRepository.findById(taskId)
                .flatMap(entity -> {
                    entity = buildTaskScheduleDetailEntity(baseDTO, entity, headerInfo);
                    return taskScheduleDetailRepository.save(entity);
                }).map(this::toVo);
    }

    private <T> Mono<TaskScheduleDetailVO> createNewTask(T baseDTO, HeaderUtils.HeaderInfo headerInfo) {
        TaskScheduleDetailEntity entity = buildTaskScheduleDetailEntity(baseDTO, null, headerInfo);
        return taskScheduleDetailRepository.save(entity).map(this::toVo);
    }

    private Mono<TaskScheduleDetailVO> findByBizCode(long bizId, String jobType) {
        Criteria criteria = Criteria.where(TaskScheduleDetailField.biz_id.name()).is(bizId)
                .and(TaskScheduleDetailField.job_type.name()).is(jobType)
                .and(TaskScheduleDetailField.row_status.name()).is(RowStatusEnum.VALID.getId());
//                .and(TaskScheduleDetailField.job_status.name()).in(TaskScheduleJobStatusEnum.created.getJobStatus(), TaskScheduleJobStatusEnum.failure.getJobStatus());
        return r2dbcEntityTemplate.select(Query.query(criteria), TaskScheduleDetailEntity.class)
                .map(this::toVo)
                .next();
    }


    public Mono<Boolean> existsTaskAfterTime(long bizId,String jobType, LocalDateTime localDateTime) {
        Criteria criteria = Criteria.where(TaskScheduleDetailField.job_type.name()).is(jobType)
                .and(TaskScheduleDetailField.biz_id.name()).is(bizId)
                .and(TaskScheduleDetailField.row_status.name()).is(RowStatusEnum.VALID.getId())
                .and(TaskScheduleDetailField.created_at.name()).greaterThan(localDateTime);

        Query query = Query.query(criteria);
        query.limit(1);
        query.sort(Sort.by(Sort.Order.asc(TaskScheduleDetailField.created_at.name())));
        return r2dbcEntityTemplate.select(query, TaskScheduleDetailEntity.class)
                .hasElements()
                .onErrorResume(throwable -> {
                            log.info("existsTaskAfterTime error", throwable);
                            return Mono.just(false);
                        }
                );
    }

    public <T> TaskScheduleDetailEntity buildTaskScheduleDetailEntity(T dto, TaskScheduleDetailEntity entity, HeaderUtils.HeaderInfo headerInfo) {
        TaskScheduleDetailEntity taskScheduleDetailEntity = new TaskScheduleDetailEntity();
        if (entity != null) {
            BeanUtils.copyProperties(entity, taskScheduleDetailEntity, "jobInput", "jobType", "jobStatus", "jobCode", "bizId");
            taskScheduleDetailEntity.setUpdatedAt(LocalDateTime.now());
            if (headerInfo != null) {
                taskScheduleDetailEntity.setUpdatedBy(headerInfo.getUserId());
                taskScheduleDetailEntity.setUpdatedByName(headerInfo.getUsername());
            }
        }
        taskScheduleDetailEntity.setJobInput(JacksonUtils.writeValueAsString(dto));
        taskScheduleDetailEntity.setRowStatus(RowStatusEnum.VALID.getId());
        taskScheduleDetailEntity.setJobHandler("");
        taskScheduleDetailEntity.setJobStatus(TaskScheduleJobStatusEnum.created.getJobStatus());
        taskScheduleDetailEntity.setJobCode(IdUtils.generateDateTimePrefixId());
        taskScheduleDetailEntity.setSourceType("");
        taskScheduleDetailEntity.setJobInstanceId("");
        taskScheduleDetailEntity.setErrorType("");
        taskScheduleDetailEntity.setSourceType("");
        taskScheduleDetailEntity.setErrorMsg("{}");
        taskScheduleDetailEntity.setJobOutput("{}");
        taskScheduleDetailEntity.setCreatedAt(LocalDateTime.now());
        if (headerInfo != null) {
            taskScheduleDetailEntity.setCreatedBy(headerInfo.getUserId());
            taskScheduleDetailEntity.setCreatedByName(headerInfo.getUsername());
        }
        // 根据不同的DTO类型设置特定属性
        if (dto instanceof PluginHotelDTO) {
            PluginHotelDTO hotelDTO = (PluginHotelDTO) dto;
            taskScheduleDetailEntity.setJobType(buildJobType(hotelDTO.getDataSource()));
            taskScheduleDetailEntity.setBizId(StringConvertUtils.stringToLongHash(hotelDTO.getHotelCode(), hotelDTO.getHotelName()));
            taskScheduleDetailEntity.setJobName(hotelDTO.getHotelName());
        } else if (dto instanceof EbkBatchCommentDTO) {
            EbkBatchCommentDTO commentDTO = (EbkBatchCommentDTO) dto;
            taskScheduleDetailEntity.setJobType(TaskScheduleJobTypeEnum.ebk_comment_batch_save.getJobType());
            taskScheduleDetailEntity.setBizId(StringConvertUtils.stringToLongHash(commentDTO.getHotelCode(), commentDTO.getHotelName()));
            taskScheduleDetailEntity.setJobName(commentDTO.getHotelName());
        } else if (dto instanceof EbkSingleCommentDTO) {
            EbkSingleCommentDTO commentDTO = (EbkSingleCommentDTO) dto;
            taskScheduleDetailEntity.setJobType(TaskScheduleJobTypeEnum.ebk_comment_single_save.getJobType());
            taskScheduleDetailEntity.setBizId(StringConvertUtils.stringToLongHash(commentDTO.getHotelCode(), null));
            taskScheduleDetailEntity.setJobName(commentDTO.getHotelName());
        } else if (dto instanceof HotelPriceDTO) {
            HotelPriceDTO hotelPriceDTO = (HotelPriceDTO) dto;
            taskScheduleDetailEntity.setJobType(TaskScheduleJobTypeEnum.ctrip_hotel_price_fetch_save.getJobType());
            taskScheduleDetailEntity.setBizId(StringConvertUtils.stringToLongHash(hotelPriceDTO.getHotelCode(), hotelPriceDTO.getHotelName()));
            taskScheduleDetailEntity.setJobName(hotelPriceDTO.getHotelName());
        } else if (dto instanceof PluginInitializationReportDTO) {
            PluginInitializationReportDTO pluginInitDto = (PluginInitializationReportDTO) dto;
            String jobType = Optional.ofNullable(entity)
                    .map(TaskScheduleDetailEntity::getJobType)
                    .orElse(TaskScheduleJobTypeEnum.real_time_save_ebk_comment_index.getJobType());

            String hotelName = Optional.ofNullable(entity)
                    .map(TaskScheduleDetailEntity::getJobName)
                    .orElse("");
            taskScheduleDetailEntity.setJobType(jobType);
            taskScheduleDetailEntity.setBizId(StringConvertUtils.combineStringsToHash("report", pluginInitDto.getHotelCode(), String.valueOf(pluginInitDto.getReportRecordId())));
            taskScheduleDetailEntity.setJobName(hotelName);
        } else if (dto instanceof PluginOrderJsonDTO) {
            PluginOrderJsonDTO pluginOrderJsonDTO = (PluginOrderJsonDTO) dto;
            taskScheduleDetailEntity.setJobType(TaskScheduleJobTypeEnum.plugin_order_save_index.getJobType());
            taskScheduleDetailEntity.setBizId(StringConvertUtils.stringToLongHash(String.valueOf(pluginOrderJsonDTO.getOrder()), null));
            taskScheduleDetailEntity.setJobName(pluginOrderJsonDTO.getHotelName());
        }
        return taskScheduleDetailEntity;
    }

    public Mono<TaskScheduleDetailVO> orderSync(PluginOrderJsonDTO pluginOrderDTO) {
        if (StringUtils.isBlank(pluginOrderDTO.getHotelCode()) || StringUtils.isBlank(pluginOrderDTO.getPlatform())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "hotel_code||platform cannot be null"));
        }
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo ->
                        syncTask(headerInfo, pluginOrderDTO,
                                dto -> StringConvertUtils.stringToLongHash(String.valueOf(pluginOrderDTO.getOrder()), null),
                                dto -> TaskScheduleJobTypeEnum.plugin_order_save_index.getJobType()))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "Invalid platform type")));
    }

//    private Mono<PluginOrderDTO> getOrderDTOByPlatform(PluginOrderDTO pluginOrderDTO) {
//        if (pluginOrderDTO.getPlatform().equals(PluginPlatformEnum.CTRIP.getCode())) {
//            return Mono.just((PluginCtripOrderDTO) pluginOrderDTO);
//        }
//        return Mono.empty();
//    }
}
