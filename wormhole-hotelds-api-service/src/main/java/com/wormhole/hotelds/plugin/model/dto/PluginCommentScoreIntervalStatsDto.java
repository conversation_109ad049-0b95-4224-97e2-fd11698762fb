package com.wormhole.hotelds.plugin.model.dto;

import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-20 19:36:05
 * @Description: 按时间间隔统计的评论平均分结果
 */
@Data
@NoArgsConstructor
public class PluginCommentScoreIntervalStatsDto {

    private PluginCommentStatisticsIntervalEnum intervalEnum;

    private List<TimeIntervalScore> intervals;


    public static PluginCommentScoreIntervalStatsDto createWithIntervals(PluginCommentStatisticsIntervalEnum intervalEnum, List<TimeIntervalScore> intervals) {
        PluginCommentScoreIntervalStatsDto dto = new PluginCommentScoreIntervalStatsDto();
        dto.setIntervalEnum(intervalEnum);
        dto.setIntervals(intervals);

        return dto;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimeIntervalScore {

        private String intervalStart;

        private Long commentCount;

        private Double avgScore;
    }


}
