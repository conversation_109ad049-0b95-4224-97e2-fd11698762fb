package com.wormhole.hotelds.plugin.model.enums;

import lombok.Getter;

@Getter
public enum PluginReplyStyleEnum {

    ENTHUSIASTIC_LIVELY("enthusiastic_lively", "热情活泼"),
    PROFESSIONAL_STEADY("professional_steady", "专业沉稳"),
    ANCIENT_POETIC("ancient_poetic", "古风诗意"),
    ;


    private final String code;
    private final String desc;

    PluginReplyStyleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
