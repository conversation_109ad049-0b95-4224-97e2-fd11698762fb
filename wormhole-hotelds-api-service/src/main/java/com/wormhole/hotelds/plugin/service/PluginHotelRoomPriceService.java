package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.model.dto.CompetitorRoomDatePriceDTO;
import com.wormhole.hotelds.plugin.model.enums.SelectStatusEnum;
import com.wormhole.hotelds.plugin.model.req.CompetitorRoomDatePriceReq;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.tuple.Pair;
import com.wormhole.agent.knowledge.model.dto.OperatorInfo;
import com.wormhole.hotelds.plugin.dao.*;
import com.wormhole.hotelds.plugin.model.dto.CompetitorRoomPriceDTO;
import com.wormhole.hotelds.plugin.model.entity.HdsCollectDataRecord;
import com.wormhole.hotelds.plugin.model.entity.HdsLowestPriceCollectRecord;
import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import com.wormhole.hotelds.plugin.model.enums.CollectTypeEnum;
import com.wormhole.hotelds.plugin.model.mapper.HotelPriceMapper;
import com.wormhole.hotelds.plugin.model.qo.HdsLowestPriceCollectRecordQO;
import com.wormhole.hotelds.plugin.model.qo.HotelCompetitorQO;
import com.wormhole.hotelds.plugin.model.qo.HotelPriceDeleteQO;
import com.wormhole.hotelds.plugin.model.req.CompetitorRoomPriceReq;
import com.wormhole.task.model.dto.HotelPriceDTO;
import com.wormhole.task.model.entity.index.HotelPriceHistoryIndex;
import com.wormhole.task.model.entity.index.HotelPriceIndex;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.wormhole.task.model.dto.HotelPriceDTO.MealInfo.toBreakfastCount;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Service
@Slf4j
public class PluginHotelRoomPriceService implements Serializable {

    @Autowired
    private HotelPriceDao hotelPriceDao;
    @Autowired
    private HotelPriceHistoryDao hotelPriceHistoryDao;
    @Autowired
    private HdsLowestPriceCollectRecordDao hdsLowestPriceCollectRecordDao;
    @Autowired
    private WpHotelCompetitorDao wpHotelCompetitorDao;
    @Autowired
    private PluginCommentProperties pluginCommentProperties;


    public Mono<CompetitorRoomDatePriceDTO> queryCompetitorRoomDatePrice(CompetitorRoomDatePriceReq competitorRoomDatePriceReq) {
        HotelCompetitorQO hotelCompetitorQO = new HotelCompetitorQO();
        hotelCompetitorQO.setHotelCode(competitorRoomDatePriceReq.getHotelCode());
        hotelCompetitorQO.setCompetitorHotelId(competitorRoomDatePriceReq.getCompetitorHotelId());
        return wpHotelCompetitorDao.findOne(hotelCompetitorQO).flatMap(
                hotelCompetitorEntity -> {
                    HdsLowestPriceCollectRecordQO priceCollectRecordQO = new HdsLowestPriceCollectRecordQO();
                    LocalDate date = LocalDate.parse(competitorRoomDatePriceReq.getDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
                    priceCollectRecordQO.setPriceDates(List.of(date));
                    priceCollectRecordQO.setExternalHotelIds(List.of(hotelCompetitorEntity.getCompetitorHotelId()));
                    return hdsLowestPriceCollectRecordDao.findList(priceCollectRecordQO).flatMap(
                            result -> {
                                CompetitorRoomDatePriceDTO competitorRoomDatePriceDTO = new CompetitorRoomDatePriceDTO();
                                competitorRoomDatePriceDTO.setCompetitorHotelId(hotelCompetitorEntity.getCompetitorHotelId());
                                competitorRoomDatePriceDTO.setCompetitorHotelName(hotelCompetitorEntity.getCompetitorHotelName());
                                competitorRoomDatePriceDTO.setDate(competitorRoomDatePriceReq.getDate());
                                if (CollUtil.isEmpty(result)) {
                                    return Mono.just(competitorRoomDatePriceDTO);
                                } else {
                                    result.sort(Comparator.comparing(HdsLowestPriceCollectRecord::getCollectTime).reversed());

                                    HdsLowestPriceCollectRecord current = result.get(0);
                                    competitorRoomDatePriceDTO.setCurrentPrice(current.getLowestPrice());
                                    competitorRoomDatePriceDTO.setLastCollectionDatetime(current.getCollectTime());
                                    competitorRoomDatePriceDTO.setPreviousPrice(
                                            result.size() > 1 ? result.get(1).getLowestPrice() : null
                                    );

                                    return Mono.just(competitorRoomDatePriceDTO);

                                }

                            }
                    );
                }
        );

    }


    public Mono<Boolean> syndeticHotelPrice(HotelPriceDTO hotelPriceDTO) {
        log.info("syndeticHotelPrice: {}", JacksonUtils.writeValueAsString(hotelPriceDTO));

        return syncHotelPriceToEs(hotelPriceDTO).flatMap(
                hotelPriceIndexes -> syncHotelLowestPriceToDb(hotelPriceDTO, hotelPriceIndexes)
        );
    }


    public Mono<List<CompetitorRoomPriceDTO>> queryCompetitorRoomPrice(CompetitorRoomPriceReq competitorRoomPriceReq) {
        HotelCompetitorQO hotelCompetitorQO = new HotelCompetitorQO();
        hotelCompetitorQO.setHotelCode(competitorRoomPriceReq.getHotelCode());
        hotelCompetitorQO.setSelectedStatus(SelectStatusEnum.SELECTED.getCode());

        return wpHotelCompetitorDao.findList(hotelCompetitorQO).flatMap(
                hotelCompetitorEntities -> {
                    List<String> competitorHotelIds = hotelCompetitorEntities.stream().map(HotelCompetitorEntity::getCompetitorHotelId).toList();
                    List<LocalDate> totalPriceDates = new ArrayList<>();
                    for (int i = 0; i < 8; i++) {
                        totalPriceDates.add(LocalDate.now().plusDays(i + 1));
                    }
                    HdsLowestPriceCollectRecordQO priceCollectRecordQO = new HdsLowestPriceCollectRecordQO();
                    priceCollectRecordQO.setPriceDates(totalPriceDates);
                    priceCollectRecordQO.setExternalHotelIds(competitorHotelIds);
                    return hdsLowestPriceCollectRecordDao.findList(priceCollectRecordQO).map(
                            hotelPriceCollectRecordList -> {
                                Map<String, Map<LocalDate, List<HdsLowestPriceCollectRecord>>> hotelCode2PriceDate2PriceRecord = hotelPriceCollectRecordList
                                        .stream()
                                        .collect(Collectors.groupingBy(HdsLowestPriceCollectRecord::getExternalHotelId
                                                , Collectors.groupingBy(HdsLowestPriceCollectRecord::getPriceDate, Collectors.toList())));
                                return hotelCompetitorEntities
                                        .stream()
                                        .map(hotelCompetitorEntity -> buildCompetitorRoomPriceDTO(hotelCompetitorEntity, hotelCode2PriceDate2PriceRecord, totalPriceDates))
                                        .toList();
                            });

                }
        );


    }

    @NotNull
    private CompetitorRoomPriceDTO buildCompetitorRoomPriceDTO(HotelCompetitorEntity hotelCompetitorEntity
            , Map<String, Map<LocalDate, List<HdsLowestPriceCollectRecord>>> hotelCode2PriceDate2PriceRecord, List<LocalDate> totalPriceDates) {
        CompetitorRoomPriceDTO competitorRoomPriceDTO = new CompetitorRoomPriceDTO();
        competitorRoomPriceDTO.setCompetitorHotelId(hotelCompetitorEntity.getCompetitorHotelId());
        competitorRoomPriceDTO.setCompetitorHotelName(hotelCompetitorEntity.getCompetitorHotelName());

        Optional<Map<LocalDate, List<HdsLowestPriceCollectRecord>>> priceDate2PriceRecordOpt = Optional
                .ofNullable(hotelCode2PriceDate2PriceRecord.get(hotelCompetitorEntity.getCompetitorHotelId()));

        List<CompetitorRoomPriceDTO.RoomPriceInfo> roomPriceInfos = totalPriceDates
                .stream()
                .map(e -> buildRoomPriceInfo(e, totalPriceDates, priceDate2PriceRecordOpt))
                .collect(Collectors.toList());
        competitorRoomPriceDTO.setPriceRecords(roomPriceInfos);
        return competitorRoomPriceDTO;
    }

    @NotNull
    private CompetitorRoomPriceDTO.RoomPriceInfo buildRoomPriceInfo(LocalDate date
            , List<LocalDate> priceDates
            , Optional<Map<LocalDate, List<HdsLowestPriceCollectRecord>>> priceDate2PriceRecordOpt) {
        CompetitorRoomPriceDTO.RoomPriceInfo roomPriceInfo = new CompetitorRoomPriceDTO.RoomPriceInfo();
        roomPriceInfo.setDate(date.toString());
        roomPriceInfo.setNeedRecollection(true);
        roomPriceInfo.setNeedShow(!Objects.equals(date, priceDates.get(priceDates.size() - 1)));

        if (priceDate2PriceRecordOpt.isPresent()) {
            Map<LocalDate, List<HdsLowestPriceCollectRecord>> priceDate2PriceRecord = priceDate2PriceRecordOpt.get();
            Optional<List<HdsLowestPriceCollectRecord>> priceRecordOpt = Optional.ofNullable(priceDate2PriceRecord.get(date));
            if (priceRecordOpt.isPresent()) {
                List<HdsLowestPriceCollectRecord> priceRecord = priceRecordOpt.get();
                priceRecord.sort(Comparator.comparing(HdsLowestPriceCollectRecord::getCollectTime).reversed());
                HdsLowestPriceCollectRecord currentLowestPriceCollectRecord = priceRecord.get(0);
                roomPriceInfo.setCurrentPrice(currentLowestPriceCollectRecord.getLowestPrice());
                roomPriceInfo.setLastCollectionDatetime(currentLowestPriceCollectRecord.getCollectTime());
                roomPriceInfo.setNeedRecollection(currentLowestPriceCollectRecord.getCollectTime().plusMinutes(pluginCommentProperties.getPriceCollectionInterval()).isBefore(LocalDateTime.now()));
                if (priceRecord.size() > 1) {
                    roomPriceInfo.setPreviousPrice(priceRecord.get(1).getLowestPrice());
                }
            }
        }
        return roomPriceInfo;
    }


    public Mono<Boolean> syncHotelLowestPriceToDb(HotelPriceDTO hotelPriceDTO, List<HotelPriceIndex> hotelPriceIndices) {
        if (CollUtil.isEmpty(hotelPriceIndices)) {
            return Mono.just(false);
        }


        Optional<HotelPriceIndex> lowestPriceOpt = hotelPriceIndices.stream().sorted(Comparator.comparing(HotelPriceIndex::getPrice)).findFirst();
        if (lowestPriceOpt.isPresent()) {
            HotelPriceIndex hotelPriceIndex = lowestPriceOpt.get();
            HdsLowestPriceCollectRecord hdsLowestPriceCollectRecord = new HdsLowestPriceCollectRecord();
            hdsLowestPriceCollectRecord.setExternalHotelId(hotelPriceIndex.getHotelCode());
            hdsLowestPriceCollectRecord.setPriceDate(hotelPriceIndex.getPriceDate());
            hdsLowestPriceCollectRecord.setLowestPrice(hotelPriceIndex.getPrice());
            hdsLowestPriceCollectRecord.setChannel(hotelPriceDTO.getChannel());
            hdsLowestPriceCollectRecord.setPlatform(hotelPriceDTO.getPlatform());
            hdsLowestPriceCollectRecord.setCollectTime(LocalDateTime.now());
            return hdsLowestPriceCollectRecordDao.save(hdsLowestPriceCollectRecord).then(Mono.just(true));
        }

        return Mono.just(false);
    }


    public Mono<List<HotelPriceIndex>> syncHotelPriceToEs(HotelPriceDTO hotelPriceDTO) {
        List<HotelPriceIndex> hotelPriceIndexs = toHotelPriceIndex(hotelPriceDTO);

        if (CollUtil.isEmpty(hotelPriceIndexs)) {
            return Mono.just(Collections.emptyList());
        }


        List<String> bizIds = hotelPriceIndexs.stream().map(HotelPriceIndex::getBizId).toList();
        HotelPriceDeleteQO hotelPriceDeleteQO = HotelPriceDeleteQO.builder()
                .hotelCode(hotelPriceDTO.getHotelCode())
                .selfHotelCode(hotelPriceDTO.getSelfHotelCode())
                .bizIds(bizIds)
                .build();

        return hotelPriceDao.delete(hotelPriceDeleteQO)
                .flatMap(deleteResult -> {
                    if (deleteResult) {
                        return hotelPriceDao.saveAll(hotelPriceIndexs)
                                .doOnNext(hotelPriceIndex -> {
                                    log.info("酒店价格数据保存成功, 酒店编码: {} ", hotelPriceDTO.getHotelCode());
                                })
                                .flatMap(entities -> {
                                    List<HotelPriceHistoryIndex> historyIndexList = HotelPriceMapper.INSTANCE.toHistoryIndexList(entities);
                                    return hotelPriceHistoryDao.saveAll(historyIndexList).doOnNext(e -> {
                                        log.info("酒店价格历史数据保存成功, 酒店编码: {} ", hotelPriceDTO.getHotelCode());
                                    }).thenReturn(hotelPriceIndexs);
                                });
                    } else {
                        log.error("酒店价格数据删除失败, 酒店编码: {}", hotelPriceDTO.getHotelCode());
                        return Mono.just(Collections.<HotelPriceIndex>emptyList());
                    }
                })
                .doOnSuccess(result -> log.info("酒店价格数据处理完成,  酒店编码: {}", hotelPriceDTO.getHotelCode()))
                .doOnError(e -> log.error("酒店价格数据处理失败,  酒店编码: {}, 错误信息: ", hotelPriceDTO.getHotelCode(), e));


    }





    public List<HotelPriceIndex> toHotelPriceIndex(HotelPriceDTO hotelPriceDTO) {


        Set<String> saleRoomIds = hotelPriceDTO.getRoomList()
                .stream()
                .flatMap(e -> e.getSubRoomList().stream())
                .map(HotelPriceDTO.SubRoom::getSkey)
                .collect(Collectors.toSet());


        List<HotelPriceIndex> hotelPriceIndices = new ArrayList<>();
        Map<String, HotelPriceDTO.PhysicRoomInfo> physicRoomMap = hotelPriceDTO.getPhysicRoomMap();
        Map<String, HotelPriceDTO.SaleRoomInfo> saleRoomMap = hotelPriceDTO.getSaleRoomMap();

        for (String saleRoomId : saleRoomIds) {
            HotelPriceDTO.SaleRoomInfo saleRoomInfo = saleRoomMap.get(saleRoomId);
            if(Objects.isNull(saleRoomInfo)) {
                log.info("销售房间信息不存在, 销售房间ID: {}", saleRoomId);
                continue;
            }
            Long physicalRoomId = saleRoomInfo.getPhysicalRoomId();
            HotelPriceDTO.PhysicRoomInfo physicRoomInfo = physicRoomMap.get(String.valueOf(physicalRoomId));
            if(Objects.isNull(physicRoomInfo)) {
                log.info("物理房间信息不存在, 物理房间ID: {}", physicalRoomId);
                continue;
            }

            HotelPriceIndex hotelPriceIndex = new HotelPriceIndex();
            hotelPriceIndex.setSelfHotelCode(hotelPriceDTO.getSelfHotelCode());
            hotelPriceIndex.setHotelCode(hotelPriceDTO.getHotelCode());
            hotelPriceIndex.setHotelName(hotelPriceDTO.getHotelName());
            hotelPriceIndex.setPluginPlatform(hotelPriceDTO.getPlatform());
            hotelPriceIndex.setPluginChannel(hotelPriceDTO.getChannel());
            hotelPriceIndex.setPhysicalRoomId(physicalRoomId);

            hotelPriceIndex.setPriceDate(Optional.ofNullable(hotelPriceDTO.getSearchBoxInfo())
                    .map(HotelPriceDTO.SearchBoxInfo::getCheckIn)
                    .filter(StringUtils::isNotBlank)
                    .map(checkIn -> LocalDateTimeUtil.parseDate(checkIn, "yyyyMMdd"))
                    .orElse(null));

            hotelPriceIndex.setProductCode(saleRoomId);

            String bizId = HotelPriceIndex.createBizId(hotelPriceIndex.getHotelCode(), hotelPriceIndex.getProductCode(), hotelPriceIndex.getPriceDate());
            hotelPriceIndex.setBizId(bizId);

            assembleSaleRoomInfo(hotelPriceIndex, saleRoomInfo);
            assemblePhysicRoomInfo(hotelPriceIndex, physicRoomInfo);
            assembleOperateInfo(hotelPriceIndex, hotelPriceDTO.getOperatorInfo());

            hotelPriceIndex.setCreatedAt(new Date());
            hotelPriceIndex.setUpdatedAt(new Date());

            hotelPriceIndices.add(hotelPriceIndex);
        }
        return hotelPriceIndices;
    }

    private void assemblePhysicRoomInfo(HotelPriceIndex hotelPriceIndex, HotelPriceDTO.PhysicRoomInfo physicRoomInfo) {
        if (Objects.isNull(physicRoomInfo)) {
            return;
        }

        hotelPriceIndex.setRoomTypeName(physicRoomInfo.getName());
        hotelPriceIndex.setBedInfo(physicRoomInfo.getBedInfoTitle());
        hotelPriceIndex.setWindowInfo(physicRoomInfo.getWindowInfoTitle());
        hotelPriceIndex.setSmokeInfo(physicRoomInfo.getSmokeInfoTitle());
        hotelPriceIndex.setAreaInfo(physicRoomInfo.getAreaInfoTitle());
        hotelPriceIndex.setFloorInfo(physicRoomInfo.getFloorInfoTitle());
    }


    private void assembleSaleRoomInfo(HotelPriceIndex hotelPriceIndex, HotelPriceDTO.SaleRoomInfo saleRoomInfo) {
        if (Objects.isNull(saleRoomInfo)) {
            return;
        }
        hotelPriceIndex.setRoomCode(saleRoomInfo.getRoomCode());
        hotelPriceIndex.setBreakfastCount(Optional.ofNullable(saleRoomInfo.getMealInfo()).map(e -> toBreakfastCount(e.getTitle())).orElse(null));
        hotelPriceIndex.setGuestCount(Optional.ofNullable(saleRoomInfo.getGuestCountInfo()).map(HotelPriceDTO.GuestCountInfo::getGuestCount).orElse(null));
        hotelPriceIndex.setChildCount(Optional.ofNullable(saleRoomInfo.getGuestCountInfo()).map(HotelPriceDTO.GuestCountInfo::getChildCount).orElse(null));
        hotelPriceIndex.setPrice(Optional.ofNullable(saleRoomInfo.getPriceInfo()).map(e -> (long) (e.getPrice() * 100)).orElse(null));
        hotelPriceIndex.setCurrency(Optional.ofNullable(saleRoomInfo.getPriceInfo()).map(HotelPriceDTO.PriceInfo::getCurrency).orElse(null));
        hotelPriceIndex.setPriceLabels(Optional.ofNullable(saleRoomInfo.getPriceLabelList()).map(lists -> lists.stream().map(HotelPriceDTO.PriceLabel::getText).collect(Collectors.toList())).orElse(null));

        hotelPriceIndex.setCancelInfo(saleRoomInfo.getCancelInfoTitle());
        hotelPriceIndex.setPaymentInfo(saleRoomInfo.getPaymentInfoTitle());
    }

    private void assembleOperateInfo(HotelPriceIndex hotelPriceIndex, OperatorInfo operatorInfo) {
        Optional<OperatorInfo> operatorInfoOptional = Optional.ofNullable(operatorInfo);
        hotelPriceIndex.setCreatedBy(operatorInfoOptional.map(OperatorInfo::getOperatorId).orElse(null));
        hotelPriceIndex.setCreatedByName(operatorInfoOptional.map(OperatorInfo::getOperatorName).orElse(null));
        hotelPriceIndex.setUpdatedBy(operatorInfoOptional.map(OperatorInfo::getOperatorId).orElse(null));
        hotelPriceIndex.setUpdatedByName(operatorInfoOptional.map(OperatorInfo::getOperatorName).orElse(null));
    }

}
