package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.model.dto.CommentSentimentAnalysisDTO;
import com.wormhole.hotelds.plugin.model.dto.CommentTagAnalysisDTO;
import com.wormhole.task.model.entity.CommentLabelAnalysisEntity;
import com.wormhole.task.model.entity.CommentSentimentAnalysisEntity;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConversionService {
    private final Map<Class<?>, Function<Object, List<Object>>> converterMap = new HashMap<>();

    @PostConstruct
    public void registerConverters() {
        register(CommentSentimentAnalysisDTO.class, dto -> {
            CommentSentimentAnalysisDTO d = (CommentSentimentAnalysisDTO) dto;
            CommentSentimentAnalysisEntity entity = new CommentSentimentAnalysisEntity();
            entity.setCommentId(d.getCommentId());
            entity.setSentiment(d.getMainSentiment());
            entity.setMainFocus(d.getMainFocus());
            entity.setScore(d.getScore());
            entity.setHotelCode(d.getHotelCode());
            entity.setCommentChannel(d.getChannel());
            return List.of(entity);
        });

        register(CommentTagAnalysisDTO.class, dto -> {
            CommentTagAnalysisDTO d = (CommentTagAnalysisDTO) dto;

            if (d.getLabelDetail() == null || d.getLabelDetail().isEmpty()) {
                return Collections.emptyList();
            }

            return d.getLabelDetail().stream().map(detail -> {
                CommentLabelAnalysisEntity entity = new CommentLabelAnalysisEntity();
                entity.setCommentId(d.getCommentId());
                entity.setHotelCode(d.getHotelCode());
                entity.setCommentChannel(d.getChannel());
                entity.setLabel(detail.getLabel());
                entity.setLabelContent(detail.getMention());
                entity.setSentiment(detail.getSentiment());
                return entity;
            }).collect(Collectors.toList());
        });
    }

    public <D> void register(Class<D> dtoClass, Function<D, List<Object>> converter) {
        converterMap.put(dtoClass, (Function<Object, List<Object>>) converter);
    }

    public List<Object> convert(Object dto) {
        Function<Object, List<Object>> converter = converterMap.get(dto.getClass());
        if (converter == null) {
            throw new RuntimeException("No converter found for: " + dto.getClass().getName());
        }
        return converter.apply(dto);
    }
}
