package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallDTO;
   import com.wormhole.hotelds.plugin.model.qo.PluginOutCallQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallVO;
import com.wormhole.hotelds.plugin.service.PluginOutCallService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping(value = "/plugin/out_call")
public class PluginOutCallController {
    @Resource
    private PluginOutCallService pluginOutCallService;
    @PostMapping("/list")
    public Mono<Result<List<PluginOutCallVO>>> list(@RequestBody PluginOutCallDTO qo) {
        return pluginOutCallService.list(qo).flatMap(Result::success);
    }

    @PostMapping("/tab/list")
    public Mono<Result<List<PluginOrderTabVo>>> listTab(@RequestBody PluginOutCallDTO qo) {
        return pluginOutCallService.listTab(qo).flatMap(Result::success);
    }

    @PostMapping("/call")
    public Mono<Result<Boolean>> call(@RequestBody PluginOutCallQo qo){
        return pluginOutCallService.call(qo).flatMap(Result::success);
    }

    @PostMapping("/shu_ke/callback")
    public Mono<Result<Boolean>> shuKeCallback(@RequestBody String json) {
        return pluginOutCallService.shuKeCallback(json).flatMap(Result::success);
    }


}
