package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/22 11:53
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelCompetitorVO {
    private String id;

    private String hotelCode;

    private String competitorHotelId;

    private String competitorHotelName;

    private String competitorPageUrl;


    private String channel;

    private String platform;

    private Integer selectedStatus; // 0: 未选中, 1: 已选中

    private String competitorHotelAbbreviation; // 竞争对手酒店简称

    private Double viewRate; // 浏览率
}
