package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import com.wormhole.task.model.entity.filed.CommentField;
import com.wormhole.task.model.entity.filed.PluginCommentLabelField;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PluginCommentTagService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    /**
     * 查询所有评论标签
     */
    public Mono<List<PluginCommentLabelEntity>> findAll() {
        return r2dbcEntityTemplate.select(PluginCommentLabelEntity.class).all().collectList();
    }


    public Mono<String> findLeafLabel() {
        // 构造查询条件
        Criteria criteria = Criteria.where(PluginCommentLabelField.IS_LEAF.getColumn()).isTrue()
                .and(CommentField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginCommentLabelEntity.class)
                .map(result -> result.getName())  // 映射为 name 字段
                .collectList()  // 收集为一个 List<String>
                .map(names -> String.join("|", names));  // 用逗号拼接成一个字符串
    }



    /**
     * 查找指定名称的叶子节点及其所有父节点
     * @param name 叶子节点名称
     * @return 按层级排序的节点列表，从顶级父节点到叶子节点
     */
    public Mono<List<PluginCommentLabelEntity>> findLeafAndParent(String name) {
        return findAll().flatMap(allTags -> {
            // 创建一个Map加速查找：id -> entity
            Map<Long, PluginCommentLabelEntity> tagMap = allTags.stream()
                    .collect(Collectors.toMap(PluginCommentLabelEntity::getId, Function.identity()));
            // 找到叶子节点，且 name 匹配的节点（唯一）
            return allTags.stream()
                    .filter(tag -> tag.isLeaf() && name.equals(tag.getName()))
                    .findFirst()
                    .map(leafNode -> {
                        // 查找父节点链条
                        List<PluginCommentLabelEntity> result = new ArrayList<>();
                        PluginCommentLabelEntity current = leafNode;
                        while (current.getParentId() != null) {
                            PluginCommentLabelEntity parent = tagMap.get(current.getParentId());
                            if (parent == null) break;
                            result.add(parent);
                            current = parent;
                        }
                        // 按从顶级父节点到叶子顺序排序并添加叶子节点
                        Collections.reverse(result);
                        result.add(leafNode);
                        return Mono.just(result);
                    })
                    .orElseGet(() -> {
                        log.warn("No matching leaf node found for name='{}'", name);
                        return Mono.just(Collections.emptyList()); // 返回空列表而非抛出异常
                    });
        });
    }

}
