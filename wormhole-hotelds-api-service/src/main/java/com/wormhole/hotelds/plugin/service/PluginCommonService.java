package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.model.dto.GetPreReplyDTO;
import com.wormhole.hotelds.plugin.model.dto.SavePreReplyDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginCommentReplyPrefixContentEntity;
import com.wormhole.hotelds.plugin.model.enums.PluginReplyStyleEnum;
import com.wormhole.hotelds.plugin.model.vo.GetPreReplyVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Service
@Slf4j
public class PluginCommonService {
    @Resource
    private PluginCommentReplyPrefixContentService pluginCommentReplyPrefixContentService;

    public Mono<GetPreReplyVO> getPreReply(GetPreReplyDTO getPreReplyDTO) {
        return pluginCommentReplyPrefixContentService.getPreReply(getPreReplyDTO.getHotelCode())
                .map(entity -> {
                    GetPreReplyVO vo = new GetPreReplyVO();
                    vo.setContent(entity.getReplyPrefixContent());
                    vo.setReplyStyle(Optional.ofNullable(entity.getReplyStyle()).orElse(PluginReplyStyleEnum.ENTHUSIASTIC_LIVELY.getCode()));
                    vo.setHotelCode(entity.getHotelCode());
                    vo.setOnlyPositive(Optional.ofNullable(entity.getOnlyPositive()).orElse(0));
                    return vo;
                })
                .defaultIfEmpty(new GetPreReplyVO(getPreReplyDTO.getHotelCode(),null,PluginReplyStyleEnum.ENTHUSIASTIC_LIVELY.getCode(),0)); // 返回一个空的成功对象，而不是Mono.empty()
    }


    public Mono<PluginCommentReplyPrefixContentEntity> savePreReply(SavePreReplyDTO savePreReplyDTO) {
       return pluginCommentReplyPrefixContentService.savePreReply(savePreReplyDTO);
    }
}
