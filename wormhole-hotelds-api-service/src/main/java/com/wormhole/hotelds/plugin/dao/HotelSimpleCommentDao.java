package com.wormhole.hotelds.plugin.dao;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.plugin.model.qo.SimpleCommentQO;
import com.wormhole.task.model.entity.SimpleCommentEntity;
import com.wormhole.task.model.entity.filed.SimpleCommentField;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Repository
public class HotelSimpleCommentDao {
    @Autowired
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<SimpleCommentEntity> insert(SimpleCommentEntity simpleCommentEntity) {
        return r2dbcEntityTemplate.insert(simpleCommentEntity);
    }


    public Mono<List<SimpleCommentEntity>> findList(SimpleCommentQO simpleCommentQO) {
        return r2dbcEntityTemplate.select(Query.query( getCriteria(simpleCommentQO)), SimpleCommentEntity.class).collectList();

    }

    public Mono<SimpleCommentEntity> findOne(SimpleCommentQO simpleCommentQO) {
        Query query1 = Query.query( getCriteria(simpleCommentQO)).limit(1);
        return r2dbcEntityTemplate.selectOne(query1, SimpleCommentEntity.class);
    }

    @NotNull
    private static Criteria getCriteria(SimpleCommentQO simpleCommentQO) {
        Criteria criteria = Criteria.empty();
        if (Objects.nonNull(simpleCommentQO.getId())) {
            criteria = criteria.and(SimpleCommentField.ID.getColumn()).is(simpleCommentQO.getId());
        }
        if(CollUtil.isNotEmpty(simpleCommentQO.getBusinessIds())) {
            criteria = criteria.and(SimpleCommentField.BUSINESS_COMMENT_ID.getColumn()).in(simpleCommentQO.getBusinessIds());
        }
        if(StringUtils.isNotBlank(simpleCommentQO.getExternalHotelId())) {
            criteria = criteria.and(SimpleCommentField.EXTERNAL_HOTEL_ID.getColumn()).is(simpleCommentQO.getExternalHotelId());
        }
        criteria = criteria.and(SimpleCommentField.ROW_STATUS.getColumn()).is(RowStatusEnum.VALID.getId());
        return criteria;
    }
}
