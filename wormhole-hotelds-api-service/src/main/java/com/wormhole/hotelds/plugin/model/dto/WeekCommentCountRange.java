package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 周度评价数分布
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WeekCommentCountRange {
    /**
     * 好评数量
     */
    private List<ScoreItem> goodCommentCount;
    
    /**
     * 差评数量
     */
    private List<ScoreItem> badCommentCount;

}