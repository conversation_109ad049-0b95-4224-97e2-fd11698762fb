package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/19 16:38
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelCompetitorDTO {

    private String hotelCode;

    /**
     * 竞争对手酒店ID，用于唯一标识竞争对手的酒店
     */
    private String competitorHotelId;

    /**
     * 竞争对手酒店名称，用于显示竞争对手的酒店名字
     */
    private String competitorHotelName;

    /**
     * 竞争对手页面URL，用于提供竞争对手酒店的页面链接
     */
    private String competitorPageUrl;

    /**
     * 平台信息，用于标识数据所属的平台
     */
    private String platform;

    /**
     * 渠道信息，用于标识数据所属的渠道
     */
    private String channel;

    private Integer selectedStatus; // 0: 未选中, 1: 已选中

    private Double viewRate;
}
