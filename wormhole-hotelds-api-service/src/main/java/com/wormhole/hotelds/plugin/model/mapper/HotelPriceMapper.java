package com.wormhole.hotelds.plugin.model.mapper;

import com.wormhole.task.model.entity.index.HotelPriceHistoryIndex;
import com.wormhole.task.model.entity.index.HotelPriceIndex;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface HotelPriceMapper {
    HotelPriceMapper INSTANCE = Mappers.getMapper(HotelPriceMapper.class);
    
    @Mapping(target = "id", ignore = true)
    HotelPriceHistoryIndex toHistoryIndex(HotelPriceIndex source);
    
    List<HotelPriceHistoryIndex> toHistoryIndexList(List<HotelPriceIndex> source);
}