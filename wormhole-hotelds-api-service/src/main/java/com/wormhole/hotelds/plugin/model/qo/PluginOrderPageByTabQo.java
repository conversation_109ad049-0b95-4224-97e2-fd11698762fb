package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-06-18 10:28:48
 * @Description: 根据tab来分页查询插件订单， 每个tab是若干个组合查询条件
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginOrderPageByTabQo {

    private Integer tabType;

    private String hotelCode;

    private String platform;

    private Integer pageSize;

    private PluginOrderIndex cursor;

}
