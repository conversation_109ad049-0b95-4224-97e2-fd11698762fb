package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.dto.PlatformStatsItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginOrderTodayReportVO {
    private String hotelCode;
    private String hotelName;
    // 今日数据
    // 今日新增订单数
    private Long todayNewOrders;
    // 今日总收入
    private BigDecimal todayTotalRevenue;
    private Long todayTotalOrders;
    // 今日取消订单数
    private Long todayCancelledOrders;
    //每个平台的统计数据
    private List<PlatformStatsItem> todayPlatformStats;

    // 昨日数据
    private BigDecimal yesterdayTotalRevenue;
    private Long yesterdayTotalOrders;
    private Long yesterdayCancelledOrders;
    private List<PlatformStatsItem> yesterdayPlatformStats;

    // 环比数据
    private BigDecimal revenueComparisonRate; // 收入环比
    private BigDecimal cancelRateComparisonRate; // 取消率环比
}
