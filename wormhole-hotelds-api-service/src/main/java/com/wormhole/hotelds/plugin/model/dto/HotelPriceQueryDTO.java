package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/24 14:15
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelPriceQueryDTO {

    @NotNull(message = "酒店编码不能为空")
    private String hotelCode;

    @NotNull(message = "价格日期不能为空")
    private String priceDate;

    private String competitorHotelId;
}
