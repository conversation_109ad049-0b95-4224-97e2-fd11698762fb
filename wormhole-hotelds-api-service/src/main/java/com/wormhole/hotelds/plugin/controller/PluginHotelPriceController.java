package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.CompetitorRoomDatePriceDTO;
import com.wormhole.hotelds.plugin.model.dto.CompetitorRoomPriceDTO;
import com.wormhole.hotelds.plugin.model.dto.HotelPriceQueryDTO;
import com.wormhole.hotelds.plugin.model.req.CompetitorRoomDatePriceReq;
import com.wormhole.hotelds.plugin.model.req.CompetitorRoomPriceReq;
import com.wormhole.hotelds.plugin.model.vo.HotelMinPriceVO;
import com.wormhole.hotelds.plugin.service.HotelPriceIndexService;
import com.wormhole.hotelds.plugin.service.PluginHotelRoomPriceService;
import com.wormhole.task.model.dto.HotelPriceDTO;
import com.wormhole.task.model.vo.TaskScheduleDetailVO;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/24 14:15
 */

@RestController
@RequestMapping(value = "/plugin/hotel_price")
public class PluginHotelPriceController {

//    @Resource
//    private HotelPriceIndexService hotelPriceIndexService;

//    @PostMapping("/query/details")
//    public Mono<Result<List<HotelMinPriceVO>>> queryHotelPriceDetails(@RequestBody @Valid HotelPriceQueryDTO queryDTO) {
//        return hotelPriceIndexService.queryHotelPriceDetails(queryDTO).flatMap(Result::success);
//    }


    @Autowired
    private PluginHotelRoomPriceService pluginHotelRoomPriceService;

    @PostMapping("/competitor_comparison/by_date")
    public Mono<Result<CompetitorRoomDatePriceDTO>> queryCompetitorRoomDatePrice(@RequestBody CompetitorRoomDatePriceReq roomDatePriceReq) {
        return  pluginHotelRoomPriceService.queryCompetitorRoomDatePrice(roomDatePriceReq).flatMap(Result::success);
    }


    @PostMapping("/competitor_comparison")
    public Mono<Result<List<CompetitorRoomPriceDTO>>> queryCompetitorRoomPrice(@RequestBody @Valid CompetitorRoomPriceReq competitorRoomPriceReq) {
        return pluginHotelRoomPriceService.queryCompetitorRoomPrice(competitorRoomPriceReq).flatMap(Result::success);
    }


    @PostMapping("/sync_data")
    public Mono<Result<Boolean>> hotelPriceSync(@RequestBody HotelPriceDTO hotelPriceDTO) {
        return pluginHotelRoomPriceService.syndeticHotelPrice(hotelPriceDTO).flatMap(Result::success);
    }


}
