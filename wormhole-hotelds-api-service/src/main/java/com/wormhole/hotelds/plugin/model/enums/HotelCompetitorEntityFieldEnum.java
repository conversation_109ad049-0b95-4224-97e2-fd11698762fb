package com.wormhole.hotelds.plugin.model.enums;

import org.springframework.data.relational.core.mapping.Column;

/**
 * <AUTHOR>
 * @date 2025-05-27
 * @Description: 酒店竞争对手实体字段枚举
 */
public enum HotelCompetitorEntityFieldEnum {

    /**
     * 主键ID
     */
    id,
    /**
     * 创建时间
     */
    created_at,
    /**
     * 更新时间
     */
    updated_at,
    /**
     * 创建人
     */
    created_by,
    /**
     * 创建人姓名
     */
    created_by_name,
    /**
     * 修改人
     */
    updated_by,
    /**
     * 修改人姓名
     */
    updated_by_name,
    /**
     * 记录状态：0-删除，1-有效
     */
    row_status,

    /**
     * 酒店编码
     */
    hotel_code,

    /**
     * 竞争对手酒店ID
     */
    competitor_hotel_id,

    /**
     * 竞争对手酒店名称
     */
    competitor_hotel_name,

    /**
     * 竞争对手页面URL
     */
    competitor_page_url,

    /**
     * 渠道
     */
    channel,

    /**
     * 平台
     */
    platform,

    selected_status


    ;

}