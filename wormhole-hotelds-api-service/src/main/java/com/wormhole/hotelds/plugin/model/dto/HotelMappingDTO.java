package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.hotelds.core.model.entity.HdsHotelMappingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelMappingDTO implements Serializable {

    private String hotelCode;


    /**
     * 酒店名称
     */
    private String hotelName;


    /**
     * 外部系统酒店id
     */
    private String externalId;


    /**
     * 外部系统酒店名称
     */
    private String externalName;



    /**
     * 外部渠道
     */
    private String channel;
    /**
     * 外部系统平台
     */
    private String platform;


    public  HdsHotelMappingEntity toEntity(){
        HdsHotelMappingEntity hdsHotelMappingEntity = new HdsHotelMappingEntity();
        hdsHotelMappingEntity.setHotelCode(this.hotelCode);
        hdsHotelMappingEntity.setHotelName(this.hotelName);
        hdsHotelMappingEntity.setExternalId(this.externalId);
        hdsHotelMappingEntity.setExternalName(this.externalName);
        hdsHotelMappingEntity.setChannel(this.channel);
        hdsHotelMappingEntity.setPlatform(this.platform);
        hdsHotelMappingEntity.setStatus(RowStatusEnum.VALID.getId());
        return hdsHotelMappingEntity;
    }
}
