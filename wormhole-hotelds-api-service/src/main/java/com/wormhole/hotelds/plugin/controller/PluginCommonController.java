package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.dto.*;
import com.wormhole.hotelds.plugin.model.vo.GetPreReplyVO;
import com.wormhole.hotelds.plugin.model.vo.HotelAbbreviationResultVO;
import com.wormhole.hotelds.plugin.service.CoveringSaveService;
import com.wormhole.hotelds.plugin.service.PluginCommentTagService;
import com.wormhole.hotelds.plugin.service.PluginCommonService;
import com.wormhole.hotelds.plugin.service.PluginHotelAbbreviationService;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Function;

@RestController
@RequestMapping(value = "/plugin/common")
public class PluginCommonController {
    @Resource
    private PluginCommentTagService pluginCommentTagService;
    @Resource
    private PluginCommonService pluginCommonService;
    @Resource
    private CoveringSaveService coveringSaveService;
    @Resource
    private PluginHotelAbbreviationService pluginHotelAbbreviationService;
    @PostMapping("/findAll")
    public Mono<Result<List<PluginCommentLabelEntity>>> findAll() {
        return pluginCommentTagService.findAll().flatMap(Result::success);
    }

    @PostMapping("/findLeafLabel")
    public Mono<Result<String>> findLeafLabel() {
        return pluginCommentTagService.findLeafLabel().flatMap(Result::success);
    }

    @PostMapping("/saveEntity")
    public Mono<Result<Object>> saveEntity(@RequestBody SaveEntityDTO saveEntityDTO) {
        return coveringSaveService.saveByOverwrite(saveEntityDTO)
                .flatMap(Result::success);
    }

    @PostMapping("/saveIndex")
    public Mono<Result<Object>> saveIndex(@RequestBody SaveIndexDTO saveIndexDTO) {
        return coveringSaveService.saveIndex(saveIndexDTO)
                .flatMap(Result::success);
    }

    @PostMapping("/save_simple_comment_index")
    public Mono<Result<Object>> saveSimpleCommentIndex(@RequestBody SaveIndexDTO saveIndexDTO) {
        return coveringSaveService.saveSimpleCommentIndex(saveIndexDTO)
                .flatMap(Result::success);
    }

    @PostMapping("/get_pre_reply")
    public Mono<Result<GetPreReplyVO>> getPreReply(@RequestBody GetPreReplyDTO getPreReplyDTO) {
        return pluginCommonService.getPreReply(getPreReplyDTO)
                .flatMap(Result::success);
    }

    @PostMapping("/save_pre_reply")
    public Mono<Result<Object>> savePreReply(@RequestBody SavePreReplyDTO savePreReplyDTO) {
        return pluginCommonService.savePreReply(savePreReplyDTO)
                .flatMap(Result::success);
    }

    @GetMapping("/hotel_abbreviation")
    public Mono<Result<String>> hotelAbbreviation(@RequestParam("channel") String channel) {
        return pluginHotelAbbreviationService.findAll(channel)
                .flatMap(Result::success);
    }

    @PostMapping("/batch_generate_abbreviation")
    public Mono<Result<List<HotelAbbreviationResultVO>>> batchGenerateHotelAbbreviationName(@RequestBody List<PluginHotelAbbreviationDTO> dto) {
        return pluginHotelAbbreviationService.batchGenerateHotelAbbreviationName(dto)
                .flatMap(Result::success);
    }
}
