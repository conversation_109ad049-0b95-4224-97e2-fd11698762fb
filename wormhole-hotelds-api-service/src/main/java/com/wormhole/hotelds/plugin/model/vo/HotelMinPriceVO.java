package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 19:22
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class HotelMinPriceVO {

    private String hotelCode;

    private String hotelName;

    private Long minPrice;

    private List<RoomTypeMinPriceVO> roomTypeMinPrices = new ArrayList<>();

    private String minPriceDate;

    private Long historyMinPrice;

    private String historyCreatedAt;

    private Long selfMinPrice;

}
