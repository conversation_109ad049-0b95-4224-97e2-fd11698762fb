package com.wormhole.hotelds.plugin.config.nacos;

import lombok.Getter;

@Getter
public enum TemplateEnum {
    negative_comment_system_prompt("negative_comment_system_prompt"),
    negative_comment_user_prompt("negative_comment_user_prompt"),
    positive_comment_system_prompt("positive_comment_system_prompt"),
    positive_comment_user_prompt("positive_comment_user_prompt"),

    ;

    private final String dataId;

    private final String group;

    private final boolean useLocal;

    TemplateEnum(String dataId, String group) {
        this.dataId = dataId;
        this.group = group;
        this.useLocal = false;
    }

    TemplateEnum(String dataId) {
        this.dataId = dataId;
        this.group = NacosConstant.WORMHOLE_HOTELDS_API_GROUP_ID;
        this.useLocal = false;
    }

    TemplateEnum(String dataId, boolean useLocal) {
        this.dataId = dataId;
        this.group = NacosConstant.WORMHOLE_HOTELDS_API_GROUP_ID;
        this.useLocal = useLocal;
    }

    public Object parseContent(String content) {
        return content;
    }
}