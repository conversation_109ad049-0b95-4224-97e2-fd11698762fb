package com.wormhole.hotelds.plugin.service;

import com.wormhole.hotelds.plugin.model.entity.PluginCommentReportRecord;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsRecordStateEnum;
import com.wormhole.hotelds.plugin.model.field.PluginCommentReportRecordField;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsMonthReportVo;
import com.wormhole.hotelds.plugin.repository.PluginCommentReportRecordRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

@Service
@Slf4j
public class PluginCommentReportRecordService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private PluginCommentReportRecordRepository pluginCommentReportRecordRepository;

    public Mono<Boolean> existReportRecord(Integer reportIntervalType, String hotelCode, String channels, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.START_DATE.getColumn()).is(startDateTime)
                .and(PluginCommentReportRecordField.END_DATE.getColumn()).is(endDateTime)
                .and(PluginCommentReportRecordField.REPORT_TYPE.getColumn()).is(reportIntervalType)
                .and(PluginCommentReportRecordField.HOTEL_CODE.getColumn()).is(hotelCode)
                .and(PluginCommentReportRecordField.CHANNELS.getColumn()).is(channels);

        Query query = Query.query(criteria);

        query.limit(1);

        return r2dbcEntityTemplate.select(query, PluginCommentReportRecord.class)
                .hasElements()
                .onErrorResume(throwable -> {
                            log.info("existReportRecord error", throwable);
                            return Mono.just(false);
                        }
                );

    }

    public Mono<PluginCommentReportRecord> findOneByTimeRangeAndState(String hotelCode,String channels,Integer reportIntervalType, LocalDateTime startDateTime,
                                                                      LocalDateTime endDateTime, int state) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.START_DATE.getColumn()).is(startDateTime)
                .and(PluginCommentReportRecordField.HOTEL_CODE.getColumn()).is(hotelCode)
                .and(PluginCommentReportRecordField.CHANNELS.getColumn()).is(channels)
                .and(PluginCommentReportRecordField.END_DATE.getColumn()).is(endDateTime)
                .and(PluginCommentReportRecordField.REPORT_TYPE.getColumn()).is(reportIntervalType)
                .and(PluginCommentReportRecordField.STATE.getColumn()).is(state);
        Query query = Query.query(criteria);

        return r2dbcEntityTemplate.selectOne(query, PluginCommentReportRecord.class);
    }

    public Mono<Integer> findStateByTimeRange(String hotelCode, Integer reportIntervalType
            , LocalDateTime startDateTime, LocalDateTime endDateTime) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.START_DATE.getColumn()).is(startDateTime)
                .and(PluginCommentReportRecordField.HOTEL_CODE.getColumn()).is(hotelCode)
                .and(PluginCommentReportRecordField.END_DATE.getColumn()).is(endDateTime)
                .and(PluginCommentReportRecordField.REPORT_TYPE.getColumn()).is(reportIntervalType);
        Query query = Query.query(criteria);
        query.columns(PluginCommentReportRecordField.STATE.getColumn());
        return r2dbcEntityTemplate.selectOne(query, PluginCommentReportRecord.class)
                .map(PluginCommentReportRecord::getState)
                ;
    }

    public Mono<PluginCommentReportRecord> findInitReportRecord(String hotelCode, Integer reportIntervalType) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.HOTEL_CODE.getColumn()).is(hotelCode)
                .and(PluginCommentReportRecordField.REPORT_TYPE.getColumn()).is(reportIntervalType);
        Query query = Query.query(criteria);
        query.sort(Sort.by(Sort.Order.asc(PluginCommentReportRecordField.CREATED_AT.getColumn())));

        query.limit(1);

        return r2dbcEntityTemplate.select(query, PluginCommentReportRecord.class)
                .next();
    }

    public Flux<PluginCommentReportRecord> listByStartIdAndState(Long startId, Integer state, Integer limit) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.ID.getColumn()).greaterThan(startId)
                .and(PluginCommentReportRecordField.STATE.getColumn()).is(state);

        Query query = Query.query(criteria);
        query.limit(limit);

        return r2dbcEntityTemplate.select(query, PluginCommentReportRecord.class);
    }

    public Mono<Boolean> updateStateAndResultById(Long id, Integer state, String resultJson) {
        Criteria criteria = Criteria.where(PluginCommentReportRecordField.ID.getColumn())
                .is(id)
                .and(PluginCommentReportRecordField.STATE.getColumn())
                .is(PluginCommentStatisticsRecordStateEnum.INIT.getValue());

        Query query = Query.query(criteria);

        Update update = Update.update(PluginCommentReportRecordField.STATE.getColumn(), state)
                .set(PluginCommentReportRecordField.RESULT.getColumn(), resultJson);


        return this.r2dbcEntityTemplate.update(query, update, PluginCommentReportRecord.class)
                .doOnNext(rowsUpdated -> log.info("Rows updated: {}", rowsUpdated))
                .switchIfEmpty(Mono.defer(() -> {
                    return Mono.just(0L); // 如果没有更新任何行，返回 0
                }))
                .map(row -> row > 0);


    }


}
