package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.knowledge.model.dto.OperatorInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelCommentReviewScoreDTO {

    /**
     * 平台
     */
    private String platform;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 三方hotelCode
     */
    private String hotelCode;




    private HotelCommentReviewScoreInfo hotelCommentReviewScoreInfo;


    private OperatorInfo operatorInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotelCommentReviewScoreInfo{

        /**
         * 分类评分列表
         */
        private List<CategoryScore> categoryScore;

        /**
         * 推荐百分比
         */
        private String recommendPercent;

        /**
         * 总评分
         */
        private String score;

        /**
         * 总评论数
         */
        private Integer totalReviews;

        /**
         * 最大评分
         */
        private Integer scoreMax;

        /**
         * TA评论总数
         */
        private Integer totalReviewsTA;

        /**
         * 评分描述
         */
        private String scoreDesc;

        /**
         * 所有评论总数
         */
        private Integer allTotalReviews;

        /**
         * 携程评论总数
         */
        private Integer ctripTotalReviews;

        /**
         * 携程页面评论总数
         */
        private Integer ctripTotalReviewsForPage;

        /**
         * 页面无用评论总数
         */
        private Integer totalUnusefulReviewsForPage;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryScore {

        /**
         * 评分名称
         */
        private String scoreName;

        /**
         * 评分值
         */
        private String itemScore;

        /**
         * 评分描述
         */
        private String scoreDescription;
    }
}
