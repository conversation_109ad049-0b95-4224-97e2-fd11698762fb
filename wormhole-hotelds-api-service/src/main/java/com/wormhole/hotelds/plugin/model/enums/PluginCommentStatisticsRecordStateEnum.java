package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-24 14:41:20
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginCommentStatisticsRecordStateEnum {

    INIT(0, "初始化"),
    GENERATING(1, "生成中"),
    GENERATE_SUCCESS(2, "生成成功"),
    GENERATE_FAIL(3, "生成失败");

    private final int value;

    private final String desc;


}
