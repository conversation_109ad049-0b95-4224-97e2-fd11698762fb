package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.task.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@Table("hds_lowest_price_collection_record")
public class HdsLowestPriceCollectRecord extends BaseEntity {
    private Long id;
    private String externalHotelId;
    private LocalDate priceDate;
    private Long lowestPrice;
    private String channel;
    private String platform;
    private LocalDateTime collectTime;
}
