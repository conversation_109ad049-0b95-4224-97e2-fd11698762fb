package com.wormhole.hotelds.plugin.model.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.dto.DateRange;
import com.wormhole.hotelds.plugin.model.dto.PluginReportBadCommentInfoDto;
import com.wormhole.hotelds.plugin.model.dto.ScoreItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 月度评论统计报告VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class PluginCommentStatisticsMonthReportVo {

    /**
     * 报告类型
     */
    private String type;

    /**
     * 时间范围
     */
    private DateRange dateRange;

    /**
     * 数据对比
     */
    private DataComparison dataComparison = new DataComparison();

    /**
     * 点评内容分析
     */
    private CommentContentAnalysis commentContentAnalysis = new CommentContentAnalysis();

    /**
     * 竞品酒店分析
     */
    private CompetitorAnalysis competitorAnalysis;

    /**
     * 差评列表
     */
    private PluginReportBadCommentInfoDto badCommentInfo;

    private AiInsight aiInsight;


    public void setPositiveCommentContentAnalysis(CommentContentAnalysisItem positive) {
        this.getCommentContentAnalysis().setPositive(positive);
    }

    public void setNegativeCommentContentAnalysis(CommentContentAnalysisItem negative) {
        this.getCommentContentAnalysis().setNegative(negative);
    }

    public void setThisYearMonthlyScore(List<ScoreItem> monthlyScore) {
        this.getDataComparison().getCommentScore().setThisYear(new CommentScoreYearData(monthlyScore));
    }


    public void setPreYearMonthlyScore(List<ScoreItem> monthlyScore) {
        this.getDataComparison().getCommentScore().setPreviousYear(new CommentScoreYearData(monthlyScore));
    }

    public void setThisYearCommentCountRange(List<ScoreItem> goodCommentCount, List<ScoreItem> badCommentCount) {
        CommentCountYearData commentCountYearData = new CommentCountYearData(goodCommentCount, badCommentCount);
        this.getDataComparison().getCommentCountRange().setThisYear(commentCountYearData);
    }

    public void setPreYearCommentCountRange(List<ScoreItem> goodCommentCount, List<ScoreItem> badCommentCount) {
        CommentCountYearData commentCountYearData = new CommentCountYearData(goodCommentCount, badCommentCount);
        this.getDataComparison().getCommentCountRange().setPreviousYear(commentCountYearData);
    }

    /**
     * 设置时间范围
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void setDateRange(String startDate, String endDate) {
        this.dateRange = new DateRange(startDate, endDate);
    }


    /**
     * 月度数据对比
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class DataComparison {

        /**
         * 评分
         */
        private CommentScoreYearComparison commentScore = new CommentScoreYearComparison();

        /**
         * 评价数分布
         */
        private CommentCountRangeYearComparison commentCountRange = new CommentCountRangeYearComparison();

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentScoreYearComparison {
        /**
         * 今年数据
         */
        private CommentScoreYearData thisYear;

        /**
         * 往年数据
         */
        private CommentScoreYearData previousYear;


    }

    /**
     * 年度数据
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentScoreYearData {
        /**
         * 每月评分
         */
        private List<ScoreItem> monthlyScore;

        /**
         * 平均分
         */
        private Double avgScore;

        public CommentScoreYearData(List<ScoreItem> monthlyScore) {
            this.monthlyScore = monthlyScore;

            this.avgScore = monthlyScore.stream().mapToDouble(ScoreItem::getValue).average().orElse(0);
        }

    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentContentAnalysis {
        /**
         * 优势
         */
        private CommentContentAnalysisItem positive;

        /**
         * 劣势
         */
        private CommentContentAnalysisItem negative;


    }

    /**
     * 标签组
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentContentAnalysisItem {
        /**
         * 标签列表
         */
        private List<Label> labels;
    }

    /**
     * 标签
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Label {
        /**
         * 标签名称
         */
        private String name;

        /**
         * 标签等级
         */
        private Integer level;

        /**
         * 数量
         */
        private Long count;

        /**
         * 占比
         */
        private Integer proportion;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CompetitorAnalysis {
        /**
         * 酒店分析列表
         */
        private List<HotelAnalysis> hotelAnalysis;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelAnalysis {

        /**
         * 酒店名称
         */
        private String name;

        /**
         * 酒店代码
         */
        private String code;
        /**
         * 是否为我的酒店
         */
        private Boolean ourHotel;

        /**
         * 标签分析
         */
        private LabelAnalysis labelAnalysis;
        private CategoryScoreTrends categoryScoreTrends;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CategoryScoreTrends {

        /**
         * 卫生评分
         */
        private BigDecimal hygieneScore;

        /**
         * 设施评分
         */
        private BigDecimal facilitiesScore;

        /**
         * 服务评分
         */
        private BigDecimal serviceScore;

        /**
         * 环境评分
         */
        private BigDecimal environmentScore;

        /**
         * 评分日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime scoreCollectionTime;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class LabelAnalysis {

        /**
         * 正面标签列表
         */
        private List<Label> positiveLabels;

        /**
         * 负面标签列表
         */
        private List<Label> negativeLabels;
    }


    /**
     * 酒店标签分析
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelLabelAnalysis {
        /**
         * 正面标签
         */
        private List<HotelLabel> positive;

        /**
         * 负面标签
         */
        private List<HotelLabel> negative;

    }

    /**
     * 酒店标签
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelLabel {
        /**
         * 酒店名称
         */
        private String name;

        /**
         * 酒店代码
         */
        private String code;

        /**
         * 是否为我的酒店
         */
        private Boolean ourHotel;

        /**
         * 标签列表
         */
        private List<Label> labels;

    }


    /**
     * 月度评价数分布
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentCountRangeYearComparison {
        /**
         * 今年数据
         */
        private CommentCountYearData thisYear;

        /**
         * 往年数据
         */
        private CommentCountYearData previousYear;

    }

    /**
     * 评价数量
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentCountYearData {
        /**
         * 好评数量
         */
        private List<ScoreItem> goodCommentCount;

        /**
         * 差评数量
         */
        private List<ScoreItem> badCommentCount;

    }
}