package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.task.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * <AUTHOR>
 * @date 2025/5/19 16:28
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Table("wp_hotel_competitor")
public class HotelCompetitorEntity extends BaseEntity {

    @Id
    @Column("id")
    private Long id;

    @Column("hotel_code")
    private String hotelCode;

    @Column("competitor_hotel_id")
    private String competitorHotelId;

    @Column("competitor_hotel_name")
    private String competitorHotelName;

    @Column("competitor_page_url")
    private String competitorPageUrl;

    @Column("channel")
    private String channel;

    @Column("platform")
    private String platform;

    @Column("selected_status")
    private Integer selectedStatus; // 0: 未选中, 1: 已选中

    @Column("view_rate")
    private Double viewRate; // 浏览率

}
