package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-05-20 10:07:14
 * @Description:
 */
@Data
@Table(name = PluginCommentReportRecord.TABLE_NAME)
public class PluginCommentReportRecord {

    public static final String TABLE_NAME = "plugin_comment_report_record";


    @Id
    @Column("id")
    private Long id;

    private Integer rowStatus;

    private String createdBy;

    private String createdByName;

    private String updatedBy;

    private String updatedByName;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * {@link PluginCommentStatisticsIntervalEnum}
     */
    private Integer reportIntervalType;

    /**
     * 状态，0：初始化，1：生成中，2：生成成功,3：生成失败
     */
    private Integer state;

    private LocalDateTime startDate;

    private LocalDateTime endDate;

    private String hotelCode;

    /**
     * 选取的渠道，逗号分隔
     */
    private String channels;


    /**
     * json
     */
    private String result;


}
