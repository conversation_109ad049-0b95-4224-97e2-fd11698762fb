package com.wormhole.hotelds.plugin.model.field;

import lombok.Getter;

@Getter
public enum PluginCommentReportRecordField {

    ID("id", "主键ID"),
    ROW_STATUS("row_status", "记录状态：0-删除，1-有效"),
    CREATED_BY("created_by", "创建人ID"),
    CREATED_BY_NAME("created_by_name", "创建人名称"),
    UPDATED_BY("updated_by", "修改人ID"),
    UPDATED_BY_NAME("updated_by_name", "修改人名称"),
    CREATED_AT("created_at", "创建时间"),
    UPDATED_AT("updated_at", "更新时间"),
    REPORT_TYPE("report_interval_type", "报告类型（枚举 PluginCommentStatisticsIntervalEnum）"),
    STATE("state", "状态：0-初始化，1-生成中，2-生成成功，3-生成失败"),
    START_DATE("start_date", "起始日期"),
    END_DATE("end_date", "结束日期"),
    HOTEL_CODE("hotel_code", "酒店code，关联酒店的标识"),
    CHANNELS("channels", "渠道"),
    RESULT("result", "JSON 结果");

    private final String column;
    private final String desc;

    PluginCommentReportRecordField(String column, String desc) {
        this.column = column;
        this.desc = desc;
    }
}
