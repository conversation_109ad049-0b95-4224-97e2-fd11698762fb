package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
public class AiInsight {
    /**
     * 上周正向评论
     * */
    private Map<String, Long> positive;

    /**
     * 上周负向评论
     * */
    private Map<String, Long> negative;

    /**
     * 上上周正向评论
     * */
    private Map<String, Long> posLast2Week;

    /**
     * 上上周负向评论
     * */
    private Map<String, Long> negLast2Week;

    /**
     * 上周top1正向评论label
     * */
    private String topPositiveLabel;

    /**
     * 上周top1正向评论label计数
     * */
    private Long topPositiveLabelCount;
    /**
     * 上周top1正向评论label跟上上周该label的计数对比
     * */
    private Long topPositiveLabelCountChange;

    /**
     * 上周top1负向评论label
     * */
    private String topNegativeLabel;
    /**
     * 上周top1负向评论label计数
     * */
    private Long topNegativeLabelCount;
    /**
     * 上周top1负向评论label跟上上周该label的计数对比
     * */
    private Long topNegativeLabelCountChange;

    /**
     * 上周正向评论label趋势 上升 / 下降 / 持平
     * */
    private String topPositiveTrend;

    /**
     * 上周负向评论label趋势 上升 / 下降 / 持平
     * */
    private String topNegativeTrend;

    /**
     * 上周正向评论label程度 显著 / 小幅
     * */
    private String topPositiveDegree;

    /**
     * 上周负向评论label程度 显著 / 小幅
     * */
    private String topNegativeDegree;

    /**
     * 上周正向评论分析
     * */
    private String positiveCommentAnalysis;

    /**
     * 上周正向评论建议
     * */
    private List<String> positiveCommentSuggestion;

    /**
     * 上周负向评论分析
     * */
    private String negativeCommentAnalysis;

    /**
     * 上周负向评论建议
     * */
    private List<String> negativeCommentSuggestion;
}
