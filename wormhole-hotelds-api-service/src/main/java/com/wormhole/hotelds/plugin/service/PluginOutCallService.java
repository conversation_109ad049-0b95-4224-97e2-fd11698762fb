package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.common.constant.BooleanEnum;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.manager.PluginOutCallManager;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallDTO;
import com.wormhole.hotelds.plugin.model.enums.PluginOutCallTabEnum;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.qo.PluginOutCallQo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTabVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallVO;
import com.wormhole.task.model.constant.PluginOutCallStatusEnum;
import com.wormhole.task.model.entity.filed.PluginOrderField;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.ManagedTypes;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.query.Order;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

@Service
@Slf4j
public class PluginOutCallService {


    @Resource
    private PluginOrderIndexService pluginOrderIndexService;

    @Resource
    private PluginCommentProperties pluginCommentProperties;

    @Resource
    private PluginOutCallManager pluginOutCallManager;

    private final Map<PluginOutCallTabEnum, Function<String, PluginOrderQo>> conditionFunctionMap = Map.of(
            PluginOutCallTabEnum.WAIT_OUT_CALL, this::buildWaitOutCallCondition,
            PluginOutCallTabEnum.OUT_CALL_SUCCESS, this::buildOutCallSuccessCondition,
            PluginOutCallTabEnum.OUT_CALL_FAIL, this::buildOutCallFailCondition
    );
    private ManagedTypes managedTypes;

//    @Resource
//    private PluginOutCallRepository pluginOutCallRepository;

    public Mono<List<PluginOutCallVO>> list(PluginOutCallDTO qo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(qo.getHotelCode()), "hotel must not be blank");
        Preconditions.checkArgument(Objects.nonNull(qo.getTabType()), "tab_type must not be blank");

        PluginOrderQo searchCondition = buildPluginOrderSearchCondition(qo.getTabType(), qo.getHotelCode());

        return pluginOrderIndexService.listPluginOrder(searchCondition, this::buildPluginOutCallVo);
    }

    private PluginOutCallVO buildPluginOutCallVo(PluginOrderIndex pluginOrderIndex) {
        PluginOutCallVO pluginOutCallVO = new PluginOutCallVO();
        pluginOutCallVO.setOrderNo(pluginOrderIndex.getOrderNo());
        pluginOutCallVO.setBookerName(pluginOrderIndex.getBookerName());
        pluginOutCallVO.setContactNumber(pluginOrderIndex.getContactNumber());

        pluginOutCallVO.setPlatform(pluginOrderIndex.getPlatform());
        pluginOutCallVO.setChannel(pluginOrderIndex.getChannel());

        Optional<String> newRemarksOptional = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getRemarks);

        String remarks = pluginOrderIndex.getOrderRemarkStr(StrUtil.COMMA);

        PluginOutCallVO.RemarksInfo remarksInfo = new PluginOutCallVO.RemarksInfo();
        remarksInfo.setRemarks(newRemarksOptional.orElse(remarks));
        remarksInfo.setUpdateFlag(newRemarksOptional.isEmpty() ? BooleanEnum.NO.getCode() : BooleanEnum.YES.getCode());
        pluginOutCallVO.setRemarksInfo(remarksInfo);

        Optional<String> newArrivalTimeOptional = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getArrivalTime);
        String arrivalTime = pluginOrderIndex.getArrivalEarlyAndLatestTime();

        PluginOutCallVO.ArrivalTimeInfo arrivalTimeInfo = new PluginOutCallVO.ArrivalTimeInfo();
        arrivalTimeInfo.setArrivalTime(newArrivalTimeOptional.orElse(arrivalTime));
        arrivalTimeInfo.setUpdateFlag(newArrivalTimeOptional.isEmpty() ? BooleanEnum.NO.getCode() : BooleanEnum.YES.getCode());
        pluginOutCallVO.setArrivalTimeInfo(arrivalTimeInfo);

        List<PluginOutCallVO.OutCallRecord> outCallRecords = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getOutCallRecords)
                .map(records -> records
                        .stream()
                        .map(this::toOutCallRecord)
                        .toList()
                )
                .orElse(Collections.emptyList());

        pluginOutCallVO.setOutCallRecordList(outCallRecords);

        Integer currentStatus = Optional.ofNullable(pluginOrderIndex.getOutCallInfo())
                .map(PluginOrderIndex.OutCallInfo::getCurrentStatus)
                .orElse(PluginOutCallStatusEnum.NO_OUT_CALL.getCode());
        pluginOutCallVO.setOutCallStatus(currentStatus);

        return pluginOutCallVO;
    }

    private PluginOutCallVO.OutCallRecord toOutCallRecord(PluginOrderIndex.OutCallRecord record) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern(DatePattern.NORM_DATETIME_PATTERN)
                .withZone(ZoneId.systemDefault());

        PluginOutCallVO.OutCallRecord result = new PluginOutCallVO.OutCallRecord();
        result.setTime(formatter.format(record.getTime()));
        result.setOutCallStatus(record.getStatus());
        result.setOutCallStatusDesc(PluginOutCallStatusEnum.getByCode(record.getStatus()).getDesc());

        return result;
    }

    public Mono<List<PluginOrderTabVo>> listTab(PluginOutCallDTO qo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(qo.getHotelCode()), "hotel must not be blank");

        List<Mono<Long>> countMonoList = new ArrayList<>();

        PluginOutCallTabEnum[] tabList = PluginOutCallTabEnum.values();
        for (PluginOutCallTabEnum value : tabList) {
            Function<String, PluginOrderQo> buildConditionFunction = conditionFunctionMap.get(value);
            PluginOrderQo condition = buildConditionFunction.apply(qo.getHotelCode());
            Mono<Long> countMono = pluginOrderIndexService.countPluginOrder(condition);
            countMonoList.add(countMono);
        }
        return Mono.zip(countMonoList, countList -> {
            List<PluginOrderTabVo> resultList = new ArrayList<>(tabList.length);

            for (int i = 0; i < tabList.length; i++) {

                PluginOutCallTabEnum tabEnum = tabList[i];
                Long count = (Long) countList[i];
                PluginOrderTabVo tabVo = new PluginOrderTabVo(tabEnum.getValue(), tabEnum.getName(), count, true);
                resultList.add(tabVo);
            }
            return resultList;
        });
    }


    /**
     * 待外呼
     */
    private PluginOrderQo buildWaitOutCallCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.NO_OUT_CALL.getCode(), PluginOutCallStatusEnum.OUT_CALLING.getCode()
                )
        );
        pluginOrderQo.setSort(Sort.by(Sort.Order.desc(PluginOrderField.UPDATED_AT.getColumn())));
        return pluginOrderQo;
    }

    private PluginOrderQo buildOutCallSuccessCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
//        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.OUT_CALL_SUCCESS.getCode()
                )
        );
        String property = StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "update_flag");
        Order order = new Order(Sort.Direction.DESC, property)
                .withNested(Order.Nested.builder(PluginOrderField.OUT_CALL_INFO.getColumn()).build());

        pluginOrderQo.setSort(Sort.by(order));
        return pluginOrderQo;
    }

    private PluginOrderQo buildOutCallFailCondition(String hotelCode) {
        PluginOrderQo pluginOrderQo = new PluginOrderQo();
        pluginOrderQo.setHotelCode(hotelCode);
        pluginOrderQo.setCheckInDate(LocalDate.now());
        pluginOrderQo.setOutCallStatusList(
                CollUtil.toList(
                        PluginOutCallStatusEnum.OUT_CALL_FAIL.getCode()
                )
        );

        String property = StrUtil.join(StrUtil.DOT, PluginOrderField.OUT_CALL_INFO.getColumn(), "last_call_time");
        Order order = new Order(Sort.Direction.DESC, property)
                .withNested(Order.Nested.builder(PluginOrderField.OUT_CALL_INFO.getColumn()).build());

        pluginOrderQo.setSort(Sort.by(order));
        return pluginOrderQo;
    }

    /**
     * 外呼
     */
    public Mono<Boolean> call(PluginOutCallQo qo) {
        checkOutCallParam(qo);
        List<String> orderNoList = qo.getOrderNoList();

        // todo lock

        PluginOrderQo searchCondition = buildPluginOrderSearchCondition(qo.getTabType(), qo.getHotelCode());
        searchCondition.setOrderNoList(orderNoList);

        Date date = new Date();
        return pluginOrderIndexService.listPluginOrder(searchCondition, Function.identity())
                .flatMap(orders -> pluginOutCallManager.doOutCall(date, orders));
    }

    /**
     * 批量外呼校验
     */
    private void checkOutCallParam(PluginOutCallQo qo) {
        String hotelCode = qo.getHotelCode();
        Preconditions.checkArgument(StringUtils.isNotBlank(hotelCode), "hotel must not be blank");

        List<String> orderNoList = qo.getOrderNoList();
        Preconditions.checkArgument(CollUtil.isNotEmpty(orderNoList), "order list must not be empty");
    }

    /**
     * 构建 订单查询的条件
     */
    private PluginOrderQo buildPluginOrderSearchCondition(Integer tabType, String hotelCode) {
        Optional<PluginOutCallTabEnum> tabOptional = Optional.ofNullable(tabType)
                .map(PluginOutCallTabEnum::fromValue)
                .orElseThrow(() -> new IllegalArgumentException("tabType is Illegal"));

        return tabOptional.map(conditionFunctionMap::get)
                .map(e -> e.apply(hotelCode))
                .orElseThrow(() -> new IllegalArgumentException("tabType is Illegal"));
    }

    public Mono<Boolean> shuKeCallback(String json) {
        log.info("shu ke  callback json :{}", json);
        return Mono.just(true);
    }
}
