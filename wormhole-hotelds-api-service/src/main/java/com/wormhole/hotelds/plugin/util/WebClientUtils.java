package com.wormhole.hotelds.plugin.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wormhole.trace.TraceConstant.TRACE_ID_ATTR;
import static com.wormhole.trace.TraceConstant.TRACE_ID_HEADER;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Slf4j
public class WebClientUtils {

    private static final WebClient WEB_CLIENT;

    static {
        WEB_CLIENT = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 设置为 5MB
                .filter((request, next) -> {
                    String traceId = MDC.get(TRACE_ID_ATTR);

                    ClientRequest newRequest = ClientRequest.from(request)
                            .headers(headers -> {
                                if (traceId != null) {
                                    headers.set(TRACE_ID_HEADER, traceId);
                                }
                            })
                            .build();

                    return next.exchange(newRequest);
                })
                .build();
    }

    public static <T> Mono<T> sendPost(String url, Map<String, String> paramMap, Object body, Map<String, String> headers, TypeReference<T> typeReference) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }

        // 使用headers方法，它接受一个Consumer<HttpHeaders>
        return WEB_CLIENT.post()
                .uri(url)
                .headers(httpHeaders -> {
                    // 添加请求头
                    if (CollUtil.isNotEmpty(headers)) {
                        headers.forEach(httpHeaders::add);
                    }
                })
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> {
                    log.error("Remote service returned error status: {}", response.statusCode());
                    return response.bodyToMono(String.class)
                            .doOnNext(errorBody -> log.error("Error response body: {}", errorBody))
                            .flatMap(errorBody ->
                                    Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                            );
                })
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnError(e -> log.error("webClientPost request failed url {} params {} body {}", url, JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .doOnNext(res -> log.info("webClientPost for: url {} params {} body {} resp {}", url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(body), JacksonUtils.writeValueAsString(res)))
                .flatMap(result -> {
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    log.info("webClientPost result {}", JacksonUtils.writeValueAsString(resultData));
                    return Mono.just(resultData);
                })
                .switchIfEmpty(Mono.empty());
    }

    public static <T> Mono<T> sendFormPost(String url, Map<String, String> paramMap, Map<String, String> formParamMap, Map<String, String> headers, TypeReference<T> typeReference) {
        // Convert to multipart format
        Map<String, List<String>> multipartMap = formParamMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                e -> Optional.ofNullable(e.getValue())
                                        .map(Object::toString)
                                        .orElse(StrUtil.EMPTY),
                                Collectors.toList()
                        )
                ));

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }
        // 使用headers方法，它接受一个Consumer<HttpHeaders>
        return WEB_CLIENT.post()
                .uri(url)
                .headers(httpHeaders -> {
                    // 添加请求头
                    if (CollUtil.isNotEmpty(headers)) {
                        headers.forEach(httpHeaders::add);
                    }
                })
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromFormData(new LinkedMultiValueMap<>(multipartMap)))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> {
                    log.error("Remote service returned error status: {}", response.statusCode());
                    return response.bodyToMono(String.class)
                            .doOnNext(errorBody -> log.error("Error response body: {}", errorBody))
                            .flatMap(errorBody ->
                                    Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                            );
                })
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnError(e -> log.error("webClientPost request failed url {} params {} formParamMap {}", url, JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(formParamMap), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .doOnNext(res -> log.info("webClientPost for: url {} params {} formParamMap {} resp {}", url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(formParamMap), JacksonUtils.writeValueAsString(res)))
                .flatMap(result -> {
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    log.info("webClientPost result {}", JacksonUtils.writeValueAsString(resultData));
                    return Mono.just(resultData);
                })
                .switchIfEmpty(Mono.empty());

    }


    private enum BodyType {
        JSON {
            @Override
            public BodyInserter<?, ?> getBodyInserter(Object body) {
                return BodyInserters.fromValue(Optional.ofNullable(body).orElse(new HashMap<>()));
            }
        },
        FORM {
            @Override
            public BodyInserter<?, ?> getBodyInserter(Object body) {
                boolean b = body instanceof Map<?, ?>;
                if (!(b)) {
                    throw new IllegalArgumentException("form 表单只能传递 Map 类型参数");
                }
                Map<?, ?> formParamMap = (Map<?, ?>) body;
                Map<String, List<String>> multipartMap = formParamMap.entrySet().stream()
                        .collect(Collectors.groupingBy(
                                e -> e.getKey().toString(),
                                Collectors.mapping(
                                        e -> Optional.ofNullable(e.getValue())
                                                .map(Object::toString)
                                                .orElse(StrUtil.EMPTY),
                                        Collectors.toList()
                                )
                        ));

                return BodyInserters.fromFormData(new LinkedMultiValueMap<>(multipartMap));
            }
        };

        public abstract BodyInserter<?, ?> getBodyInserter(Object body);
    }


    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    private static class WebClientResult {
        private String code;
        private String msg;
        private String traceId;

        public boolean isSuccess() {
            return ResultCode.SUCCESS.getCode().equals(this.code);
        }

    }
}
