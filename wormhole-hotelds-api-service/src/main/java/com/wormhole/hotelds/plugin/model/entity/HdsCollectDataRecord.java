package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */

@Data
@Table("hds_collect_data_record")
public class HdsCollectDataRecord extends BaseEntity {
    private Long id;
    private String externalHotelId;
    private String collectType;
    private LocalDateTime collectTime;
    private String channel;
    private String platform;
}
