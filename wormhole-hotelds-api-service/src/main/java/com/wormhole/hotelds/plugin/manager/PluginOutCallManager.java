package com.wormhole.hotelds.plugin.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.config.CommonProperties;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.dao.PluginCallRecordDao;
import com.wormhole.hotelds.plugin.model.dto.PluginOutCallRecordAnalyzeDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginCallRecord;
import com.wormhole.hotelds.plugin.model.vo.PluginOutCallRecordAnalyzeVO;
import com.wormhole.hotelds.plugin.service.WorkflowExecutor;
import com.wormhole.task.model.entity.index.PluginOrderIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:52:27
 * @Description:
 */
@Component
@Slf4j
public class PluginOutCallManager {

//    private final ShuKeRobotApiClient shuKeRobotApiClient = new ShuKeRobotApiClient();

    @Resource
    private PluginCommentProperties pluginCommentProperties;

    @Resource
    private PluginCallRecordDao pluginCallRecordDao;

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private WorkflowExecutor workflowExecutor;

    /**
     * 处理外呼结果
     * 落库等处理
     */
//    public Mono<Boolean> executeOutCallMsg(ShuKeOutCallRecordDto shuKeOutCallRecordDto) {
//        shuKeOutCallRecordDto.get

//        List<ShuKeOutCallRecordDetailDto> recordDetail = shuKeOutCallRecordDto.getRecordDetail();
//        String recordDetailJson = JacksonUtils.writeValueAsString(recordDetail);
//        return analyzeOutCallRecord(
//                PluginOutCallRecordAnalyzeDTO.builder().content(recordDetailJson).build()
//        ).flatMap(analyzeVO -> {
//            String arrival = analyzeVO.getArrival();
//            String remark = analyzeVO.getRemark();
//
//
//        })
//        return null;
//    }


    /**
     * 外呼
     */
    public Mono<Boolean> doOutCall(Date date, List<PluginOrderIndex> orders) {
        List<Mono<Boolean>> monoList = orders
                .stream()
                .map(order -> this.doOutCall(date, order))
                .toList();
        // 使用 Mono.zip 组合所有 Mono 对象，只有当所有 Mono 都成功（即返回 true）时，整个 Mono 才会返回 true
        return Mono.zip(monoList, objects -> Arrays.stream(objects).allMatch(object -> (Boolean) object));
    }


    public Mono<Boolean> doOutCall(Date date, PluginOrderIndex order) {
//        PluginCallRecord pluginCallRecord = PluginCallRecord.init(order.getId(), order.getContactNumber(), date);
//        return pluginCallRecordDao.save(pluginCallRecord)
//                .flatMap(e -> shuKeRobotApiClient.uploadOutCallVirtualNumber(ordersToShuKeOutCallVirtualNumberReq(order))
//                        .map(resp -> {
//                            log.info("uploadOutCallVirtualNumber resp:{}", JacksonUtils.writeValueAsString(resp));
//                            return Boolean.TRUE;
//                        })
//                        .defaultIfEmpty(Boolean.FALSE)
//                        .doOnError(throwable -> log.error("uploadOutCallVirtualNumber error:{}", throwable.getMessage()))
//                )
//                .doOnError(throwable -> log.error("doOutCall error:{}", throwable.getMessage()))
//                ;
        return null;
    }

    public Mono<PluginOutCallRecordAnalyzeVO> analyzeOutCallRecord(PluginOutCallRecordAnalyzeDTO dto) {

        Map<String, Object> initialInput = Map.of(
                "USER_INPUT", dto.getContent());

        String workflowCode = commonProperties.getOutCallAnalyzeWorkFlowCode();
        return workflowExecutor.execute(workflowCode, initialInput)
                .flatMap(responseList -> {
                    return responseList.stream()
                            .filter(node -> "end".equals(node.getType()))
                            .findFirst()
                            .map(node -> Optional.ofNullable(node.getOutput())
                                    .map(output -> {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> map = (Map<String, Object>) output;
                                        Map<String, String> res = (Map<String, String>) map.get("output");
                                        PluginOutCallRecordAnalyzeVO analyzeVO = new PluginOutCallRecordAnalyzeVO();
                                        analyzeVO.setArrival(res.get("arrival"));
                                        analyzeVO.setRemark(res.get("remark"));
                                        return Optional.ofNullable(analyzeVO)
                                                .orElse(new PluginOutCallRecordAnalyzeVO());
                                    })
                                    .orElse(new PluginOutCallRecordAnalyzeVO()))
                            .map(Mono::just)
                            .orElse(Mono.just(new PluginOutCallRecordAnalyzeVO()));
                })
                .onErrorResume(e -> {
                    log.error("外呼调用工作流失败, error: {}", e.getMessage(), e);
                    return Mono.just(new PluginOutCallRecordAnalyzeVO());
                });
    }


//    private ShuKeOutCallVirtualNumberReq ordersToShuKeOutCallVirtualNumberReq(PluginOrderIndex order) {
//        PluginCommentProperties.ShuKe shuKe = pluginCommentProperties.getOutCall().getShuKe();
//
//        ShuKeOutCallVirtualNumberReq.FileData fileData = new ShuKeOutCallVirtualNumberReq.FileData();
//        fileData.setAsrId(shuKe.getAsrId());
//        fileData.setBatchId(shuKe.getBatchId());
//        fileData.setFiles(CollUtil.toList(orderToFileItemDto(order)));
//        fileData.setCallbackUrl(
//                StrUtil.isNotBlank(shuKe.getCallbackUrl()) ? shuKe.getCallbackUrl() : "#"
//        );
//        fileData.setDefine(order.getId());
//
//
//        ShuKeOutCallVirtualNumberReq req = new ShuKeOutCallVirtualNumberReq();
//        req.setFileData(fileData);
//        req.setToken(shuKe.getToken());
//
//        return req;
//    }
//
//    private ShuKeFileItemDto orderToFileItemDto(PluginOrderIndex order) {
//        ShuKeFileItemDto shuKeFileItemDto = new ShuKeFileItemDto();
//        shuKeFileItemDto.setName(order.getBookerName());
//        shuKeFileItemDto.setPhone(order.getContactNumber());
//        shuKeFileItemDto.setCompanyName(order.getHotelName());
//        return shuKeFileItemDto;
//    }

}
