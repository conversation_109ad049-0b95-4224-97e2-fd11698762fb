package com.wormhole.hotelds.plugin.model.qo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SentimentLabelsQO implements Serializable {

    private String startDateTime;

    private String endDateTime;

    private String hotelCode;

    private List<String> channels;

}
