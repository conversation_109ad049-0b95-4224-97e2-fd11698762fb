package com.wormhole.hotelds.plugin.repository;

import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 16:33
 */
public interface WpHotelCompetitorRepository extends ReactiveCrudRepository<HotelCompetitorEntity, Long> {

    Flux<HotelCompetitorEntity> findByHotelCodeAndRowStatus(String hotelCode, Integer rowStatus);

    Flux<HotelCompetitorEntity> findByHotelCodeAndSelectedStatusAndRowStatus(String hotelCode,Integer selectedStatus, Integer rowStatus);

    Flux<HotelCompetitorEntity> findByHotelCodeAndCompetitorHotelIdAndRowStatus(String hotelCode, String competitorHotelId, Integer rowStatus);

    Mono<Long> countByHotelCodeAndRowStatus(String hotelCode, Integer rowStatus);

    Mono<Long> countByHotelCodeAndAndSelectedStatusAndRowStatus(String hotelCode, Integer selectedStatus, Integer rowStatus);

    /**
     * 根据酒店代码、竞品酒店ID、渠道和状态检查是否存在
     */
    Mono<Boolean> existsByHotelCodeAndCompetitorHotelIdAndChannelAndRowStatus(String hotelCode, String competitorHotelId,
                                                                              String channel, Integer rowStatus);


    Flux<HotelCompetitorEntity> findByHotelCodeAndRowStatusAndChannelIn(
            String hotelCode,
            Integer rowStatus,
            Collection<String> channels
    );

    Mono<HotelCompetitorEntity> findByHotelCodeAndCompetitorHotelIdAndChannelAndRowStatus(String hotelCode, String competitorHotelId, String channel,Integer rowStatus);
}
