package com.wormhole.hotelds.plugin.dao;

import com.wormhole.task.model.entity.index.HotelPriceHistoryIndex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 */
@Repository
public class HotelPriceHistoryDao {
    @Autowired
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    public Mono<HotelPriceHistoryIndex> save(HotelPriceHistoryIndex hotelPriceHistoryIndex) {
        return reactiveElasticsearchTemplate.save(hotelPriceHistoryIndex);
    }


    public Mono<List<HotelPriceHistoryIndex>> saveAll(List<HotelPriceHistoryIndex> hotelPriceHistoryIndexList) {
        return reactiveElasticsearchTemplate.saveAll(hotelPriceHistoryIndexList, HotelPriceHistoryIndex.class).collectList();
    }
}
