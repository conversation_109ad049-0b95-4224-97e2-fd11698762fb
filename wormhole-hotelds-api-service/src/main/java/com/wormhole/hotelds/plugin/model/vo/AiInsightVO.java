package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentSentimentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
public class AiInsightVO {
    private String title; // 拼接生成的新字段
    private List<String> Labels; // 关键词标签列表
    private Long LabelCount; // 关键词出现次数
    private Double percentage; // 占本周比例 单位：%
    private String comparisonWithLastWeek; // 与上周对比
    private String commentAnalysis; //分析
    private List<String> commentSuggestion; //建议

    /**
     * @see PluginCommentSentimentEnum
     * 正面，负面
     */
    private String sentiment;

}
