package com.wormhole.hotelds.plugin.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-08 14:27:57
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginOutCallTabEnum {

    OUT_CALL_SUCCESS(1, "已接通"),

    OUT_CALL_FAIL(0, "未接通"),

    WAIT_OUT_CALL(2, "待外呼"),

    ;

    private final int value;

    private final String name;


    public static Optional<PluginOutCallTabEnum> fromValue(int value) {
        for (PluginOutCallTabEnum tabEnum : PluginOutCallTabEnum.values()) {
            if (tabEnum.value == value) {
                return Optional.of(tabEnum);
            }
        }
        return Optional.empty();
    }


}
