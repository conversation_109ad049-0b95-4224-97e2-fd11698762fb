package com.wormhole.hotelds.plugin.model.field;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum HotelCommentScoreField {

    ID("id", "主键ID"),
    ROW_STATUS("row_status", "行状态：1-有效，0-无效"),
    CREATED_BY("created_by", "创建人ID"),
    CREATED_BY_NAME("created_by_name", "创建人姓名"),
    UPDATED_BY("updated_by", "更新人ID"),
    UPDATED_BY_NAME("updated_by_name", "更新人姓名"),
    CREATED_AT("created_at", "创建时间"),
    UPDATED_AT("updated_at", "更新时间"),
    HOTEL_CODE("hotel_code", "酒店 code，关联酒店的标识"),
    HYGIENE_SCORE("hygiene_score", "卫生评分"),
    FACILITIES_SCORE("facilities_score", "设施评分"),
    SERVICE_SCORE("service_score", "服务评分"),
    ENVIRONMENT_SCORE("environment_score", "环境评分"),
    SCORE_COLLECTION_TIME("score_collection_time", "采集时间"),
    COMMENT_CHANNEL("comment_channel", "点评渠道"),
    COMMENT_PLATFORM("comment_platform", "平台");

    private final String column;
    private final String desc;

}