package com.wormhole.hotelds.plugin.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.agent.client.chat.response.vo.WorkflowNodeSimpleVO;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import com.wormhole.hotelds.api.hotel.util.LockUtils;
import com.wormhole.hotelds.plugin.config.CommonProperties;
import com.wormhole.hotelds.plugin.model.dto.PluginHotelAbbreviationDTO;
import com.wormhole.hotelds.plugin.model.entity.PluginHotelAbbreviationEntity;
import com.wormhole.hotelds.plugin.model.vo.HotelAbbreviationResultVO;
import com.wormhole.hotelds.plugin.util.WebClientUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class PluginHotelAbbreviationService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private CommonProperties commonProperties;
    @Resource
    private WorkflowExecutor workflowExecutor;


    @Resource
    private LockUtils lockUtils;

    private static final String API = "%s/debug/workflow";

    /**
     * 查询所有评论标签
     */
    public Mono<String> findAll(String channel) {
        // 构造查询条件
        Criteria criteria = Criteria.where("channel").is(channel);

        return r2dbcEntityTemplate.select(Query.query(criteria), PluginHotelAbbreviationEntity.class)
                .map(PluginHotelAbbreviationEntity::getHotelAbbreviation)  // 映射为 name 字段
                .collectList()  // 收集为一个 List<String>
                .map(names -> names.isEmpty() ? "" : String.join(",", names));  // 返回空字符串或拼接后的字符串
    }

    public Mono<String> findNameByChannelAndExtHotelId(String channel, String externalHotelId) {
        Criteria criteria = Criteria.where("channel").is(channel)
                .and("external_hotel_id").is(externalHotelId);
        Query query = Query.query(criteria);
        query.sort(Sort.by(Sort.Order.desc("id")));
        query.limit(1);
        return r2dbcEntityTemplate.select(query, PluginHotelAbbreviationEntity.class)
                .next()
                .map(PluginHotelAbbreviationEntity::getHotelAbbreviation);
    }


    public Mono<PluginHotelAbbreviationEntity> save(PluginHotelAbbreviationEntity entity) {
        // 设置默认值
        entity.setRowStatus(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.insert(PluginHotelAbbreviationEntity.class)
                .using(entity)
                .thenReturn(entity);
    }

    public Mono<List<PluginHotelAbbreviationEntity>> findByExternalHotelIdAndChannel(List<String> externalHotelIds, String channel) {
        Criteria criteria = Criteria.where("external_hotel_id").in(externalHotelIds)
                .and("channel").is(channel)
                .and("row_status").is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), PluginHotelAbbreviationEntity.class)
                .collectList();
    }

    public Mono<String> generateHotelAbbreviationName(PluginHotelAbbreviationDTO pluginHotelAbbreviationDTO) {
        String channel = pluginHotelAbbreviationDTO.getChannel();
        String externalHotelId = pluginHotelAbbreviationDTO.getExternalHotelId();
        String lockKey = String.format(RedisConstant.GENERATE_ABBREVIATION_HOTEL_NAME, channel, externalHotelId);
        long waitTimeMs = 10 * 1000;
        int leaseTimeMs = 10 * 1000;

        return lockUtils.tryLock(lockKey, waitTimeMs, leaseTimeMs, () -> {
            Mono<String> generateShortName = executeWorkFlow(pluginHotelAbbreviationDTO)
                    .flatMap(shortName -> {
                        // 插入简称到表中
                        PluginHotelAbbreviationEntity entity = new PluginHotelAbbreviationEntity();
                        entity.setExternalHotelId(pluginHotelAbbreviationDTO.getExternalHotelId());
                        entity.setChannel(pluginHotelAbbreviationDTO.getChannel());
                        entity.setPlatform(pluginHotelAbbreviationDTO.getPlatform());
                        entity.setHotelName(pluginHotelAbbreviationDTO.getHotelName());
                        entity.setHotelAbbreviation(shortName);
                        return this.save(entity)
                                .thenReturn(shortName);
                    });

            return findNameByChannelAndExtHotelId(channel, externalHotelId)
                    .switchIfEmpty(generateShortName);
        });
    }


    public Mono<String> executeWorkFlow(PluginHotelAbbreviationDTO pluginHotelAbbreviationDTO) {

        Map<String, Object> initialInput = Map.of(
                "external_hotel_id", pluginHotelAbbreviationDTO.getExternalHotelId(),
                "channel", pluginHotelAbbreviationDTO.getChannel(),
                "USER_INPUT", pluginHotelAbbreviationDTO.getHotelName()
        );

        String workflowCode = commonProperties.getHotelAbbreviationWorkFlowCode();
        return workflowExecutor.execute(workflowCode, initialInput)
                .flatMap(responseList -> {
                    return responseList.stream()
                            .filter(node -> "end".equals(node.getType()))
                            .findFirst()
                            .map(node -> Optional.ofNullable(node.getOutput())
                                    .map(output -> {
                                        @SuppressWarnings("unchecked")
                                        Map<String, String> map = (Map<String, String>) output;
                                        return Optional.ofNullable(map.get("output"))
                                                .orElse("No abbreviation found");
                                    })
                                    .orElse("Output is null"))
                            .map(Mono::just)
                            .orElse(Mono.just("No valid end node found"));
                })
                .onErrorResume(e -> {
                    log.error("处理酒店名称缩写工作流时发生错误, error: {}", e.getMessage(), e);
                    return Mono.just("Error executing workflow: " + e.getMessage());
                });
    }

    public Mono<List<HotelAbbreviationResultVO>> batchGenerateHotelAbbreviationName(List<PluginHotelAbbreviationDTO> dtoList) {
        return Flux.fromIterable(dtoList)
                .flatMap(dto -> {
                    Mono<String> abbreviationMono = this.generateHotelAbbreviationName(dto);
                    return abbreviationMono.map(abbreviation ->
                            new HotelAbbreviationResultVO(
                                    dto.getExternalHotelId(),
                                    dto.getHotelName(),
                                    abbreviation
                            )
                    );
                })
                .collectList();
    }
}
