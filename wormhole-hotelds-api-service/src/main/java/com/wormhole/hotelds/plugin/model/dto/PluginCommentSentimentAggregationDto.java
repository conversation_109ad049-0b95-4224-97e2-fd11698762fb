package com.wormhole.hotelds.plugin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginCommentSentimentAggregationDto {
    private Map<String, Long> sentimentDistribution;
    private Long totalSentimentCount;
    private Double avgSentimentScore;
    private Map<String, Double> sentimentProportion;
}
