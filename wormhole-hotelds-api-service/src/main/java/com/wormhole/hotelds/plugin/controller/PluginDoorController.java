package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.service.PluginCommentLabelService;
import com.wormhole.task.model.entity.PluginCommentLabelEntity;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/plugin/door")
public class PluginDoorController {
    @Resource
    private PluginCommentLabelService pluginCommentLabelService;

    @PostMapping("/saveLabel")
    public Mono<Result<PluginCommentLabelEntity>> saveLabel(@RequestBody PluginCommentLabelEntity entity) {
        return pluginCommentLabelService.saveLabel(entity).flatMap(Result::success);
    }

}
