package com.wormhole.hotelds.plugin.controller;

import com.wormhole.common.result.Result;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsMonthReportVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTodayReportVO;
import com.wormhole.hotelds.plugin.service.PluginCommentStatisticsService;
import com.wormhole.hotelds.plugin.service.PluginOrderStatisticsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = "/plugin/statistics/order")
public class PluginOrderStatisticsController {

    @Resource
    private PluginOrderStatisticsService pluginOrderStatisticsService;

    @PostMapping("/report/today")
    public Mono<Result<PluginOrderTodayReportVO>> todayReport(@RequestBody PluginOrderQo params) {
        return pluginOrderStatisticsService.searchTodayReportData(params).flatMap(Result::success);
    }

}
