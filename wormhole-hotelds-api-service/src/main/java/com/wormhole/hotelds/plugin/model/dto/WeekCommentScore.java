package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 周度评分数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WeekCommentScore {
    /**
     * 每周评分
     */
    private List<ScoreItem> weeklyScore;
    
    /**
     * 平均分
     */
    private Double avgScore;

    public WeekCommentScore(List<ScoreItem> weeklyScore) {
        this.weeklyScore = weeklyScore;

        this.avgScore = weeklyScore.stream().mapToDouble(ScoreItem::getValue).average().orElse(0);
    }
}