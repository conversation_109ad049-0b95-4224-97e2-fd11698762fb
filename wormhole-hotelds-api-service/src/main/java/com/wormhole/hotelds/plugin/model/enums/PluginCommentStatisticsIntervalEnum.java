package com.wormhole.hotelds.plugin.model.enums;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-05-20 09:59:46
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PluginCommentStatisticsIntervalEnum {

    MONTH(1, "月度", DateField.MONTH) {
        @Override
        public String calcIntervalEnd(String IntervalStart) {
            DateTime dateTime = DateUtil.parseDate(IntervalStart);
            return DateUtil.endOfMonth(dateTime).toDateStr();
        }
    },
    WEEK(2, "周度", DateField.WEEK_OF_YEAR) {
        @Override
        public String calcIntervalEnd(String IntervalStart) {
            DateTime dateTime = DateUtil.parseDate(IntervalStart);
            return DateUtil.endOfWeek(dateTime, true).toDateStr();
        }
    },
    ;

    private final int value;

    private final String desc;

    private final DateField dateField;


    public abstract String calcIntervalEnd(String IntervalStart);


    public static Optional<PluginCommentStatisticsIntervalEnum> ofValue(Integer value) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(value, e.getValue()))
                .findFirst();
    }
}
