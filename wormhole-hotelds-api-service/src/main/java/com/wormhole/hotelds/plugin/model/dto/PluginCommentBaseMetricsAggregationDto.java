package com.wormhole.hotelds.plugin.model.dto;

import co.elastic.clients.elasticsearch._types.aggregations.RangeBucket;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-12 17:06:08
 * @Description: 点评 基础指标 es聚合查询的dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginCommentBaseMetricsAggregationDto {
    /**
     * 总评论数
     */
    private Long totalCommentCount;

    /**
     * 评分 分布 && 每个分区的回复数
     */
    private List<CommentScoreRangeBucket> commentScoreRangeList;

    /**
     * 平均分
     */
    private Double avgCommentScore;

    /**
     * 环境分
     */
    private List<RangeBucketDto> environmentScoreRangeList;

    private Double avgEnvironmentScore;

    private List<RangeBucketDto> facilitiesScoreRangeList;

    private Double avgFacilitiesScore;

    private List<RangeBucketDto> hygieneScoreRangeList;

    private Double avgHygieneScore;

    private List<RangeBucketDto> commentLengthRangeList;

    private List<RangeBucketDto> timeToCommentRangeList;

    private List<RangeBucketDto> timeToReplyRangeList;

    private Long totalReplyCount;

    private Double avgReplyRelevance;

    private Double avgReplyLength;

    /**
     * 逾期未回复数
     */
    private Long overdueNotReplyCount;

    /**
     * 逾期回复数(有回复，但是逾期回复)
     */
    private Long overdueReplyCount;

    @EqualsAndHashCode(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CommentScoreRangeBucket extends RangeBucketDto {
        private Long replyCount;

        public CommentScoreRangeBucket(RangeBucket rangeBucket) {
            super(rangeBucket);
        }
    }

}
