package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.knowledge.model.dto.OperatorInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 主数据实体类
 * <AUTHOR>
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelCommentDTO {
    
    /**
     * 平台
     */
    private String platform;
    
    /**
     * 渠道
     */
    private String channel;
    
    /**
     * 竞争对手酒店ID
     */
    private String competitorHotelId;
    /**
     * 是否是内部酒店
     */
    private boolean innerHotel = false;

    
    /**
     * 评论信息
     */

    private List<CommentInfoV1> commentInfoV1s;

    private List<CommentInfoV2> commentInfoV2s;

    private OperatorInfo operatorInfo;

    private boolean lastPage = false;



    @Data
    public static class CommentInfoV2 {
        private Long id;
        private Long hotelId;
        private Integer usefulCount;
        private Integer source;
        private String language;
        private Boolean canMarkUseful;
        private String checkInDate;
        private String content;
        private String createDate;
        private Integer rating;
        private String commentLevel;
        private String roomTypeName;
        private Integer travelType;
        private String travelTypeText;
        private List<FeedbackInfo> feedbackList;
        private UserInfo userInfo;
        private RatingInfo ratingInfo;
    }

    @Data
    public static class FeedbackInfo {
        private String content;
        private String language;
        private Integer type;
        private String createDate;
        private String ipLocation;
    }

    @Data
    public static class UserInfo {
        private String headPictureUrl;
        private Integer commentCount;
        private String nickName;
    }
    @Data
    public static class RatingInfo {
        private String commentLevel;
        private String ratingAll;
        private Integer ratingMax;
    }


        /**
     * 评论信息实体类
     */
    @Data
    public static class CommentInfoV1 {
        
        /**
         * 用户资料
         */
        private UserProfile userProfile;
        
        /**
         * 评论详情
         */
        private ReviewDetails reviewDetails;
        
        /**
         * 评论ID
         */
        private String reviewId;
        
        /**
         * 有用数量
         */
        private Integer helpfulCount;
        
        /**
         * 标记为有用
         */
        private Boolean markUseful;
        
        /**
         * IP位置
         */
        private String ipLocation;
    }
    
    /**
     * 用户资料实体类
     */
    @Data
    public static class UserProfile {
        
        /**
         * 头像URL（小尺寸）
         */
        private String avatarUrl;
        
        /**
         * 用户名
         */
        private String userName;
        
        /**
         * 已点评数量
         */
        private Integer reviewedCount;
        
        /**
         * 头像URL（大尺寸）
         */
        private String headPictureUrl;
    }
    
    /**
     * 评论详情实体类
     */
    @Data
    public static class ReviewDetails {
        
        /**
         * 评分信息
         */
        private ReviewScore reviewScore;
        
        /**
         * 评论内容
         */
        private String reviewContent;
        
        /**
         * 发布日期
         */
        private String releaseDate;
        
        /**
         * 旅行类型
         */
        private String travelType;
        
        /**
         * 房间类型
         */
        private String roomType;
        
        /**
         * 入住日期
         */
        private String checkInDate;
        
//        /**
//         * 评论更新的图片列表
//         */
//        private List<String> reviewUpdateImages;
        
        /**
         * 反馈列表
         */
        private List<FeedbackItem> feedbackList;
        
        /**
         * 来源文本
         */
        private String sourceText;
        
        /**
         * 语言
         */
        private String language;
        
        /**
         * 来源
         */
        private Integer source;
    }
    
    /**
     * 评分信息实体类
     */
    @Data
    public static class ReviewScore {
        
        /**
         * 评分
         */
        private String score;
        
        /**
         * 最高评分
         */
        private String scoreMax;
        
        /**
         * 评分描述
         */
        private String scoreDescription;
    }
    
    /**
     * 反馈项实体类
     */
    @Data
    public static class FeedbackItem {
        
        /**
         * 评论ID
         */
        private String reviewId;
        
        /**
         * 类型
         */
        private Integer type;
        
        /**
         * 创建日期
         */
        private String createDate;
        
        /**
         * 内容
         */
        private String content;
        
        /**
         * IP位置
         */
        private String ipLocation;
        
        /**
         * 语言
         */
        private String language;
    }
}