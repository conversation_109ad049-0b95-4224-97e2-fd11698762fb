package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@AllArgsConstructor
@NoArgsConstructor
public class CommentTagAnalysisDTO {
    private Long id;

    /**
     * 点评主键ID（外键或冗余主键）
     */
    private Long commentId;

    private String channel;

    private String hotelCode;

    private List<LabelDetailDTO> labelDetail;
}
