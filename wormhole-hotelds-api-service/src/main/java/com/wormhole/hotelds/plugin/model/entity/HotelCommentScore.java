package com.wormhole.hotelds.plugin.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("plugin_hotel_comment_score")
public class HotelCommentScore extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 酒店 code，关联酒店的标识
     */
    private String hotelCode;
    /**
     * 平均分
     */
    private BigDecimal averageScore;
    /**
     * 最大分
     */
    private Integer scoreMax;

    private Long totalReviewsCount;



    /**
     * 卫生评分
     */
    private BigDecimal hygieneScore;

    /**
     * 设施评分
     */
    private BigDecimal facilitiesScore;

    /**
     * 服务评分
     */
    private BigDecimal serviceScore;

    /**
     * 环境评分
     */
    private BigDecimal environmentScore;

    /**
     * 采集时间
     */
    private LocalDateTime scoreCollectionTime;

    /**
     * 点评渠道
     */
    private String commentChannel;

    /**
     * 平台
     */
    private String commentPlatform;

}