package com.wormhole.hotelds.plugin.model.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@Table(name = "plugin_comment_reply_prefix_content")
public class PluginCommentReplyPrefixContentEntity {

    @Id
    @Column("id")
    private Long id;

    private Integer rowStatus;

    private String createdBy;

    private String createdByName;

    private String updatedBy;

    private String updatedByName;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String hotelCode;

    private String replyStyle;

    private Integer onlyPositive;

    private String replyPrefixContent;

}
