package com.wormhole.hotelds.plugin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.util.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-14 10:44:57
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginCommentAdvancedMetricsVo {


    private String startDate;

    private String endDate;

    private String hotelCode;


    private Long totalSentimentCount;

    private Double avgSentimentScore;


    private List<SentimentDistributionDto> sentimentDistributionList;

    /**
     * 标签 ->  个数
     */
    private List<Pair<String, Long>> positiveTagsTopList;

    private List<Pair<String, Long>> neutralTagsTopList;

    private List<Pair<String, Long>> negativeTagsTopList;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SentimentDistributionDto {
        private String sentiment;

        private String sentimentDesc;

        private Long count;

        private String proportion;


    }
}
