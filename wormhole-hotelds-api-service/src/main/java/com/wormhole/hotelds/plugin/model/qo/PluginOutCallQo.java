package com.wormhole.hotelds.plugin.model.qo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-09 13:56:20
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginOutCallQo {

    private String hotelCode;

    private List<String>  orderNoList;

    private Integer tabType;
}
