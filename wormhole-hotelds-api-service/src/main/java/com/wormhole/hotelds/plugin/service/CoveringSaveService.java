package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.dao.HotelSimpleCommentDao;
import com.wormhole.hotelds.plugin.model.dto.SaveEntityDTO;
import com.wormhole.hotelds.plugin.model.dto.SaveIndexDTO;
import com.wormhole.hotelds.plugin.model.qo.SimpleCommentQO;
import com.wormhole.task.model.entity.*;
import com.wormhole.task.model.entity.index.PluginCommentIndex;
import com.wormhole.task.model.entity.index.PluginSimpleCommentIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
@Slf4j
public class CoveringSaveService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private ConversionService conversionService;
    @Resource
    private CommentLabelAnalysisService commentLabelAnalysisService;
    @Resource
    private CommentSentimentAnalysisService commentSentimentAnalysisService;
    @Resource
    private PluginCommentEntityService pluginCommentEntityService;
    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;
    @Resource
    private PluginCommentTagService pluginCommentTagService;
    @Autowired
    private HotelSimpleCommentDao hotelSimpleCommentDao;

    public Mono<List<Object>> saveByOverwrite(SaveEntityDTO saveEntityDTO) {
        log.info("saveByOverwrite saveEntityDTO: {}", saveEntityDTO);
        try {
            Class<?> dtoClass = Class.forName(saveEntityDTO.getDtoType());
            Object dto = JacksonUtils.readValue(saveEntityDTO.getJson(), dtoClass);
            log.info("saveByOverwrite dto: {}", dto);

            List<Object> entities = conversionService.convert(dto);
            log.info("saveByOverwrite entities: {}", entities);
            if (entities.isEmpty()) {
                log.error("No entities generated from DTO");
                return Mono.error(new RuntimeException("No entities generated from DTO"));
            }

            Class<?> entityClass = entities.get(0).getClass();
            Map<String, Object> queryFields = parseQueryFields(saveEntityDTO.getQueryFields());
            log.info("saveByOverwrite queryFields: {}", queryFields);

            if (queryFields == null || queryFields.isEmpty()) {
                log.error("QueryFields is empty or invalid;saveEntityDTO: {}", saveEntityDTO);
                return Mono.error(new RuntimeException("QueryFields is empty or invalid"));
            }

            Criteria criteria = Criteria.empty();
            for (Map.Entry<String, Object> entry : queryFields.entrySet()) {
                criteria = criteria.and(entry.getKey()).is(entry.getValue());
            }

            Query deleteQuery = Query.query(criteria);

            return r2dbcEntityTemplate.delete(entityClass)
                    .matching(deleteQuery)
                    .all()
                    .thenMany(Flux.fromIterable(entities)
                            .flatMap(r2dbcEntityTemplate::insert)
                    ).collectList();

        } catch (Exception e) {
            log.error("Error during saveByOverwrite saveEntityDTO: {} ",saveEntityDTO, e);

            return Mono.error(new RuntimeException("DTO转换或保存失败", e));
        }
    }

    public Mono<List<PluginCommentIndex>> saveIndex(SaveIndexDTO saveEntityDTO) {
        String commentId = saveEntityDTO.getCommentId();
        Mono<List<CommentLabelAnalysisEntity>> labelMono = commentLabelAnalysisService.getByCommentId(commentId);
        Mono<CommentSentimentAnalysisEntity> sentimentMono = commentSentimentAnalysisService.getByCommentId(commentId);
        Mono<CommentEntity> commentMono = pluginCommentEntityService.findById(commentId);

        return Mono.zip(labelMono, sentimentMono, commentMono)
                .flatMap(tuple -> {
                    List<CommentLabelAnalysisEntity> labels = tuple.getT1();
                    CommentSentimentAnalysisEntity sentimentAnalysis = tuple.getT2();
                    CommentEntity commentEntity = tuple.getT3();

                    //构建索引的基本信息
                    PluginCommentIndex index = buildIndex(commentEntity);

                    if (labels.isEmpty()) {
                        log.warn("No labels found for commentId={}", commentId);
                        return Mono.just(List.of(index)); // 返回空索引数据
                    }

                    //标签分析
                    return Flux.fromIterable(labels)
                            .flatMap(labelEntity ->
                                    pluginCommentTagService.findLeafAndParent(labelEntity.getLabel())
                                            .onErrorResume(e -> {
                                                log.error("Error finding leaf and parent tags for label: {}", labelEntity.getLabel(), e);
                                                return Mono.just(Collections.emptyList()); // 返回空列表以避免中断
                                            })
                                            .map(parents -> {
                                                List<PluginCommentIndex.Label> allLabels = new ArrayList<>();
                                                for (PluginCommentLabelEntity tag : parents) {
                                                    allLabels.add(new PluginCommentIndex.Label(tag.getId(), tag.getLevel(), tag.getName()));
                                                }
                                                return new PluginCommentIndex.LabelAnalysis(
                                                        allLabels,
                                                        labelEntity.getSentiment(),
                                                        labelEntity.getScore() != null ? labelEntity.getScore().floatValue() : 0f
                                                );
                                            })
                            )
                            .collectList()
                            .flatMap(labelAnalysisList -> {
                                index.setLabelAnalyses(labelAnalysisList);

                                // 情感分析
                                PluginCommentIndex.SentimentAnalysis sentiment = PluginCommentIndex.SentimentAnalysis.builder()
                                        .sentiment(sentimentAnalysis.getSentiment())
                                        .score(sentimentAnalysis.getScore().floatValue())
                                        .mainFocus(sentimentAnalysis.getMainFocus())
                                        .build();
                                index.setSentimentAnalysis(sentiment);

                                return reactiveElasticsearchTemplate.save(index).thenReturn(List.of(index));
                            });
                }).onErrorResume(e -> {
                    log.error("Error saving index: ", e);
                    return Mono.error(new RuntimeException("Index保存失败", e));
                });
    }


    public Mono<List<PluginSimpleCommentIndex>> saveSimpleCommentIndex(SaveIndexDTO saveEntityDTO) {
        String commentId = saveEntityDTO.getCommentId();
        Mono<List<CommentLabelAnalysisEntity>> labelMono = commentLabelAnalysisService.getByCommentId(commentId);
        SimpleCommentQO simpleCommentQO = SimpleCommentQO.builder().id(Long.parseLong(commentId)).build();
        Mono<SimpleCommentEntity> commentMono = hotelSimpleCommentDao.findOne(simpleCommentQO);

        return Mono.zip(labelMono, commentMono)
                .flatMap(tuple -> {
                    List<CommentLabelAnalysisEntity> labels = tuple.getT1();
                    SimpleCommentEntity commentEntity = tuple.getT2();

                    //构建索引的基本信息
                    PluginSimpleCommentIndex index = buildSimpleCommentIndex(commentEntity);

                    //标签分析
                    return Flux.fromIterable(labels)
                            .flatMap(labelEntity ->
                                    pluginCommentTagService.findLeafAndParent(labelEntity.getLabel())
                                            .map(parents -> {
                                                List<PluginCommentIndex.Label> allLabels = new ArrayList<>();
                                                for (int i = 0; i < parents.size(); i++) {
                                                    PluginCommentLabelEntity tag = parents.get(i);
                                                    allLabels.add(new PluginCommentIndex.Label(
                                                            tag.getId(),
                                                            tag.getLevel(),
                                                            tag.getName()
                                                    ));
                                                }
                                                return new PluginCommentIndex.LabelAnalysis(
                                                        allLabels,
                                                        labelEntity.getSentiment(),
                                                        labelEntity.getScore() != null ? labelEntity.getScore().floatValue() : 0f
                                                );
                                            })
                            )
                            .collectList()
                            .flatMap(labelAnalysisList -> {
                                index.setLabelAnalyses(labelAnalysisList);
                                return reactiveElasticsearchTemplate.save(index).thenReturn(List.of(index));
                            });
                }).onErrorResume(e -> {
                    log.error("saveSimpleCommentIndex error: ", e);
                    return Mono.error(new RuntimeException("Index保存失败", e));
                });
    }

    private PluginSimpleCommentIndex buildSimpleCommentIndex(SimpleCommentEntity commentEntity) {
        String commentId = commentEntity.getId().toString();
        PluginSimpleCommentIndex index = new PluginSimpleCommentIndex();

        // 设置基本信息
        index.setId(commentId);
        index.setCommentId(commentId);
        index.setExternalHotelId(commentEntity.getExternalHotelId());
        index.setCommentChannel(commentEntity.getCommentChannel());
        index.setCommentPlatform(commentEntity.getCommentPlatform());


        index.setCreatedAt(Instant.now());
        index.setCreatedBy(commentEntity.getCreatedBy());
        index.setCreatedByName(commentEntity.getCreatedByName());

        index.setUpdatedAt(Instant.now());
        index.setUpdatedBy(commentEntity.getUpdatedBy());
        index.setUpdatedByName(commentEntity.getUpdatedByName());

        // 设置评论相关信息
        index.setUsername(commentEntity.getUsername());
        index.setCheckInDate(commentEntity.getCheckInDate());

        // 设置评论日期（直接存储字符串，因为PluginSimpleCommentIndex中commentDate是String类型）
        index.setCommentDate(commentEntity.getCommentDate().atStartOfDay(ZoneOffset.systemDefault()).toInstant());
        index.setHotelReplyContent(commentEntity.getHotelReplyContent());
        index.setReplyDate(commentEntity.getReplyDate().atStartOfDay(ZoneOffset.systemDefault()).toInstant());

        // 设置房型信息
        index.setRoomType(commentEntity.getRoomType());

        // 设置评论内容和长度
        index.setCommentContent(commentEntity.getCommentContent());
        index.setCommentLength(commentEntity.getCommentContent() != null ?
                commentEntity.getCommentContent().length() : 0);

        // 设置评分
        if (commentEntity.getCommentScore() != null) {
            index.setCommentScore(commentEntity.getCommentScore().floatValue());
        }

        // 设置时间到评论的时长（没有明确的计算逻辑，默认为0）
        index.setTimeToComment(0);

        return index;
    }

    private PluginCommentIndex buildIndex(CommentEntity commentEntity) {
        String commentId = commentEntity.getId().toString();
        PluginCommentIndex index = new PluginCommentIndex();
        index.setId(commentId);
        index.setCommentId(commentId);
        index.setHotelCode(commentEntity.getHotelCode());
        index.setCommentChannel(commentEntity.getCommentChannel());

        if (commentEntity.getCreatedAt() != null) {
            index.setCreatedAt(commentEntity.getCreatedAt().atZone(ZoneOffset.UTC).toInstant());
        }
        index.setCreatedBy(commentEntity.getCreatedBy());
        index.setCreatedByName(commentEntity.getCreatedByName());
        if (commentEntity.getUpdatedAt() != null) {
            index.setUpdatedAt(commentEntity.getUpdatedAt().atZone(ZoneOffset.UTC).toInstant());
        }
        index.setUpdatedBy(commentEntity.getUpdatedBy());
        index.setUpdatedByName(commentEntity.getUpdatedByName());
        index.setCheckInDate(commentEntity.getCheckInDate());
        if (commentEntity.getCommentDate() != null) {
            index.setCommentDate(commentEntity.getCommentDate().atZone(ZoneOffset.UTC).toInstant());
        }
        index.setCommentContent(commentEntity.getCommentContent());
        index.setCommentLength(commentEntity.getCommentContent().length());
        index.setHotelReplyContent(commentEntity.getHotelReplyContent());
        if (commentEntity.getReplyDate() != null) {
            index.setReplyDate(commentEntity.getReplyDate().atZone(ZoneOffset.UTC).toInstant());
        }
        index.setReplyDepartment(commentEntity.getReplyDepartment());
        index.setReplyStaff(commentEntity.getReplyStaff());
        index.setReplyLength(commentEntity.getHotelReplyContent() == null ? 0 : commentEntity.getHotelReplyContent().length());
        index.setReplyRelevance(100);
        index.setTimeToComment(0);
        index.setTimeToReply(commentEntity.getReplyDate() != null && commentEntity.getCommentDate() != null
                ? (int) ChronoUnit.HOURS.between(commentEntity.getCommentDate(), commentEntity.getReplyDate())
                : 0);
        index.setRoomType(commentEntity.getRoomType());
        index.setPlatform(commentEntity.getPlatform());
        index.setUsername(commentEntity.getUsername());
        index.setScoreable(commentEntity.getScoreable() == 1);

        if (commentEntity.getCommentScore() != null) {
            index.setCommentScore(commentEntity.getCommentScore().floatValue());
        }
        if (commentEntity.getEnvironmentScore() != null) {
            index.setEnvironmentScore(commentEntity.getEnvironmentScore().floatValue());
        }
        if (commentEntity.getFacilitiesScore() != null) {
            index.setFacilitiesScore(commentEntity.getFacilitiesScore().floatValue());
        }
        if (commentEntity.getServiceScore() != null) {
            index.setServiceScore(commentEntity.getServiceScore().floatValue());
        }
        if (commentEntity.getHygieneScore() != null) {
            index.setHygieneScore(commentEntity.getHygieneScore().floatValue());
        }
        return index;
    }


    private Map<String, Object> parseQueryFields(String json) {
        try {
            return JacksonUtils.readValue(json, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("Failed to parse queryFields", e);
            return Collections.emptyMap();
        }
    }
}
