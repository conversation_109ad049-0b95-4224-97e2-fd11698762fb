package com.wormhole.hotelds.plugin.dao;

import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.RedisConstant;
import com.wormhole.hotelds.plugin.model.dto.ScanTimeWindowDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Repository
@Slf4j
public class HotelCommentRedisDao {

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;


    public Mono<Boolean> saveInfo(String hotelId, ScanTimeWindowDTO scanTimeWindowDTO) {
        String key = String.format(RedisConstant.HOTEL_COMMENT_SCAN_KEY, hotelId);
        log.info("保存酒店评论扫描窗口信息：{},json:{}", key, JacksonUtils.writeValueAsString(scanTimeWindowDTO));
        return reactiveStringRedisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(scanTimeWindowDTO));
    }


    public Mono<ScanTimeWindowDTO> getScanTimeWindowDTO(String hotelId) {
        String key = String.format(RedisConstant.HOTEL_COMMENT_SCAN_KEY, hotelId);
        return reactiveStringRedisTemplate.opsForValue().get(key).flatMap(json ->{
            log.info("获取酒店评论扫描窗口信息：{},json:{}", key,json);
            if(StringUtils.isBlank(json)){
                return Mono.empty(); // 流正常完成，但没有数据
            }
            return Mono.just(JacksonUtils.readValue(json, ScanTimeWindowDTO.class));
        });
    }

}
