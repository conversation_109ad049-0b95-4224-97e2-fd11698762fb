package com.wormhole.hotelds.plugin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2025-05-12 16:00:02
 * @Description:
 */
@ConfigurationProperties(prefix = "wormhole.plugin.comment")
@Data
@RefreshScope
public class PluginCommentProperties {

    /**
     * 月度报表数据比较过去月份
     */
    private Integer monthsToCompare = 6;

    /**
     * 周报数据比较过去 4周
     */
    private Integer weeksToCompare = 4;

    private Integer yearToCompare = 1;


    private Integer priceCollectionInterval = 60;

    @NestedConfigurationProperty
    private OutCall outCall;

    @Data
    public static class OutCall {

        @NestedConfigurationProperty
        private ShuKe shuKe;

    }


    @Data
    public static class ShuKe {

        private String token = "98ae481b8df211eb900c00163e0ec7ee";

        private String domain = "https://ai21.ytcall.net";

        private Integer asrId = 647;

        private Integer batchId = 30918;

        private String callbackUrl = "https://dev-gateway.bwagent.net/hotelds-api/plugin/out_call/shu_ke/callback";


    }

}
