package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.util.NamedValue;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.hotelds.plugin.config.PluginCommentProperties;
import com.wormhole.hotelds.plugin.model.dto.*;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentSearchParams;
import com.wormhole.hotelds.plugin.model.param.PluginSimpleCommentSearchParams;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentVO;
import com.wormhole.task.model.entity.filed.PluginCommentIndexField;
import com.wormhole.task.model.entity.filed.PluginSimpleCommentIndexField;
import com.wormhole.task.model.entity.index.PluginCommentIndex;
import com.wormhole.task.model.entity.index.PluginSimpleCommentIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.client.elc.*;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PluginCommentIndexService {

    private static final Double negativeReviewsScore = 4.0;

    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;

    @Resource
    private PluginCommentProperties pluginCommentProperties;

    /**
     * 保存单个分片
     *
     * @param pluginCommentIndex
     * @return
     */
    public Mono<PluginCommentIndex> save(PluginCommentIndex pluginCommentIndex) {
        return reactiveElasticsearchTemplate.save(pluginCommentIndex);
    }

    /**
     * 批量保存分片列表
     *
     * @param pluginCommentIndices
     * @return
     */
    public Flux<PluginCommentIndex> saveAll(List<PluginCommentIndex> pluginCommentIndices) {
        return reactiveElasticsearchTemplate.saveAll(pluginCommentIndices, PluginCommentIndex.class);
    }

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    public Mono<String> deleteById(String id) {
        return reactiveElasticsearchTemplate.delete(id, PluginCommentIndex.class);
    }

    /**
     * 根据主键批量删除，根据refresh确定删除后立即刷新索引
     *
     * @param idList
     * @param refresh
     * @return
     */
    public Mono<ByQueryResponse> delete(List<String> idList, boolean refresh) {
        Criteria criteria = Criteria.where(PluginCommentIndexField.ID.getColumn()).in(idList);
        CriteriaQuery criteriaQuery = new CriteriaQueryBuilder(criteria).build();
        DeleteQuery deleteQuery = DeleteQuery.builder(criteriaQuery).withRefresh(refresh).build();
        return reactiveElasticsearchTemplate.delete(deleteQuery, PluginCommentIndex.class);
    }

    /**
     * 根据知识库id和code检索分片列表
     *
     * @param KnowledgeCode 知识库id
     * @param bizCode       业务code
     * @return
     */
    public Flux<PluginCommentVO> search() {
        return Flux.empty();
    }


    /**
     * 构建内容搜索查询
     *
     * @param boolQueryBuilder 查询构建器
     * @param searchParams     搜索参数
     */
    private void buildContentQuery(BoolQuery.Builder boolQueryBuilder, PluginCommentSearchParams searchParams) {
        if (Objects.nonNull(searchParams.getStartDateTime()) && Objects.nonNull(searchParams.getEndDateTime())) {
            boolQueryBuilder.filter(Querys.range("comment_date", searchParams.getStartDateTime(), searchParams.getEndDateTime()));
        }

        if (CollUtil.isNotEmpty(searchParams.getChannels())) {
            boolQueryBuilder.filter(Querys.terms("comment_channel", searchParams.getChannels()));
        }

        if (StrUtil.isNotEmpty(searchParams.getHotelCode())) {
            boolQueryBuilder.filter(Querys.term("hotel_code", searchParams.getHotelCode()));
        }
    }

    public Mono<PluginCommentCountIntervalStatsDto> countCommentCountsByInterval(PluginCommentSearchParams params, PluginCommentStatisticsIntervalEnum interval) {
        Map<String, Aggregation> aggregationMap = new HashMap<>();

        aggregationMap.put(
                "interval_comment_counts", Aggregation.of(a ->
                        a.dateHistogram(dh ->
                                        dh.field(PluginCommentIndexField.COMMENT_DATE.getColumn())
                                                .calendarInterval(ofCalendarInterval(interval.name()))
                                                .format(DatePattern.NORM_DATE_PATTERN)
                                                .minDocCount(1)
                                )
                                .aggregations("count_ranges", Aggregation.of(countRanges ->
                                                countRanges.range(rangeAgg ->
                                                        rangeAgg.field(PluginCommentIndexField.COMMENT_SCORE.getColumn())
                                                                .ranges(
                                                                        AggregationRange.of(ar ->
                                                                                ar.from(0D).to(4D).key("negative_comment")
                                                                        )
                                                                        , AggregationRange.of(ar ->
                                                                                ar.from(4D).to(10D).key("positive_comment")
                                                                        )
                                                                )
                                                )
                                        )
                                )

                )
        );


        return this.aggregation(builder -> buildBaseQueryCondition(builder, params), aggregationMap, this::toTimeIntervalStatsDto)
                .map(intervals -> PluginCommentCountIntervalStatsDto.createWithIntervals(interval, intervals));
    }


    public Mono<PluginCommentScoreIntervalStatsDto> avgCommentScoresByInterval(PluginCommentSearchParams params, PluginCommentStatisticsIntervalEnum interval) {
        Map<String, Aggregation> aggregationMap = new HashMap<>();
        aggregationMap.put(
                "interval_comment_scores", Aggregation.of(a ->
                        a.dateHistogram(dh ->
                                        dh.field(PluginCommentIndexField.COMMENT_DATE.getColumn())
                                                .calendarInterval(ofCalendarInterval(interval.name()))
                                                .format(DatePattern.NORM_DATE_PATTERN)
                                                .minDocCount(1)
                                )
                                .aggregations("avg_score", Aggregation.of(avgAgg ->
                                                avgAgg.avg(avg -> avg.field(PluginCommentIndexField.COMMENT_SCORE.getColumn()))
                                        )
                                )
                )
        );

        return this.aggregation(builder -> buildBaseQueryCondition(builder, params), aggregationMap, this::toIntervalScoreStatsDto)
                .map(intervals -> PluginCommentScoreIntervalStatsDto.createWithIntervals(interval, intervals));
    }

    private CalendarInterval ofCalendarInterval(String interval) {
        Optional<CalendarInterval> first = Arrays.stream(CalendarInterval.values())
                .filter(e -> e.name().equalsIgnoreCase(interval))
                .findFirst();
        return first.orElse(CalendarInterval.Month);
    }

    private List<PluginCommentScoreIntervalStatsDto.TimeIntervalScore> toIntervalScoreStatsDto(AggregationsContainer<?> aggregations) {

        return Optional.ofNullable(aggregations)
                .map(e -> (ElasticsearchAggregations) e)
                .map(e -> e.get("interval_comment_scores"))
                .map(e -> e.aggregation().getAggregate())
                .map(e -> e.dateHistogram().buckets().array())
                .map(bucketList -> bucketList
                        .stream()
                        .map(bucket -> {

                            PluginCommentScoreIntervalStatsDto.TimeIntervalScore timeIntervalScore = new PluginCommentScoreIntervalStatsDto.TimeIntervalScore();
                            timeIntervalScore.setIntervalStart(bucket.keyAsString());
                            timeIntervalScore.setCommentCount(bucket.docCount());
                            Double avgScore = Optional.ofNullable(bucket.aggregations())
                                    .map(avgAggMap -> avgAggMap.get("avg_score"))
                                    .map(avgAgg -> avgAgg.avg().value())
                                    .orElse(0D);
                            timeIntervalScore.setAvgScore(
                                    avgScore
                            );
                            return timeIntervalScore;
                        }).toList()
                )
                .orElse(new ArrayList<>());
    }

    private List<PluginCommentCountIntervalStatsDto.TimeIntervalStats> toTimeIntervalStatsDto(AggregationsContainer<?> aggregations) {
        return Optional.ofNullable(aggregations)
                .map(e -> (ElasticsearchAggregations) e)
                .map(e -> e.get("interval_comment_counts"))
                .map(e -> e.aggregation().getAggregate())
                .map(e -> e.dateHistogram().buckets().array())
                .map(bucketList -> bucketList
                        .stream()
                        .map(bucket -> {

                            long negativeCommentCount, positiveCommentCount = 0;
                            Map<String, RangeBucket> countBucketMap = Optional.ofNullable(bucket.aggregations())
                                    .map(v -> v.get("count_ranges"))
                                    .map(v -> v.range().buckets().array())
                                    .map(v -> v.stream()
                                            .collect(Collectors.toMap(RangeBucket::key, Function.identity()))
                                    )
                                    .orElse(new HashMap<>(1));


                            PluginCommentCountIntervalStatsDto.TimeIntervalStats timeIntervalStats = new PluginCommentCountIntervalStatsDto.TimeIntervalStats();
                            timeIntervalStats.setIntervalStart(bucket.keyAsString());

                            negativeCommentCount = Optional.ofNullable(countBucketMap.get("negative_comment"))
                                    .map(MultiBucketBase::docCount).orElse(0L);

                            positiveCommentCount = Optional.ofNullable(countBucketMap.get("positive_comment"))
                                    .map(MultiBucketBase::docCount).orElse(0L);

                            timeIntervalStats.setNegativeCommentCount(negativeCommentCount);
                            timeIntervalStats.setPositiveCommentCount(positiveCommentCount);

                            return timeIntervalStats;
                        }).toList()

                ).orElse(new ArrayList<>(1));
    }


    private <T extends PluginCommentSearchParams> void buildBaseQueryCondition(Query.Builder queryBuilder, T params) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 日期范围过滤
        Optional.ofNullable(params.getStartDateTime())
                .filter(start -> Objects.nonNull(params.getEndDateTime()))
                .ifPresent(start -> boolQueryBuilder.filter(
                        Querys.range("comment_date", start, params.getEndDateTime())
                ));

        // 渠道过滤
        Optional.ofNullable(params.getChannels())
                .filter(CollUtil::isNotEmpty)
                .ifPresent(channels -> boolQueryBuilder.filter(
                        Querys.terms("comment_channel", channels)
                ));

        // 酒店编码过滤
        Optional.ofNullable(params.getHotelCode())
                .filter(StrUtil::isNotBlank)
                .ifPresent(code -> boolQueryBuilder.filter(
                        Querys.term("hotel_code", code)
                ));

        queryBuilder.bool(boolQueryBuilder.build());
    }

    /**
     * 聚合  可以直接拿这个用
     */
    private <R> Mono<R> aggregation(Consumer<Query.Builder> queryConsumer, Map<String, Aggregation> aggregationsMap, Function<AggregationsContainer<?>, R> resultMapper) {


        Query.Builder builder = new Query.Builder();
        Optional.ofNullable(queryConsumer)
                .ifPresent(c -> c.accept(builder));

        // 构建查询
        NativeQueryBuilder queryBuilder = NativeQuery.builder()
                .withQuery(builder.build())
                .withMaxResults(0); // size: 100

        // 添加所有聚合
        aggregationsMap.forEach(queryBuilder::withAggregation);


        NativeQuery nativeQuery = queryBuilder.build();

        // 执行搜索
        return reactiveElasticsearchOperations.searchForHits(
                        nativeQuery,
                        PluginCommentIndex.class)
                .map(searchHits -> {
                    AggregationsContainer<?> aggregations = searchHits.getAggregations();
                    return resultMapper.apply(aggregations);
                });
    }

    public Mono<List<Pair<String, Long>>> getLabelsWithSentiment(PluginCommentSearchParams searchParams, String sentiment) {
        Map<String, Aggregation> aggregationsMap = new HashMap<>();

        // 创建嵌套路径聚合 - label_analysis
        Aggregation nestedLabelAnalysisAgg = Aggregation.of(nestedBuilder -> nestedBuilder
                .nested(n -> n.path("label_analysis"))
                .aggregations("sentiment_filter", filterAgg -> filterAgg
                        .filter(f -> f.term(t -> t.field("label_analysis.sentiment").value(sentiment)))
                        .aggregations("nested_tags", nestedTagsAgg -> nestedTagsAgg
                                .nested(n -> n.path("label_analysis.labels"))
                                .aggregations("level_2_filter", levelFilterAgg -> levelFilterAgg
                                        .filter(f -> f.term(t -> t.field("label_analysis.labels.level").value(4)))
                                        .aggregations("tag_names", tagNamesAgg -> tagNamesAgg
                                                .terms(t -> t
                                                        .field("label_analysis.labels.name")
                                                        .order(List.of(NamedValue.of("_count", SortOrder.Desc)))
                                                )
                                        )
                                )
                        )
                )
        );

        // 添加聚合到主Map
        aggregationsMap.put("level_2_tags", nestedLabelAnalysisAgg);

        BoolQuery.Builder conditionBoolQueryBuilder = new BoolQuery.Builder();
        buildContentQuery(conditionBoolQueryBuilder, searchParams);
        Query conditionQuery = conditionBoolQueryBuilder.build()._toQuery();
        // 构建查询
        NativeQueryBuilder queryBuilder = NativeQuery.builder()
                .withQuery(conditionQuery)
                .withMaxResults(0); // size: 100

        // 添加所有聚合
        for (Map.Entry<String, Aggregation> entry : aggregationsMap.entrySet()) {
            queryBuilder.withAggregation(entry.getKey(), entry.getValue());
        }

        NativeQuery nativeQuery = queryBuilder.build();

        // 执行搜索
        return reactiveElasticsearchOperations.searchForHits(
                        nativeQuery,
                        PluginCommentIndex.class)
                .map(searchHits -> {
                    AggregationsContainer<?> aggregations = searchHits.getAggregations();
                    return getLevel3TagsDistribution(aggregations);
                });
    }

    private List<Pair<String, Long>> getLevel3TagsDistribution(AggregationsContainer<?> aggregations) {
        List<Pair<String, Long>> resultList = new ArrayList<>();

        if (aggregations != null) {
            try {
                // 获取level_2_tags嵌套聚合
                ElasticsearchAggregation level2TagsAgg = ((ElasticsearchAggregations) aggregations).get("level_2_tags");
                if (level2TagsAgg != null) {
                    // 获取level_2_tags嵌套聚合内的子聚合
                    Map<String, Aggregate> level2TagsAggregates = level2TagsAgg.aggregation()
                            .getAggregate().nested().aggregations();

                    // 获取sentiment_filter聚合
                    Aggregate sentimentFilterAgg = level2TagsAggregates.get("sentiment_filter");
                    if (sentimentFilterAgg != null) {
                        // 获取sentiment_filter聚合内的子聚合
                        Map<String, Aggregate> sentimentFilterAggregates = sentimentFilterAgg.filter().aggregations();

                        // 获取nested_tags嵌套聚合
                        Aggregate nestedTagsAgg = sentimentFilterAggregates.get("nested_tags");
                        if (nestedTagsAgg != null) {
                            // 获取nested_tags嵌套聚合内的子聚合
                            Map<String, Aggregate> nestedTagsAggregates = nestedTagsAgg.nested().aggregations();

                            // 获取level_2_filter过滤聚合
                            Aggregate level2FilterAgg = nestedTagsAggregates.get("level_2_filter");
                            if (level2FilterAgg != null) {
                                // 获取level_2_filter过滤聚合内的子聚合
                                Map<String, Aggregate> level2FilterAggregates = level2FilterAgg.filter().aggregations();

                                // 获取tag_names词条聚合
                                Aggregate tagNamesAgg = level2FilterAggregates.get("tag_names");
                                if (tagNamesAgg != null) {
                                    // 处理tag_names词条聚合结果
                                    var buckets = tagNamesAgg.sterms().buckets().array();
                                    for (var bucket : buckets) {
                                        resultList.add(Pair.of(bucket.key().stringValue(), bucket.docCount()));
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理level3标签聚合结果时出错: {}", e.getMessage());
            }
        }

        return resultList;
    }

    public Mono<List<PluginCommentIndex>> getCommentsByLabels(PluginCommentSearchParams params, String topLabel) {
        Query hotelCodeQuery = TermQuery.of(tq -> tq
                .field(PluginCommentIndexField.HOTEL_CODE.getColumn())
                .value(params.getHotelCode())
        )._toQuery();

        Query commentDateQuery = DateRangeQuery.of(drq -> drq
                .field(PluginCommentIndexField.COMMENT_DATE.getColumn())
                .gte(params.getStartDateTime())
                .lte(params.getEndDateTime())
                .format(DatePattern.NORM_DATETIME_PATTERN) // 一般为 yyyy-MM-dd
        )._toRangeQuery()._toQuery();

        Query levelQuery = TermQuery.of(tq -> tq
                .field("label_analysis.labels.level")
                .value(4)
        )._toQuery();

        Query labelNameQuery = TermQuery.of(tq -> tq
                .field("label_analysis.labels.name")
                .value(topLabel) // 例如 "干净整洁"
        )._toQuery();

        Query nestedLabelsQuery = NestedQuery.of(nq -> nq
                .path("label_analysis.labels")
                .query(qb -> qb
                        .bool(b -> b
                                .must(levelQuery)
                                .must(labelNameQuery)
                        )
                )
        )._toQuery();

        Query nestedLabelAnalysisQuery = NestedQuery.of(nq -> nq
                .path("label_analysis")
                .query(qb -> qb.bool(b -> b.must(nestedLabelsQuery)))
        )._toQuery();

        Query boolQuery = BoolQuery.of(b -> b
                .must(hotelCodeQuery)
                .must(commentDateQuery)
                .must(nestedLabelAnalysisQuery)
        )._toQuery();

        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(boolQuery)
                .withSort(sort -> sort.field(f -> f
                        .field(PluginCommentIndexField.COMMENT_DATE.getColumn())
                        .order(SortOrder.Desc)
                ))
                .build();

        return reactiveElasticsearchOperations.search(nativeQuery, PluginCommentIndex.class)
                .map(SearchHit::getContent)
                .collectList();
    }

    private String getTopLabel(Map<String, Long> labelMap) {
        return labelMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    public Mono<List<PluginCommentIndex>> negativeReviewDetail(PluginSimpleCommentSearchParams params) {
        // 构建酒店代码查询
        Query hotelCodeQuery = TermQuery.of(tq -> tq
                .field(PluginCommentIndexField.HOTEL_CODE.getColumn())
                .value(params.getHotelCode())
        )._toQuery();

        // 构建评分查询
        Query scoreQuery = NumberRangeQuery.of(nrq -> nrq
                .field(PluginCommentIndexField.COMMENT_SCORE.getColumn())
                .lt(negativeReviewsScore)
        )._toRangeQuery()._toQuery();

        // 构建日期范围查询
        Query dateQuery = DateRangeQuery.of(drq -> drq
                .field(PluginCommentIndexField.COMMENT_DATE.getColumn())
                .gte(params.getStartDate())
                .lte(params.getEndDate())
                .format(DatePattern.NORM_DATETIME_PATTERN)
        )._toRangeQuery()._toQuery();

        // 组合查询条件
        Query boolQuery = BoolQuery.of(b -> b
                .must(hotelCodeQuery)
                .must(scoreQuery)
                .must(dateQuery)
        )._toQuery();

        // 创建NativeQuery，添加查询
        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(boolQuery)
                .withSort(sort -> sort.field(f -> f.field(PluginCommentIndexField.COMMENT_DATE.getColumn()).order(SortOrder.Desc)))
                .withPageable(PageRequest.of(0, params.getTopN()))
                .build();

        // 执行查询并返回结果流
        return reactiveElasticsearchOperations.search(nativeQuery, PluginCommentIndex.class)
                .map(SearchHit::getContent).collectList();
    }
}
