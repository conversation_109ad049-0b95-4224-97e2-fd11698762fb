package com.wormhole.hotelds.plugin.model.dto;

import co.elastic.clients.elasticsearch._types.aggregations.RangeBucket;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-05-13 10:58:08
 * @Description: es bucket range分桶 的dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RangeBucketDto {

    private String key;

    private Double to;

    private Double from;

    private Long count;

    public RangeBucketDto(RangeBucket rangeBucket) {
        this.key = rangeBucket.key();
        this.to = rangeBucket.to();
        this.from = rangeBucket.from();
        this.count = rangeBucket.docCount();
    }

    public RangeBucketDto(String key, Double from, Double to) {
        this.key = key;
        this.to = to;
        this.from = from;
    }
}
