package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompetitorRoomPriceDTO implements Serializable {
    private String competitorHotelId;
    private String competitorHotelName;
    private List<RoomPriceInfo> priceRecords;



    @Data
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class RoomPriceInfo {
        private String date;
        private Long currentPrice;
        private Long previousPrice;
        private LocalDateTime lastCollectionDatetime;
        private Boolean needRecollection;
        private Boolean needShow;

    }
}
