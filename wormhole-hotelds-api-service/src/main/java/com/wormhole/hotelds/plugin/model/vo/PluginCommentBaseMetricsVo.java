package com.wormhole.hotelds.plugin.model.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.hotelds.plugin.model.dto.RangeBucketDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-13 13:50:54
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginCommentBaseMetricsVo {

    private String startDate;

    private String endDate;

    private String hotelCode;

    private Metrics metrics;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Metrics {
        private UserMetrics userMetrics;

        private HotelMetrics hotelMetrics;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class UserMetrics {
        /**
         * 总评论数
         */
        private Long totalCommentCount;

        private String goodCommentRate;

        private String middleCommentRate;

        private String badCommentRate;

        /**
         * 平均分
         */
        private Double avgCommentScore;

        private List<RangeBucketDto> commentLengthRange;

        private List<RangeBucketDto> timeToCommentRange;


        private Double avgEnvironmentScore;

        private String goodEnvironmentRate;

        private String middleEnvironmentRate;

        private String badEnvironmentRate;


        private Double avgFacilitiesScore;

        private String goodFacilitiesRate;

        private String middleFacilitiesRate;

        private String badFacilitiesRate;


        private Double avgHygieneScore;


        private String goodHygieneRate;

        private String middleHygieneRate;

        private String badHygieneRate;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class HotelMetrics {

        private Long totalReplyCount;

        /**
         * 差评回复率
         */
        private String badCommentReplyRate;


        private Double avgReplyRelevance;

        private Double avgReplyLength;

        /**
         * 逾期未回复数
         */
        private Long overdueNotReplyCount;

        /**
         * 逾期回复数(有回复，但是逾期回复)
         */
        private Long overdueReplyCount;

    }


}
