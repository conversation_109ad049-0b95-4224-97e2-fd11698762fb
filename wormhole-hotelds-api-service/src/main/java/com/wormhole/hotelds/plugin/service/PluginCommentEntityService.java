package com.wormhole.hotelds.plugin.service;

import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.task.model.entity.CommentEntity;
import com.wormhole.task.model.entity.filed.CommentField;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Service
@Slf4j
public class PluginCommentEntityService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    /**
     * 根据id查询评论
     * */
    public Mono<CommentEntity> findById(String commentId) {
        Criteria criteria = Criteria.where(CommentField.ID.getColumn())
                .is(commentId)
                .and(CommentField.ROW_STATUS.getColumn())
                .is(RowStatusEnum.VALID.getId());
        return r2dbcEntityTemplate.select(Query.query(criteria), CommentEntity.class)
                .next();
    }
}
