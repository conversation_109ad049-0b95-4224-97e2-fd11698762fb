package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.model.dto.PluginOrderStatsDto;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsIntervalEnum;
import com.wormhole.hotelds.plugin.model.enums.PluginCommentStatisticsRecordStateEnum;
import com.wormhole.hotelds.plugin.model.param.PluginCommentReportParams;
import com.wormhole.hotelds.plugin.model.param.PluginCommentSearchParams;
import com.wormhole.hotelds.plugin.model.param.PluginSimpleCommentSearchParams;
import com.wormhole.hotelds.plugin.model.qo.PluginOrderQo;
import com.wormhole.hotelds.plugin.model.vo.PluginCommentStatisticsMonthReportVo;
import com.wormhole.hotelds.plugin.model.vo.PluginOrderTodayReportVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Service
@Slf4j
public class PluginOrderStatisticsService {
    @Resource
    private PluginOrderIndexService pluginOrderIndexService;

    public Mono<PluginOrderTodayReportVO> searchTodayReportData(PluginOrderQo param) {
        PluginOrderTodayReportVO reportVo = new PluginOrderTodayReportVO();
        // 查询今日和昨日数据并处理
        return assembleOrderStats(buildSearchParams(param, true), reportVo, true)
                .flatMap(result -> assembleOrderStats(buildSearchParams(param, false), result, false))
                .doOnError(error -> log.error("获取今日报表数据失败", error));
    }

    // 构建查询参数（今日或昨日通用）
    private PluginOrderQo buildSearchParams(PluginOrderQo param, boolean isToday) {
        PluginOrderQo searchParams = new PluginOrderQo();
        searchParams.setHotelCode(param.getHotelCode());
        searchParams.setPlatform(param.getPlatform());
        searchParams.setChannel(param.getChannel());
        searchParams.setOrderStatus(param.getOrderStatus());
        // 根据 isToday 构造不同的时间范围
        LocalDate targetDate = isToday ? LocalDate.now() : LocalDate.now().minusDays(1);
        searchParams.setStartDateTime(targetDate.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        searchParams.setEndDateTime(targetDate.atTime(23, 59, 59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return searchParams;
    }

    // 通用订单数据汇总方法（今日/昨日数据）
    private Mono<PluginOrderTodayReportVO> assembleOrderStats(PluginOrderQo params, PluginOrderTodayReportVO reportVo, boolean isToday) {
        Mono<PluginOrderStatsDto> statsMono = pluginOrderIndexService.getOrderStatsByPlatform(params);
        return statsMono.map(statsDto -> {
            if (isToday) {
                // 设置今日数据
                reportVo.setTodayNewOrders(statsDto.getTotalNewOrders());
                reportVo.setTodayTotalRevenue(statsDto.getTotalRevenue());
                reportVo.setTodayTotalOrders(statsDto.getTotalOrders());
                reportVo.setTodayCancelledOrders(statsDto.getCancelledOrders());
                reportVo.setTodayPlatformStats(statsDto.getPlatformStats());
            } else {
                // 设置昨日数据用于对比
                reportVo.setYesterdayTotalRevenue(statsDto.getTotalRevenue());
                reportVo.setYesterdayTotalOrders(statsDto.getTotalOrders());
                reportVo.setYesterdayCancelledOrders(statsDto.getCancelledOrders());
                reportVo.setYesterdayPlatformStats(statsDto.getPlatformStats());
                // 计算环比
                calculateComparisonRates(reportVo);
            }
            return reportVo;
        });
    }

//    private void calculateComparisonRates(PluginOrderTodayReportVO reportVo) {
//        // 收入环比
//        if (reportVo.getYesterdayTotalRevenue() != null && reportVo.getYesterdayTotalRevenue().compareTo(BigDecimal.ZERO) > 0) {
//            BigDecimal revenueRate = reportVo.getTodayTotalRevenue()
//                    .subtract(reportVo.getYesterdayTotalRevenue())
//                    .divide(reportVo.getYesterdayTotalRevenue(), 4, RoundingMode.HALF_UP)
//                    .multiply(BigDecimal.valueOf(100));
//            reportVo.setRevenueComparisonRate(revenueRate);
//        }
//
//        // 取消率环比
//        if (reportVo.getYesterdayTotalOrders() != null &&reportVo.getYesterdayTotalOrders() > 0 && reportVo.getTodayTotalOrders() != null && reportVo.getTodayTotalOrders() > 0) {
//            double todayCancelRate = (double) reportVo.getTodayCancelledOrders() / reportVo.getTodayTotalOrders() * 100;
//            double yesterdayCancelRate = (double) reportVo.getYesterdayCancelledOrders() / reportVo.getYesterdayTotalOrders() * 100;
//
//            if (yesterdayCancelRate > 0) {
//                double cancelRateComparison = (todayCancelRate - yesterdayCancelRate) / yesterdayCancelRate * 100;
//                reportVo.setCancelRateComparisonRate(BigDecimal.valueOf(cancelRateComparison));
//            }
//        }
//    }

    private void calculateComparisonRates(PluginOrderTodayReportVO reportVo) {
        // 收入环比计算
        BigDecimal todayRevenue = reportVo.getTodayTotalRevenue();
        BigDecimal yesterdayRevenue = reportVo.getYesterdayTotalRevenue();

        if (todayRevenue != null && yesterdayRevenue != null && yesterdayRevenue.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal revenueDiff = todayRevenue.subtract(yesterdayRevenue);
            BigDecimal revenueRate = revenueDiff
                    .divide(yesterdayRevenue, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            reportVo.setRevenueComparisonRate(revenueRate);
        } else {
            // 设置为 null 明确表示无法计算
            reportVo.setRevenueComparisonRate(null);
        }

        // 取消率环比计算
        Long todayOrders = reportVo.getTodayTotalOrders();
        Long yesterdayOrders = reportVo.getYesterdayTotalOrders();
        Long todayCancelled = reportVo.getTodayCancelledOrders();
        Long yesterdayCancelled = reportVo.getYesterdayCancelledOrders();

        if (todayOrders != null && todayOrders > 0 &&
                yesterdayOrders != null && yesterdayOrders > 0 &&
                todayCancelled != null && yesterdayCancelled != null) {

            double todayCancelRate = (double) todayCancelled / todayOrders * 100;
            double yesterdayCancelRate = (double) yesterdayCancelled / yesterdayOrders * 100;

            if (yesterdayCancelRate > 0) {
                double cancelRateComparison = (todayCancelRate - yesterdayCancelRate) / yesterdayCancelRate * 100;
                reportVo.setCancelRateComparisonRate(BigDecimal.valueOf(cancelRateComparison));
            } else {
                reportVo.setCancelRateComparisonRate(null);
            }
        } else {
            // 设置为 null 明确表示无法计算
            reportVo.setCancelRateComparisonRate(null);
        }
    }


}
