package com.wormhole.hotelds.plugin.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.agent.client.chat.response.vo.WorkflowNodeSimpleVO;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.plugin.config.CommonProperties;
import com.wormhole.hotelds.plugin.util.WebClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class WorkflowExecutor {

    private final CommonProperties commonProperties;

    private static final String API_TEMPLATE = "%s/debug/workflow";

    public WorkflowExecutor(CommonProperties commonProperties) {
        this.commonProperties = commonProperties;
    }

    /**
     * 执行一个工作流并返回其完整的执行节点列表。
     *
     * @param workflowCode 要执行的工作流的唯一标识码。
     * @param initialInput 工作流所需的初始输入 Map。
     * @return 一个发出 WorkflowNodeSimpleVO 列表的 Mono，如果出错则发出错误信号。
     */
    public Mono<List<WorkflowNodeSimpleVO>> execute(String workflowCode, Map<String, Object> initialInput) {
        String url = String.format(API_TEMPLATE, commonProperties.getWormholeAgentUrl());

        // 工具类负责构建完整的请求体
        Map<String, Object> requestBodyMap = Map.of(
                "workflow_code", workflowCode,
                "is_debug", true,
                "initial_input", initialInput
        );

        String body = JacksonUtils.writeValueAsString(requestBodyMap);

        return WebClientUtils.sendPost(url, null, body, null, new TypeReference<List<WorkflowNodeSimpleVO>>() {})
                .onErrorResume(e -> {
                    log.error("调用工作流API失败。 URL: {}, WorkflowCode: {}, Error: {}",
                            url, workflowCode, e.getMessage(), e);
                    // 直接将错误往上层传递
                    return Mono.error(new RuntimeException("执行工作流 '" + workflowCode + "' 失败", e));
                });
    }
}