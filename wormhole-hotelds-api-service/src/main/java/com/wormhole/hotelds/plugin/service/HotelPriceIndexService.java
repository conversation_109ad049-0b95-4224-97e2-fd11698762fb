package com.wormhole.hotelds.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.text.CharSequenceUtil;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.util.NamedValue;
import com.google.common.collect.Lists;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.hotelds.plugin.model.dto.HotelPriceQueryDTO;
import com.wormhole.hotelds.plugin.model.entity.HotelCompetitorEntity;
import com.wormhole.hotelds.plugin.model.vo.HotelMinPriceVO;
import com.wormhole.hotelds.plugin.model.vo.RoomTypeMinPriceVO;
import com.wormhole.hotelds.plugin.repository.WpHotelCompetitorRepository;
import com.wormhole.task.model.constant.CancelTypeEnum;
import com.wormhole.task.model.entity.filed.HotelPriceIndexField;
import com.wormhole.task.model.entity.index.HotelPriceHistoryIndex;
import com.wormhole.task.model.entity.index.HotelPriceIndex;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2025/5/22 14:28
 */

@Service
@Slf4j
public class HotelPriceIndexService {

    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;

    @Resource
    private WpHotelCompetitorRepository wpHotelCompetitorRepository;


    public Mono<List<HotelMinPriceVO>> queryHotelPriceDetails(HotelPriceQueryDTO queryDTO) {
        return getCompetitorHotelCodes(queryDTO.getHotelCode(), queryDTO.getCompetitorHotelId())
                .flatMap(competitorCodes -> {
                    if (CollectionUtil.isEmpty(competitorCodes)) {
                        return Mono.just(Collections.emptyList());
                    }
                    return assembleHotelPriceDetails(queryDTO, competitorCodes);
                });
    }

    /**
     * 获取竞品酒店
     *
     * @param hotelCode
     * @return
     */
    private Mono<List<String>> getCompetitorHotelCodes(String hotelCode, String competitorHotelId) {
        return (CharSequenceUtil.isNotBlank(competitorHotelId)
                ? wpHotelCompetitorRepository.findByHotelCodeAndCompetitorHotelIdAndRowStatus(hotelCode, competitorHotelId, RowStatusEnum.VALID.getId())
                : wpHotelCompetitorRepository.findByHotelCodeAndRowStatus(hotelCode, RowStatusEnum.VALID.getId()))
                .map(HotelCompetitorEntity::getCompetitorHotelId)
                .collect(Collectors.toList())
                .defaultIfEmpty(Collections.emptyList());
    }

    /**
     * 组装酒店价格详情
     *
     * @param queryDTO
     * @param competitorHotelCodes
     * @return
     */
    private Mono<List<HotelMinPriceVO>> assembleHotelPriceDetails(HotelPriceQueryDTO queryDTO, List<String> competitorHotelCodes) {
        List<String> allHotelCodes = new ArrayList<>(competitorHotelCodes);
        allHotelCodes.add(queryDTO.getHotelCode());

        return Mono.zip(
                getMinHotelPrices(queryDTO.getHotelCode(), allHotelCodes, queryDTO.getPriceDate()),
                getHotelHistoryMinPrice(queryDTO.getHotelCode(), competitorHotelCodes, queryDTO.getPriceDate())
        ).map(tuple -> buildHotelPriceVOList(queryDTO.getHotelCode(), competitorHotelCodes, tuple.getT1(), tuple.getT2()));
    }

    /**
     * 构建酒店价格VO列表
     *
     * @param selfHotelCode    当前酒店代码
     * @param competitorCodes  竞品酒店代码列表
     * @param allCurrentPrices 当前所有酒店的最低价列表
     * @param historyPrices    历史价格列表
     * @return 构建好的酒店价格VO列表
     */
    private List<HotelMinPriceVO> buildHotelPriceVOList(String selfHotelCode,
                                                        List<String> competitorCodes,
                                                        List<HotelMinPriceVO> allCurrentPrices,
                                                        List<HotelMinPriceVO> historyPrices) {

        Map<String, HotelMinPriceVO> currentPriceMap = Optional.ofNullable(allCurrentPrices)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(HotelMinPriceVO::getHotelCode, vo -> vo));

        Map<String, HotelMinPriceVO> historyMap = Optional.ofNullable(historyPrices)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(HotelMinPriceVO::getHotelCode, vo -> vo));

        HotelMinPriceVO selfPrice = currentPriceMap.get(selfHotelCode);

        return competitorCodes.stream()
                .map(code -> {
                    HotelMinPriceVO current = Optional.ofNullable(currentPriceMap.get(code)).orElse(new HotelMinPriceVO());
                    current.setHotelCode(code);

                    // 设置历史价格
                    Optional.ofNullable(historyMap.get(code)).ifPresent(history -> {
                        current.setHistoryMinPrice(history.getHistoryMinPrice());
                        current.setHistoryCreatedAt(history.getHistoryCreatedAt());
                    });

                    // 设置本酒店当前价格
                    if (selfPrice != null) {
                        current.setSelfMinPrice(selfPrice.getMinPrice());
                    }

                    return current;
                })
                .collect(Collectors.toList());
    }


    /**
     * 查询每个酒店的最低价以及各房型的最低价
     */
    public Mono<List<HotelMinPriceVO>> getMinHotelPrices(String selfHotelCode, List<String> hotelCodes, String priceDate) {
        Map<String, Aggregation> aggregationMap = new HashMap<>();

        // 构建按酒店分组的聚合
        aggregationMap.put("by_hotel", Aggregation.of(a -> a
                .terms(t -> t
                        .field(HotelPriceIndexField.HOTEL_CODE.getColumn())
                        .size(hotelCodes.size())
                )
                .aggregations("min_price", Aggregation.of(aa -> aa
                        .min(m -> m.field(HotelPriceIndexField.PRICE.getColumn()))
                ))
                .aggregations("room_type_min_price", Aggregation.of(aa -> aa
                        .terms(tt -> tt
                                .field("room_type_name.keyword")
                                .size(100)
                        )
                        .aggregations("min_price", Aggregation.of(aaa -> aaa
                                .min(m -> m.field(HotelPriceIndexField.PRICE.getColumn()))
                        ))
                        .aggregations("lowest_price_doc", Aggregation.of(aaa -> aaa
                                .topHits(th -> th
                                        .size(1)
                                        .sort(s -> s.field(f -> f
                                                .field(HotelPriceIndexField.PRICE.getColumn())
                                                .order(SortOrder.Asc)
                                        ))
                                        .source(src -> src
                                                .filter(f ->
                                                        f.includes(Stream.of(HotelPriceIndexField.values()).map(HotelPriceIndexField::getColumn).toList())
                                                )
                                        )
                                )
                        ))
                ))
        ));

        return this.aggregation(
                builder -> buildHotelPriceQuery(builder, hotelCodes, priceDate, selfHotelCode),
                aggregationMap,
                this::mapToHotelMinPriceVO,
                HotelPriceIndex.class
        );
    }

    private List<HotelMinPriceVO> mapToHotelMinPriceVO(AggregationsContainer<?> aggregations) {
        ElasticsearchAggregations eAggs = (ElasticsearchAggregations) aggregations;
        return Optional.ofNullable(eAggs.get("by_hotel"))
                .map(e -> (StringTermsAggregate) e.aggregation().getAggregate()._get())
                .map(terms -> terms.buckets().array().stream().map(bucket -> {
                    Map<String, Aggregate> aggregateMap = bucket.aggregations();
                    HotelMinPriceVO hotelStats = new HotelMinPriceVO();
                    hotelStats.setHotelCode((String) bucket.key()._get());
                    Double hotelMinPrice = Optional.ofNullable(aggregateMap.get("min_price"))
                            .map(Aggregate::min)
                            .map(MinAggregate::value)
                            .orElse(null);
                    hotelStats.setMinPrice(hotelMinPrice != null ? hotelMinPrice.longValue() : null);

                    // 处理房型价格统计
                    StringTermsAggregate roomTypeMinPrice = (StringTermsAggregate) aggregateMap.get("room_type_min_price")._get();
                    List<RoomTypeMinPriceVO> roomTypePrices = roomTypeMinPrice.buckets().array().stream()
                            .map(roomBucket -> {
                                RoomTypeMinPriceVO roomStats = new RoomTypeMinPriceVO();
                                Map<String, Aggregate> roomAggregateMap = roomBucket.aggregations();
                                // 获取最低价格的文档
                                Optional.ofNullable(roomAggregateMap.get("lowest_price_doc"))
                                        .map(Aggregate::topHits)
                                        .map(topHits -> topHits.hits().hits())
                                        .filter(hits -> !hits.isEmpty())
                                        .map(hits -> hits.get(0).source())
                                        .map(source -> source.to(RoomTypeMinPriceVO.class))
                                        .ifPresent(priceVO -> {
                                            BeanUtils.copyProperties(priceVO, roomStats);
                                            if (CharSequenceUtil.isNotBlank(roomStats.getCreatedAt())) {
                                                Instant instant = Instant.parse(roomStats.getCreatedAt());
                                                LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                                                roomStats.setCreatedAt(localDateTime.format(DatePattern.NORM_DATETIME_FORMATTER));
                                            }
                                        });
                                // 获取房型最低价
                                Double roomMinPrice = Optional.ofNullable(roomAggregateMap.get("min_price"))
                                        .map(Aggregate::min)
                                        .map(MinAggregate::value)
                                        .orElse(null);
                                roomStats.setMinPrice(roomMinPrice != null ? roomMinPrice.longValue() : null);

                                Optional.ofNullable(roomStats.getCancelInfo())
                                        .ifPresent(cancelInfo -> roomStats.setCancelType(CancelTypeEnum.fromCancelInfo(roomStats.getCancelInfo()).getValue()));
                                return roomStats;
                            }).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(roomTypePrices)) {
                        hotelStats.setHotelName(roomTypePrices.get(0).getHotelName());
                        hotelStats.setMinPriceDate(roomTypePrices.get(0).getPriceDate());
                    }
                    hotelStats.setRoomTypeMinPrices(roomTypePrices);
                    return hotelStats;
                }).toList())
                .orElse(Collections.emptyList());
    }

    /**
     * 查询历史记录中每个酒店第2批job_id的最低价
     */
    public Mono<List<HotelMinPriceVO>> getHotelHistoryMinPrice(String selfHotelCode, List<String> hotelCodes, String priceDate) {
        Map<String, Aggregation> aggregationMap = new HashMap<>();
        // 构建按酒店分组的聚合
        aggregationMap.put("by_hotel", Aggregation.of(a -> a
                .terms(t -> t
                        .field(HotelPriceIndexField.HOTEL_CODE.getColumn())
                        .size(hotelCodes.size())
                )
                .aggregations("by_job", Aggregation.of(aa -> aa
                        .terms(tt -> tt
                                .field("job_id")
                                .size(2)
                                .order(Collections.singletonList(NamedValue.of("_key", SortOrder.Desc)))
                        )
                        .aggregations("min_price_doc", Aggregation.of(aaa -> aaa
                                .topHits(th -> th
                                        .size(1)
                                        .sort(s -> s.field(f -> f
                                                .field(HotelPriceIndexField.PRICE.getColumn())
                                                .order(SortOrder.Asc)
                                        ))
                                        .source(src -> src.filter(f ->
                                                        f.includes(Lists.newArrayList(HotelPriceIndexField.PRICE.getColumn(),
                                                                HotelPriceIndexField.CREATED_AT.getColumn()))
                                                )
                                        )
                                ))
                        ))
                ))
        );
        return this.aggregation(
                builder -> buildHotelPriceQuery(builder, hotelCodes, priceDate, selfHotelCode),
                aggregationMap,
                this::mapToHotelHistoryMinPriceVO,
                HotelPriceHistoryIndex.class
        );
    }

    private List<HotelMinPriceVO> mapToHotelHistoryMinPriceVO(AggregationsContainer<?> aggregations) {
        ElasticsearchAggregations eAggs = (ElasticsearchAggregations) aggregations;

        return Optional.ofNullable(eAggs.get("by_hotel"))
                .map(e -> (StringTermsAggregate) e.aggregation().getAggregate()._get())
                .map(terms -> terms.buckets().array().stream().map(bucket -> {
                            HotelMinPriceVO hotelStats = new HotelMinPriceVO();
                            hotelStats.setHotelCode((String) bucket.key()._get());

                            Aggregate byJob = bucket.aggregations().get("by_job");
                            if (!(byJob._get() instanceof LongTermsAggregate jobTermsAgg)) {
                                return null;
                            }
                            List<LongTermsBucket> jobBuckets = jobTermsAgg.buckets().array();
                            // 取第2个job_id的最低价
                            if (CollectionUtil.isEmpty(jobBuckets) || jobBuckets.size() < 2) {
                                return null;
                            }
                            jobBuckets.get(1).aggregations().get("min_price_doc")
                                    .topHits()
                                    .hits()
                                    .hits()
                                    .stream()
                                    .findFirst()
                                    .map(hit -> hit.source().to(HashMap.class))
                                    .ifPresent(map -> {
                                        hotelStats.setHistoryMinPrice(Long.valueOf((Integer) map.getOrDefault("price", 0)));
                                        Instant instant = Instant.parse(map.get("created_at").toString());
                                        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                                        hotelStats.setHistoryCreatedAt(localDateTime.format(DatePattern.NORM_DATETIME_FORMATTER));
                                    });
                            return hotelStats;
                        }).filter(Objects::nonNull)
                        .toList())
                .orElse(Collections.emptyList());
    }

    private void buildHotelPriceQuery(Query.Builder queryBuilder, List<String> hotelCodes, String priceDate, String selfHotelCode) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 酒店代码过滤
        Optional.ofNullable(hotelCodes)
                .filter(CollUtil::isNotEmpty)
                .ifPresent(codes -> boolQueryBuilder.filter(
                        Querys.terms(HotelPriceIndexField.HOTEL_CODE.getColumn(), codes)
                ));

        // 日期过滤
        Optional.ofNullable(priceDate)
                .ifPresent(date -> boolQueryBuilder.filter(
                        Querys.term(HotelPriceIndexField.PRICE_DATE.getColumn(), date)
                ));

        // 本酒店过滤
        Optional.ofNullable(selfHotelCode)
                .ifPresent(date -> boolQueryBuilder.filter(
                        Querys.term(HotelPriceIndexField.SELF_HOTEL_CODE.getColumn(), selfHotelCode)
                ));

        queryBuilder.bool(boolQueryBuilder.build());
    }

    private <R> Mono<R> aggregation(Consumer<Query.Builder> queryConsumer,
                                    Map<String, Aggregation> aggregationsMap,
                                    Function<AggregationsContainer<?>, R> resultMapper,
                                    Class<?> classes) {

        Query.Builder builder = new Query.Builder();
        queryConsumer.accept(builder);

        NativeQueryBuilder queryBuilder = NativeQuery.builder()
                .withQuery(builder.build())
                .withMaxResults(0);

        aggregationsMap.forEach(queryBuilder::withAggregation);

        NativeQuery query = queryBuilder.build();

        return reactiveElasticsearchOperations.searchForHits(query, classes)
                .map(response -> resultMapper.apply(response.getAggregations()));
    }


}
