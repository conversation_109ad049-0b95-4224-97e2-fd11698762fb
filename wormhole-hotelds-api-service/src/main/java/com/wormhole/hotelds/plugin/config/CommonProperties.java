package com.wormhole.hotelds.plugin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "wormhole.common")
public class CommonProperties {
    private Integer timeInterval=7;
    private String hotelAbbreviationWorkFlowCode = "workflow_8d375aa51046e2eaddeda070c70fa823";
    private String wormholeAgentUrl = "http://wormhole-agent.delonix.dev";
    private String outCallAnalyzeWorkFlowCode = "workflow_ff74e00ee9263490f806a62e00c75e88";
}