package com.wormhole.hotelds.plugin.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@Service
public class EntitySaveService {

    private final R2dbcEntityTemplate r2dbcEntityTemplate;

    public EntitySaveService(R2dbcEntityTemplate r2dbcEntityTemplate) {
        this.r2dbcEntityTemplate = r2dbcEntityTemplate;
    }

    public <T> Mono<T> saveEntity(T entity, Map<String, Object> queryFields) {
        if (entity == null) {
            return Mono.error(new IllegalArgumentException("Entity cannot be null"));
        }
        if (queryFields == null || queryFields.isEmpty()) {
            return Mono.error(new IllegalArgumentException("Query fields cannot be null or empty"));
        }

        try {
            // 1. 构建查询条件
            Criteria criteria = Criteria.empty();
            for (Map.Entry<String, Object> entry : queryFields.entrySet()) {
                criteria = criteria.and(entry.getKey()).is(entry.getValue());
            }

            Query query = Query.query(criteria);

            // 2. 查询并决定插入或更新
            return r2dbcEntityTemplate.select((Class<T>) entity.getClass())
                    .matching(query)
                    .one()
                    .flatMap(existing -> {
                        // 3. 更新已有数据
                        return r2dbcEntityTemplate.update(entity)
                                .doOnSuccess(updated -> log.info("Entity updated: {}", updated))
                                .doOnError(e -> log.error("Entity update failed: {}", e.getMessage()));
                    })
                    .switchIfEmpty(
                            // 4. 插入新数据
                            r2dbcEntityTemplate.insert(entity)
                                    .doOnSuccess(saved -> log.info("Entity created: {}", saved))
                                    .doOnError(e -> log.error("Entity insert failed: {}", e.getMessage()))
                    );
        } catch (Exception e) {
            log.error("SaveEntity error", e);
            return Mono.error(new RuntimeException("Failed to process entity", e));
        }
    }
}

