package com.wormhole.hotelds.plugin.model.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 差评列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginReportBadCommentInfoDto {
    /**
     * 差评数量
     */
    private Integer count;

    /**
     * 差评列表
     */
    private List<Comment> commentList;


    /**
     * 评论
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Comment {
        /**
         * 评论ID
         */
        private String commentId;

        /**
         * 用户名
         */
        private String username;

        /**
         * 评论内容
         */
        private String content;

        /**
         * 评分
         */
        private Float commentScore;

        /**
         * 评论日期
         */
        private String commentDate;

        /**
         * 标签列表
         */
        private List<CommentLabel> labels;

        /**
         * 房间类型
         */
        private String roomType;

        /**
         * 是否有回复
         */
        private Boolean hasReply;

        /**
         * 出行类型
         */
        private String travelType;

        /**
         * 酒店回复内容
         * */
        private String hotelReplyContent;

        /**
         * 回复日期
         * */
        private String replyDate;

        /**
         * 回复员工
         * */
        private String replyStaff;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CommentLabel {
        /**
         * 标签名称
         */
        private String name;

        /**
         * 情感（正面/负面）
         */
        private String sentiment;

    }
}